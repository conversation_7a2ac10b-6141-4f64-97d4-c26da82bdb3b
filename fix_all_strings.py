#!/usr/bin/env python3

import os
import re
import subprocess

def get_files_with_unterminated_strings():
    """Get all files with unterminated string literals from dart analyze"""
    try:
        result = subprocess.run(['dart', 'analyze', '--fatal-infos', '.'], 
                              capture_output=True, text=True, cwd='.')
        lines = result.stderr.split('\n')
        files = set()
        for line in lines:
            if 'Unterminated string literal' in line:
                # Extract file path from error line
                parts = line.split(' - ')
                if len(parts) >= 2:
                    file_path = parts[1].split(':')[0]
                    files.add(file_path)
        return list(files)
    except Exception as e:
        print(f"Error running dart analyze: {e}")
        return []

def fix_unterminated_strings_in_file(file_path):
    """Fix unterminated string literals in a single file"""
    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Pattern 1: Fix strings that end with a literal newline
        # Look for patterns like 'text\n' where the newline is literal
        content = re.sub(r"'([^']*)\n([^']*)'", r"'\1\\n\2'", content)
        
        # Pattern 2: Fix multi-line strings that are broken
        # Look for unterminated single quotes followed by newline
        lines = content.split('\n')
        fixed_lines = []
        
        for i, line in enumerate(lines):
            # Check if line has unterminated string
            if "'" in line:
                # Count single quotes (excluding escaped ones)
                quote_count = len(re.findall(r"(?<!\\)'", line))
                if quote_count % 2 == 1:  # Odd number means unterminated
                    # Check if next line continues the string
                    if i + 1 < len(lines):
                        next_line = lines[i + 1]
                        # If next line doesn't start with quote, add \n and close quote
                        if not next_line.strip().startswith("'"):
                            line = line + "\\n'"
                            # Remove the opening quote from next line if it exists
                            if "'" in next_line:
                                next_line = re.sub(r"^(\s*)'", r"\1", next_line)
                                lines[i + 1] = next_line
            
            fixed_lines.append(line)
        
        content = '\n'.join(fixed_lines)
        
        # Pattern 3: Fix specific patterns we've seen
        # Fix patterns like 'text\nmore text' -> 'text\\nmore text'
        content = re.sub(r"'([^']*)\n([^']*)'", r"'\1\\n\2'", content)
        
        # Only write if content changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Fixed: {file_path}")
            return True
        else:
            print(f"No changes needed: {file_path}")
            return False
            
    except Exception as e:
        print(f"Error fixing {file_path}: {e}")
        return False

def main():
    print("Finding files with unterminated string literals...")
    files = get_files_with_unterminated_strings()
    
    if not files:
        print("No files found with unterminated string literals.")
        return
    
    print(f"Found {len(files)} files with unterminated string literals:")
    for file_path in files:
        print(f"  {file_path}")
    
    print("\nFixing files...")
    fixed_count = 0
    for file_path in files:
        if fix_unterminated_strings_in_file(file_path):
            fixed_count += 1
    
    print(f"\nFixed {fixed_count} files.")
    print("Please run 'dart analyze' again to check for remaining issues.")

if __name__ == "__main__":
    main()
