#!/usr/bin/env python3

import os
import glob

def find_cumulative_position():
    """Find which file contains character positions 9047-9102 when files are processed in order"""
    
    # Get all dart files in the project
    dart_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.dart'):
                dart_files.append(os.path.join(root, file))
    
    # Sort files to match dart_eval processing order (alphabetical)
    dart_files.sort()
    
    cumulative_chars = 0
    target_start = 9047
    target_end = 9102
    
    for file_path in dart_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            file_size = len(content)
            file_start = cumulative_chars
            file_end = cumulative_chars + file_size
            
            # Check if target range falls within this file
            if (file_start <= target_start <= file_end) or (file_start <= target_end <= file_end):
                print(f"=== FOUND TARGET FILE ===")
                print(f"File: {file_path}")
                print(f"File size: {file_size} chars")
                print(f"File range: {file_start} - {file_end}")
                print(f"Target range: {target_start} - {target_end}")
                
                # Calculate position within file
                local_start = max(0, target_start - file_start)
                local_end = min(file_size, target_end - file_start)
                
                print(f"Local position in file: {local_start} - {local_end}")
                
                # Show content around the problematic area
                context_start = max(0, local_start - 100)
                context_end = min(file_size, local_end + 100)
                problematic_content = content[context_start:context_end]
                
                print(f"Content around position:")
                print(repr(problematic_content))
                print("=" * 50)
                
                # Look for doc directive patterns
                if '{@' in problematic_content:
                    print("Found {@ pattern in problematic content!")
                    
                return file_path, local_start, local_end
            
            cumulative_chars += file_size
            
        except Exception as e:
            print(f"Error reading {file_path}: {e}")
            continue
    
    print(f"Target position {target_start}-{target_end} not found in any file")
    print(f"Total cumulative characters: {cumulative_chars}")
    return None, None, None

if __name__ == "__main__":
    find_cumulative_position()
