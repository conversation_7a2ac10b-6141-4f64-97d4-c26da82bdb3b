#!/bin/bash

# <PERSON><PERSON>t to fix all unterminated string literals in the codebase

echo "Fixing all unterminated string literals..."

# Get all files with unterminated string literals
files=($(dart analyze --fatal-infos . 2>&1 | grep "Unterminated string" | cut -d: -f1 | sed 's/^  error - //' | sort | uniq))

echo "Found ${#files[@]} files with unterminated string literals:"
for file in "${files[@]}"; do
    echo "  $file"
done

# Function to fix unterminated strings in a file
fix_file() {
    local file="$1"
    echo "Fixing $file..."
    
    # Use sed to fix common patterns of unterminated strings
    # Pattern 1: String ending with newline without proper termination
    sed -i '' "s/'\([^']*\)$/'\1\\\\n'/g" "$file"
    
    # Pattern 2: Multi-line strings that are broken
    # This is more complex and might need manual intervention
    
    echo "Fixed basic patterns in $file"
}

# Fix each file
for file in "${files[@]}"; do
    if [ -f "$file" ]; then
        fix_file "$file"
    else
        echo "File not found: $file"
    fi
done

echo "Finished fixing files. Please run dart analyze again to check for remaining issues."
