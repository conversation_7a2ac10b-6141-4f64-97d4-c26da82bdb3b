import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_task/models/task/task.dart';
import 'package:gp_feat_task/models/task/task_repeat_option.dart';
import 'package:gp_feat_task/routes/router_name.dart';
import 'package:gp_feat_task/screens/date_time_picker/controller/date_time_picker_controller.dart';

class RepeatPickerWidget extends StatelessWidget {
  const RepeatPickerWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LocaleKeys.task_datetimePicker_repeat.tr,
            style: textStyle(GPTypography.headingSmall),
          ),
          const SizedBox(height: 8),
          GetBuilder<DateTimePickerController>(
            id: ConstantUpdateStateController.repeatOption,
            builder: (controller) => GPInputAction(
              displayName: controller.pickedTaskRepeatLabel,
              onTap: () => _showRepeatBottomSheet(controller),
              inputTextStyle: textStyle(GPTypography.bodyMedium),
              textHasFullWidth: true,
              maxLinesText: 1,
              tailingWidget: SvgWidget(
                'assets/images/ic20-fill-chevron-down.svg',
                color: GPColor.contentSecondary,
              ),
              prefixWidget: Padding(
                padding: const EdgeInsets.only(right: 12),
                child: SvgWidget(
                  'assets/images/ic20-line15-2arrow-rotate-ellipse.svg',
                  color: GPColor.contentSecondary,
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  void _showRepeatBottomSheet(DateTimePickerController controller) async {
    final result = await Popup.instance.showBottomSheet(
      _RepeatPickerBottomSheet(
        data: controller.repeatTask,
        checkedIndex: controller.pickedTaskRepeat?.index ?? 0,
        controller: controller,
      ),
    );
    if (result != null) {
      controller.setTaskRepeat(result);
    }
  }
}

class _RepeatPickerBottomSheet extends StatelessWidget {
  const _RepeatPickerBottomSheet({
    required this.data,
    required this.checkedIndex,
    required this.controller,
  });

  final List<BottomSheetModel> data;
  final int checkedIndex;
  final DateTimePickerController controller;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Text(LocaleKeys.task_datetimePicker_repeat.tr,
            style: textStyle(
              GPTypography.headingMedium,
            )).paddingSymmetric(vertical: 10),
        Divider(color: GPColor.bgSecondary, thickness: 1),
        ListView.builder(
            shrinkWrap: true,
            itemCount: data.length,
            itemBuilder: (context, index) {
              BottomSheetModel item = data[index];
              return GestureDetector(
                onTap: () async {
                  await _onItemTapped(item.value);
                },
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: Text(
                          item.displayName,
                          style: textStyle(GPTypography.bodyLarge)?.copyWith(
                              color: index == checkedIndex
                                  ? GPColor.functionAccentWorkSecondary
                                  : GPColor.functionAlwaysDarkPrimary,
                              fontWeight: index == checkedIndex
                                  ? FontWeight.bold
                                  : FontWeight.normal),
                        ),
                      ),
                      const SizedBox(width: 12),
                      if (index == checkedIndex)
                        SvgWidget(
                          'assets/images/svg/ic24-line20-checkmark-circle.svg',
                          color: GPColor.functionAccentWorkSecondary,
                        ),
                      if (item.value == TaskRepeatType.custom)
                        const SvgWidget(
                          'assets/images/svg/ic24-line15-chevron-right.svg',
                        )
                    ],
                  ),
                ),
              );
            }),
      ],
    );
  }

  _onItemTapped(TaskRepeatType type) async {
    if (type == TaskRepeatType.custom) {
      final rRule =
          controller.rRule.replaceAll("\n", ";").replaceAll("RRULE:", "");
      final result =
          await Get.toNamed(TaskRouterName.customRepeatScreen, arguments: {
        "rRule": rRule,
        "recurrence_settings": controller.recurrenceSettings,
        "startDate": controller.startTimeRRule,
      });
      Utils.back(result: result);
    } else if (type == TaskRepeatType.noRepeat) {
      Utils.back(result: {
        "rRule": "",
        "recurrence_settings":
            RecurrenceSettings(assignee: false, watcher: false, subtask: false)
      });
    }
  }
}
