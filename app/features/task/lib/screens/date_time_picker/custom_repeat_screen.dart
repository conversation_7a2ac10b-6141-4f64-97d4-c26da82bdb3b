import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_task/screens/date_time_picker/controller/custom_repeat_controller.dart';
import 'package:gp_feat_task/screens/date_time_picker/widget/custom_repeat_app_bar.dart';
import 'package:gp_feat_task/screens/date_time_picker/widget/month_days_widget.dart';
import 'package:gp_feat_task/screens/date_time_picker/widget/repeat_type_widget.dart';
import 'package:gp_feat_task/screens/date_time_picker/widget/week_days_widget.dart';

class CustomRepeatScreen extends GetView<RepeatEveryController> {
  const CustomRepeatScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Material(
      color: GPColor.bgPrimary,
      child: SafeArea(
        bottom: false,
        child: GetBuilder<CustomRepeatController>(
          builder: (customRepeatController) => Listener(
            behavior: HitTestBehavior.opaque,
            onPointerUp: (_) {
              GPKeyBoard.hide();
              customRepeatController.hideTooltip();
            },
            child: Scaffold(
              backgroundColor: GPColor.bgSecondary,
              appBar: const CustomRepeatAppBar(),
              body: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const RepeatTypeWidget(),
                    Obx(() => controller.rxRepeatEveryByModel.value ==
                            RepeatEveryByModel.week
                        ? const OnWeekDay()
                        : controller.rxRepeatEveryByModel.value ==
                                RepeatEveryByModel.month
                            ? OnMonthDay()
                            : const SizedBox()),
                    const Padding(
                      padding: EdgeInsets.symmetric(vertical: 8),
                      child: Column(
                        children: [
                          RepeatTime(),
                          CreateTimeAndDueDate(),
                        ],
                      ),
                    ),
                    const DuplicateOptionsWidget()
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class WeekDayItem extends StatelessWidget {
  const WeekDayItem({
    super.key,
    required this.text,
    this.isSelected = false,
    required this.onTap,
  });

  final String text;
  final bool isSelected;
  final Function() onTap;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: isSelected
                ? GPColor.functionAccentWorkSecondary
                : GPColor.bgSecondary),
        width: 40,
        height: 40,
        child: Align(
          alignment: Alignment.center,
          child: Text(
            text,
            style: textStyle(GPTypography.bodyLarge)?.copyWith(
                color: isSelected ? GPColor.bgPrimary : GPColor.contentPrimary,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400),
          ),
        ),
      ),
    );
  }
}

class DuplicateOptionsWidget extends GetView<CustomRepeatController> {
  const DuplicateOptionsWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: GPColor.bgPrimary,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
            child: Text(
              LocaleKeys.task_datetimePicker_duplicateTaskInclude.tr,
              style: textStyle(GPTypography.headingSmall),
            ),
          ),
          ListView.builder(
              shrinkWrap: true,
              padding: EdgeInsets.zero,
              itemCount: controller.duplicateOptions.length,
              itemBuilder: (context, index) {
                final item = controller.duplicateOptions[index];
                return _DuplicateOptionItem(
                  item: item,
                  controller: controller,
                );
              })
        ],
      ),
    );
  }
}

class _DuplicateOptionItem extends StatelessWidget {
  _DuplicateOptionItem({
    required this.item,
    required this.controller,
  }) {
    isSelected.value = item.isSelected;
  }

  final DuplicateOptionModel item;
  final CustomRepeatController controller;

  final RxBool isSelected = false.obs;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        _onChange(item);
      },
      child: Row(
        children: [
          ObxValue<RxBool>(
              (data) => GPCheckBox(
                    isSelected: isSelected.value,
                    onCheckedChanged: (_) {
                      _onChange(item);
                    },
                  ),
              isSelected),
          Expanded(
              child: Text(
            item.displayText,
            style: textStyle(GPTypography.bodyLarge)?.copyWith(height: 1),
          ))
        ],
      ),
    );
  }

  void _onChange(DuplicateOptionModel item) {
    isSelected.value = !isSelected.value;
    controller.onCheckDuplicateOption(item);
  }
}

class _DashedLinePainter extends CustomPainter {
  final Color color;

  _DashedLinePainter({
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    double dashWidth = 4, dashSpace = 4, startY = 0;
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.5;
    while (startY < size.height) {
      canvas.drawLine(Offset(0, startY), Offset(0, startY + dashWidth), paint);
      startY += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class RepeatTime extends GetView<CustomRepeatController> {
  const RepeatTime({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
      color: GPColor.bgPrimary,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 10),
            child: Text(
              LocaleKeys.task_datetimePicker_duplicateDate.tr,
              style: textStyle(GPTypography.headingSmall),
            ),
          ),
          GetBuilder<RepeatOverController>(
              init: RepeatOverController(
                  scrollController: controller.scrollController),
              builder: (overController) => Stack(
                    children: [
                      Column(
                        children: [
                          Row(
                            children: [
                              SvgWidget(
                                'assets/images/svg/ic16-fill-calendar.svg',
                                width: 16,
                                height: 16,
                                color: GPColor.blue,
                              ).paddingOnly(left: 2, right: 14.67),
                              Expanded(
                                  child: TaskDatePicker(
                                controller: controller.startDateTimeController,
                                onDateTimeSelected:
                                    controller.onStartDateChanged,
                                initialDate: controller.startDateTime.value,
                                dateFormat: 'dd/MM/yyyy',
                                pickerTitle:
                                    LocaleKeys.task_datetimePicker_pickDate.tr,
                              )),
                            ],
                          ).paddingOnly(bottom: 16),
                          Row(
                            children: [
                              SvgWidget(
                                'assets/images/svg/ic16-fill-calendar.svg',
                                width: 16,
                                height: 16,
                                color: GPColor.functionCriticalPrimary,
                              ).paddingOnly(left: 2, right: 14.67),
                              Expanded(
                                child: Obx(() => TaskDatePicker(
                                      controller:
                                          controller.endDateTimeController,
                                      onDateTimeSelected:
                                          controller.onEndDateChanged,
                                      initialDate: controller.endDateTime.value,
                                      dateFormat: 'dd/MM/yyyy',
                                      pickerTitle: LocaleKeys
                                          .task_datetimePicker_pickDate.tr,
                                      showIconX:
                                          controller.endDateTime.value != null,
                                      onTapIconX: controller.onTapIconXEndDate,
                                      allowPickFromDate:
                                          controller.startDateTime.value,
                                    )),
                              ),
                            ],
                          ).paddingOnly(bottom: 6),
                        ],
                      ),
                      const _DashedLineOverlay(),
                    ],
                  )),
        ],
      ),
    );
  }
}

class _DashedLineOverlay extends StatelessWidget {
  const _DashedLineOverlay();

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 30,
          alignment: Alignment.centerLeft,
        ),
        SizedBox(
          height: 35,
          child: CustomPaint(
            painter: _DashedLinePainter(
              color: GPColor.contentQuaternary,
            ),
          ),
        ).paddingOnly(left: 8 - 0.5),
      ],
    ).paddingOnly(left: 2);
  }
}

class CreateTimeAndDueDate extends GetView<CustomRepeatController> {
  const CreateTimeAndDueDate({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
      color: GPColor.bgPrimary,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(bottom: 10),
            child: Row(
              children: [
                Text(
                  LocaleKeys.task_datetimePicker_duplicateTime.tr,
                  style: textStyle(GPTypography.headingSmall),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 4),
                  child: InfoTooltip(controller: controller),
                )
              ],
            ),
          ),
          GetBuilder<RepeatOverController>(
              init: RepeatOverController(
                  scrollController: controller.scrollController),
              builder: (overController) => Stack(
                    children: [
                      Column(
                        children: [
                          Row(
                            children: [
                              const SvgWidget(
                                      'assets/images/svg/ic16-fill-clock-blue.svg',
                                      width: 16,
                                      height: 16)
                                  .paddingOnly(left: 2, right: 14.67),
                              Expanded(
                                  child: TaskTimePicker(
                                controller: controller.createTimeController,
                                onDateTimeSelected:
                                    controller.onCreateTimeChanged,
                                initialDate: controller.startDateTime.value,
                                pickerTitle: LocaleKeys
                                    .task_datetimePicker_timePickerTitle.tr,
                              )),
                            ],
                          ).paddingOnly(bottom: 16),
                          Row(
                            children: [
                              const SvgWidget(
                                      'assets/images/svg/ic16-fill-clock-orange.svg',
                                      width: 16,
                                      height: 16)
                                  .paddingOnly(left: 2, right: 14.67),
                              Expanded(
                                child: Obx(() => TaskTimePicker(
                                      controller:
                                          controller.dueDateTimeController,
                                      onDateTimeSelected:
                                          controller.onDueDateChanged,
                                      initialDate: controller.dueDateTime.value,
                                      pickerTitle: LocaleKeys
                                          .task_datetimePicker_timePickerTitle
                                          .tr,
                                      showIconX:
                                          controller.dueDateTime.value != null,
                                      onTapIconX: controller.onTapIconXDueDate,
                                    )),
                              ),
                            ],
                          ).paddingOnly(bottom: 6),
                        ],
                      ),
                      const _DashedLineOverlay(),
                    ],
                  )),
        ],
      ),
    );
  }
}

class InfoTooltip extends StatelessWidget {
  const InfoTooltip({
    super.key,
    required this.controller,
  });

  final CustomRepeatController controller;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: controller.showTooltip,
      child: SuperTooltip(
        controller: controller.tooltipController,
        showBarrier: false,
        hasShadow: false,
        popupDirection: TooltipDirection.up,
        backgroundColor: GPColor.bgInversePrimary,
        arrowBaseWidth: 12,
        arrowLength: 8,
        arrowTipDistance: 12,
        borderRadius: 4,
        content: Text(
          '• ${LocaleKeys.task_datetimePicker_duplicateCreateTime.tr} \n• ${LocaleKeys.task_datetimePicker_duplicateDueDate.tr}',
          style: textStyle(GPTypography.bodyMedium)
              ?.copyWith(color: GPColor.contentInversePrimary),
        ),
        child: Padding(
          padding: const EdgeInsets.all(4),
          child: SvgWidget(
            'assets/images/svg/ic16-line15-informationmark-circle.svg',
            color: GPColor.contentSecondary,
          ),
        ),
      ),
    );
  }
}
