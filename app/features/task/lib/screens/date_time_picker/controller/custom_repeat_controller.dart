import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/extensions/date_time_extension.dart';
import 'package:gp_core/shared_features/custom_repeat/rrule_mixin.dart';
import 'package:gp_core/widgets/datetime_picker/date_time_picker_mixin.dart';
import 'package:gp_feat_task/models/task/task.dart';

class CustomRepeatController extends BaseController with RRuleMixin {
  CustomRepeatController({GPConnection? gpConnection})
      : super(gpConnection ?? GPConnectionConcrete());
  final scrollController = AutoScrollController();
  Rx<DateTime> startDateTime = DateTime.now().obs;
  // ignore: unnecessary_cast
  Rx<DateTime?> endDateTime = (null as DateTime?).obs;
  Rx<DateTime?> dueDateTime = (null as DateTime?).obs;

  late DateTimePickerController startDateTimeController;
  late DateTimePickerController endDateTimeController;
  late DateTimePickerController createTimeController;
  late DateTimePickerController dueDateTimeController;

  /// Số lặp trong lựa chọn lặp lại mỗi ngày/tháng/năm
  int numRepeat = 1;

  List<DuplicateOptionModel> duplicateOptions = [];

  List<BottomSheetModel> repeatEveryType = [
    BottomSheetModel(
        displayName: RepeatEveryByModel.day.displayName,
        value: RepeatEveryByModel.day),
    BottomSheetModel(
        displayName: RepeatEveryByModel.week.displayName,
        value: RepeatEveryByModel.week),
    BottomSheetModel(
        displayName: RepeatEveryByModel.month.displayName,
        value: RepeatEveryByModel.month),
    BottomSheetModel(
        displayName: RepeatEveryByModel.year.displayName,
        value: RepeatEveryByModel.year),
  ];

  /// Week days repeat
  List<int> selectedWeekDays = [1];

  /// Month day repeat
  int? selectedMonthDay;

  /// Options duplicate task
  RecurrenceSettings recurrenceSettings = RecurrenceSettings();

  final repeatEveryController = RepeatEveryController();

  String? timeZone;

  final tooltipController = SuperTooltipController();

  @override
  void onInit() {
    super.onInit();
    startDateTimeController = DateTimePickerController(startDateTime.value);
    endDateTimeController = DateTimePickerController(endDateTime.value);
    createTimeController = DateTimePickerController(startDateTime.value);
    dueDateTimeController = DateTimePickerController(dueDateTime.value);

    if (Get.arguments is Map) {
      final rRule = Get.arguments["rRule"];
      final startDate = Get.arguments["startDate"];
      final recurrenceSettings = Get.arguments["recurrence_settings"];
      getTimeZone();
      onInitRRule(rRule, startDate);
      onInitRecurrenceSettings(recurrenceSettings);

      if (rRule.isEmpty) {
        initDefault();
      }
    }
  }

  @override
  String get rRule {
    final startTime = startDateTime.value.isoStr;
    // Trim "Z" character
    final startTimeWithoutZ = startTime.substring(0, startTime.length - 1);
    String repeatRule = Get.find<RepeatEveryController>().rRule;
    String overRule = Get.find<RepeatOverController>().rRule;

    return "DTSTART;TZID=$timeZone:$startTimeWithoutZ\nRRULE:$repeatRule$overRule";
  }

  get repeatType {
    return repeatEveryController.rxRepeatEveryByModel.value;
  }

  void getTimeZone() async {
    // timeZone = await FlutterNativeTimezone.getLocalTimezone();
    timeZone = await FlutterTimezone.getLocalTimezone();
  }

  void onInitRRule(String rRule, DateTime startDate) {
    final rRuleStr = rRule.isNotEmpty ? rRule : "FREQ=DAILY;INTERVAL=1";
    final rRuleModel = RRuleModel(rRuleStr, startDate: startDate);
    numRepeat = rRuleModel.intervalNumber;
    if (rRuleModel.startDate != null) {
      startDateTime.value = rRuleModel.startDate!;
    }
    if (rRuleModel.overOnADate != null) {
      onEndDateChanged(
          rRuleModel.overOnADate!.add(DateTime.now().timeZoneOffset));
    }
    if (rRuleModel.isDaily) {
      repeatEveryController.rxRepeatEveryByModel.value = RepeatEveryByModel.day;
    } else if (rRuleModel.isWeekly) {
      repeatEveryController.rxRepeatEveryByModel.value =
          RepeatEveryByModel.week;
    } else if (rRuleModel.isMonthly) {
      repeatEveryController.rxRepeatEveryByModel.value =
          RepeatEveryByModel.month;
    } else if (rRuleModel.isYearly) {
      repeatEveryController.rxRepeatEveryByModel.value =
          RepeatEveryByModel.year;
    }
    if (rRuleModel.hasByDay) {
      List<String> byDays = rRuleModel.byDay!.split(",");
      final days =
          byDays.map((e) => DateTimeUtils.weekDayStringToInt(e)).toList();
      selectedWeekDays.clear();
      selectedWeekDays.addAll(days);
    }
    if (rRuleModel.hasByMonthDay) {
      selectedMonthDay = int.parse(rRuleModel.byMonthDay ?? "0");
    }
  }

  void onInitRecurrenceSettings(RecurrenceSettings data) {
    recurrenceSettings = data;
    duplicateOptions = [
      DuplicateOptionModel(
          displayText: DuplicateOption.subtask.displayText,
          option: DuplicateOption.subtask,
          isSelected: recurrenceSettings.subtask ?? false),
      DuplicateOptionModel(
          displayText: DuplicateOption.assignee.displayText,
          option: DuplicateOption.assignee,
          isSelected: recurrenceSettings.assignee ?? false),
      DuplicateOptionModel(
          displayText: DuplicateOption.watcher.displayText,
          option: DuplicateOption.watcher,
          isSelected: recurrenceSettings.watcher ?? false),
    ];
    onDueDateChanged(data.dueDateDateTime);
  }

  void initDefault() {
    startDateTime.value = DateTime.now().applyTimeOfDay(hour: 8, minute: 0);
    final defaultEndDate = startDateTime.value.addMonth(1);
    onEndDateChanged(defaultEndDate);
    onDueDateChanged(DateTime.now().applyTimeOfDay(hour: 17, minute: 0));
  }

  String generateRRule() {
    return rRule;
  }

  void onDone() {
    final rRule = generateRRule();
    recurrenceSettings.dueDateTime = dueDateTime.value?.formatHHmm;
    Utils.back(result: {"rRule": rRule, "duplicate": ""});
  }

  void showRepeatTypeBottomSheet() {}

  void onCheckDuplicateOption(DuplicateOptionModel item) {
    switch (item.option) {
      case DuplicateOption.assignee:
        recurrenceSettings.assignee = !(recurrenceSettings.assignee ?? false);
        break;
      case DuplicateOption.subtask:
        recurrenceSettings.subtask = !(recurrenceSettings.subtask ?? false);
        break;
      case DuplicateOption.watcher:
        recurrenceSettings.watcher = !(recurrenceSettings.watcher ?? false);
        break;
    }
  }

  void onStartDateChanged(DateTime? dateTime) {
    startDateTime.value = dateTime ?? DateTime.now();
    startDateTimeController.value = dateTime ?? DateTime.now();
    // update end date if start date is larger than end date
    if (endDateTime.value != null &&
        endDateTime.value!.compareTo(startDateTime.value) < 0) {
      endDateTime.value = startDateTime.value.addMonth(1);
      endDateTimeController.value = endDateTime.value!;
    }
  }

  void onEndDateChanged(DateTime? dateTime) {
    endDateTime.value = dateTime;
    endDateTimeController.value = dateTime;
    if (Get.isRegistered<RepeatOverController>()) {
      final repeatOverController = Get.find<RepeatOverController>();
      repeatOverController.rxRepeatEnd.value = RepeatOverModel.onADay;
      repeatOverController.rxOnADayPickedDate.value =
          dateTime?.toUtc() ?? DateTime.now().toUtc();
    }
    // update start date if end date is smaller than start date
    if (endDateTime.value != null &&
        endDateTime.value!.compareTo(startDateTime.value) <= 0) {
      startDateTime.value = endDateTime.value!;
      startDateTimeController.value = startDateTime.value;
      createTimeController.value = startDateTime.value;
    }
    update([CustomRepeatConst.endDateTimePick]);
  }

  void onCreateTimeChanged(DateTime? dateTime) {
    startDateTime.value = dateTime ?? DateTime.now();
    startDateTimeController.value = dateTime;
  }

  void onDueDateChanged(DateTime? dateTime) {
    dueDateTime.value = dateTime;
    dueDateTimeController.value = dateTime;
  }

  void onTapIconXDueDate() {
    dueDateTime.value = null;
    dueDateTimeController.value = null;
  }

  Future showTooltip() async {
    await tooltipController.showTooltip();
  }

  Future hideTooltip() async {
    if (tooltipController.isVisible) {
      await tooltipController.hideTooltip();
    }
  }

  void setRepeatType(RepeatEveryByModel type) {
    repeatType.value = type;
  }

  void onSelectWeekDay(int day) {
    if (selectedWeekDays.contains(day) && selectedWeekDays.length > 1) {
      selectedWeekDays.remove(day);
    } else {
      selectedWeekDays.add(day);
    }
    update([CustomRepeatConst.weekDayItem]);
  }

  void onSelectMonthDay(int day) {
    selectedMonthDay = day;
    update([CustomRepeatConst.monthDayItem]);
  }

  void onTapIconXEndDate() {
    endDateTime.value = null;
    endDateTimeController.value = null;
    final repeatOverController = Get.find<RepeatOverController>();
    repeatOverController.rxRepeatEnd.value = RepeatOverModel.never;
  }
}

class CustomRepeatConst {
  static String duplicateOption = 'duplicateOption';
  static String weekDayItem = 'weekDayItem';
  static String monthDayItem = 'monthDayItem';
  static String endDateTimePick = 'endDateTimePick';
}

enum DuplicateOption { subtask, assignee, watcher }

extension DuplicateOptionExt on DuplicateOption {
  String get displayText {
    switch (this) {
      case DuplicateOption.subtask:
        return LocaleKeys.task_todo.tr;
      case DuplicateOption.assignee:
        return LocaleKeys.task_assignee_assignees.tr;
      case DuplicateOption.watcher:
        return LocaleKeys.task_datetimePicker_watcher.tr;
    }
  }
}

class DuplicateOptionModel {
  final String displayText;
  bool isSelected;
  final DuplicateOption option;

  DuplicateOptionModel(
      {required this.displayText,
      this.isSelected = false,
      required this.option});
}
