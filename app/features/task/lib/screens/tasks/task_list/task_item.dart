import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_task/models/task/task.dart';
import 'package:gp_feat_task/models/task/task_header_model.dart';
import 'package:gp_feat_task/models/task/user_role.dart';
import 'package:gp_feat_task/routes/router_name.dart';
import 'package:gp_feat_task/screens/components/list_sub_task/item_sub_task_widget.dart';
import 'package:gp_feat_task/screens/components/list_sub_task/list_sub_task.dart';
import 'package:gp_feat_task/screens/mini-task/folder/components/confirm_archive_popup.dart';
import 'package:gp_feat_task/screens/mini-task/general/components/controller/task_item_controller.dart';
import 'package:gp_feat_task/screens/mini-task/general/components/task_dependence.dart';
import 'package:gp_feat_task/screens/tasks/task_list/task_header_item.dart';
import 'package:gp_feat_task/screens/tasks/tasks_controller.dart';
import 'package:gp_feat_task/screens/tasks/tasks_screen.dart';
import 'package:gp_feat_task/utils/extentions/response_status_checker_ext.dart';
import 'package:gp_feat_task/utils/extentions/sub_task/get_data_assign_sub_task.dart';
import 'package:gp_feat_task/widgets/assignee_view.dart';
import 'package:gp_feat_task/widgets/checklist_view.dart';
import 'package:gp_feat_task/widgets/duedate_view.dart';
import 'package:gp_feat_task/widgets/priority_view.dart';
import 'package:gp_feat_task/widgets/tag_view.dart';

import 'task_list_controller.dart';

// ignore: must_be_immutable
class TaskItem extends StatelessWidget {
  TaskItem({
    super.key,
    required this.controller,
    required this.task,
    required this.tasksController,
  }) {
    rxCompleted.value = task.isCompleted;
  }

  final TaskListController controller;

  final TasksController tasksController;

  Task task;

  final RxBool rxCompleted = false.obs;

  Future openATask(Task task) async {
    final result =
        await Get.toNamed(TaskRouterName.taskCreate, arguments: task);

    if (result is List) {
      var action = result.first;
      if (action == TaskResultAction.refresh) {
        controller.tasksController.updateListByATask(task);

        if (controller.isFromSearch) {
          controller.tasksController.reloadSearch();
        }

        controller.taskGeneralControllerByTag()?.reload();
      }
    }

    controller.tasksController.updateListByATask(task);
  }

  bool get isNormalTaskList {
    return Get.isRegistered<TasksController>(tag: controller.parentTag);
  }

  @override
  Widget build(BuildContext context) {
    var assignees = task.getAssignees();

    return Material(
      color: GPColor.bgSecondary,
      child: InkWell(
        onTap: () => openATask(task),
        onLongPress: _onLongPress,
        child: Container(
            color: GPColor.contentInversePrimary,
            child: GetBuilder<TaskItemController>(
              assignId: true,
              init: TaskItemController()..initData(task),
              tag: task.id,
              builder: (taskItemController) {
                return ObxValue<RxBool>((data) {
                  if (taskItemController.taskValue != null && data.value) {
                    task = taskItemController.taskValue!;
                    taskItemController.updateTask.value = false;
                  }

                  return Column(
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Obx(() {
                            return InkWell(
                              onTap: () async {
                                if (task.isArchived) {
                                  //do nothing
                                } else {
                                  await controller.toggleStatusTask(task,
                                      onSuccess: () {
                                    rxCompleted.value = task.isCompleted;
                                  });
                                }
                              },
                              child: Ink(
                                color: rxCompleted.value
                                    ? Colors.white
                                    : Colors.white,
                                child: AnimatedContainer(
                                  margin: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 18),
                                  width: 20,
                                  height: 20,
                                  decoration: BoxDecoration(
                                    color: task.isArchived
                                        ? GPColor.lineTertiary.withAlpha(200)
                                        : task.taskStatus.bgCheckBoxColor(task),
                                    shape: BoxShape.circle,
                                    border: Border.all(
                                      width: 1.5,
                                      color: task.isArchived ||
                                              (!task.doneTaskDependency &&
                                                  rxCompleted.value)
                                          ? GPColor.lineTertiary
                                          : task.taskStatus.borderCheckBoxColor,
                                    ),
                                  ),
                                  duration: const Duration(milliseconds: 200),
                                  child: Center(
                                    child: SvgWidget(
                                      'assets/images/ic_check.svg',
                                      width: 11,
                                      color: task.isArchived
                                          ? task.taskStatus.isDone
                                              ? GPColor.contentQuaternary
                                              : Colors.transparent
                                          : Colors.white,
                                    ),
                                  ),
                                ),
                              ),
                            );
                          }),
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.fromLTRB(0, 16, 0, 16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  _headerTitle(),
                                  if (controller.isFromSearch &&
                                      task.isArchived == false)
                                    Column(
                                      children: [
                                        const SizedBox(height: 3),
                                        Text(
                                          task.projectName ??
                                              LocaleKeys
                                                  .taskGeneral_defaultProjectName
                                                  .tr,
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                          style:
                                              textStyle(GPTypography.bodySmall)
                                                  ?.mergeColor(
                                                      GPColor.contentSecondary),
                                        ),
                                        const SizedBox(height: 4),
                                      ],
                                    ),
                                  const SizedBox(height: 12),
                                  Row(
                                    children: [
                                      AssigneesView(
                                        users: assignees,
                                        userDisplay:
                                            assignees.length <= 2 ? 2 : 1,
                                      ),
                                      PriorityView(
                                        priority: (task.priority != null &&
                                                task.priority != 0)
                                            ? TaskPriority
                                                .values[task.priority!]
                                            : null,
                                      ),
                                      DeadlineView(
                                          task: task, isShowOnlyDate: true),
                                      Obx(() {
                                        Task taskValue = task;
                                        if (taskItemController
                                            .isUpdating.value) {
                                          taskValue =
                                              taskItemController.task?.value ??
                                                  task;
                                        }
                                        return CheckListView(
                                          task: taskValue,
                                        );
                                      }),
                                      TaskDependence(
                                        task: task,
                                        taskItemController: taskItemController,
                                      ),
                                    ],
                                  ),
                                  Obx(() {
                                    Task taskValue = task;
                                    if (taskItemController.isUpdating.value) {
                                      taskValue =
                                          taskItemController.task?.value ??
                                              task;
                                    }
                                    return TagView(task: taskValue);
                                  }),
                                ],
                              ),
                            ),
                          ),
                          Obx(() {
                            Task taskValue = task;
                            if (taskItemController.isUpdating.value) {
                              taskValue =
                                  taskItemController.task?.value ?? task;
                            }
                            return (taskValue.subTasks ?? []).isNotEmpty
                                ? GestureDetector(
                                    onTap: () {
                                      controller.toggleExpandSubTask(task.id);
                                    },
                                    child: SizedBox(
                                      width: 36,
                                      height: 52,
                                      child: Obx(() => RotatedBox(
                                            quarterTurns:
                                                controller.expandedSubTask[
                                                            task.id] ??
                                                        false
                                                    ? -6
                                                    : 0,
                                            child: Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 16,
                                                      horizontal: 8),
                                              child: SvgWidget(
                                                  "assets/images/ic20-fill-chevron-down.svg",
                                                  color:
                                                      GPColor.contentSecondary),
                                            ),
                                          )),
                                    ))
                                : const SizedBox(
                                    width: 36,
                                    height: 52,
                                  );
                          })
                        ],
                      ),
                      _subtask((List<Task>? listSubTask) {
                        taskItemController.setCountSubTask(listSubTask ?? []);
                      }),
                    ],
                  );
                }, taskItemController.updateTask);
              },
            )),
      ),
    );
  }

  _headerTitle() {
    return Obx((() {
      return TaskHeaderItem(
        task: task,
        complete: rxCompleted.value,
      );
    }));
  }

  Widget _subtask(Function(List<Task>? listSubTask)? functionListSubtask) {
    return Obx(() {
      bool showSubTask = false;
      if (((task.subTasks ?? []).isNotEmpty &&
          controller.expandedSubTask[task.id] == true)) {
        showSubTask = true;
      }
      if (controller.expandedSubTask[task.id] == false) {
        showSubTask = false;
      }

      return Expandable(
        collapsed: const SizedBox(),
        expanded: showSubTask
            ? ListSubTask(
                task: task,
                isShowNameProject: false,
                paddingRightItem: 0,
                tag: controller.tag,
                parentTag: controller.tasksController.parentTag,
                functionListSubtask: functionListSubtask,
                functionCallParent: (value) async {
                  if (value == TypeFunctionCallParent.reloadSection) {
                    await controller.getListItems();
                    controller.reload();
                    controller.handleCount();
                  }
                },
              )
            : const SizedBox(),
      );
    });
  }

  Future _onLongPress() async {
    Get.closeCurrentSnackbar();
    await Popup.instance.showBottomSheet(
      BottomSheetActionWidget(data: actions),
    );
  }

  List<BottomSheetActionModel> get actions {
    bool isArchived = task.isArchived;
    var list = [
      BottomSheetActionModel(
          displayName: LocaleKeys.task_longPress_view.tr,
          iconAsset: "assets/images/ic24-line15-feed.png",
          onClick: _openTaskBottomSheet),
      BottomSheetActionModel(
          displayName: LocaleKeys.task_longPress_copyUrl.tr,
          iconAsset: "assets/images/ic24-line15-link.png",
          onClick: _copyTaskLink),
    ];
    List<String> projectMemberIds = [];
    if (Get.isRegistered<TasksController>(tag: controller.parentTag)) {
      final tasksController =
          Get.find<TasksController>(tag: controller.parentTag);
      projectMemberIds = tasksController.projectMemberIds;
    }

    List<UserTaskRole> roles =
        controller.detectUserTaskRole(task, projectMemberIds: projectMemberIds);

    if (!task.isArchived) {
      list.add(BottomSheetActionModel(
          displayName: LocaleKeys.task_longPress_clone.tr,
          iconAsset: "assets/images/ic24-line15-2square.svg",
          onClick: _cloneTask));
    }

    if (roles.canEditRemoveTodoTask) {
      final archiveAction = BottomSheetActionModel(
          displayName: isArchived
              ? LocaleKeys.task_unarchive.tr
              : LocaleKeys.task_archive.tr,
          iconAsset: isArchived
              ? "assets/images/ic24-line15-archive-box-xmark.png"
              : "assets/images/ic24-line15-archive-box.png",
          onClick: archiveTaskDialog);
      list.add(archiveAction);

      if (task.projectId != null && (task.depth ?? 0) < 1) {
        final dependencyAction = BottomSheetActionModel(
            displayName: LocaleKeys.task_dependencies.tr,
            iconAsset: "assets/images/ic24-line15-dependency-3square.svg",
            onClick: _dependencyTask);
        list.add(dependencyAction);
      }

      if (isCreatorTask(task)) {
        final deleteAction = BottomSheetActionModel(
            displayName: LocaleKeys.task_longPress_deleteTask.tr,
            iconAsset: "assets/images/ic24-line15-trash.png",
            textColor: GPColor.functionNegativePrimary,
            iconColor: GPColor.functionNegativePrimary,
            onClick: _deleteTask);
        list.add(deleteAction);
      }
    }

    if (!isArchived && (task.depth ?? 0) > 0) {
      list.insert(
          list.length - 1,
          BottomSheetActionModel(
              displayName: LocaleKeys.task_changeSubTaskToTask.tr,
              iconAsset: "assets/images/ic24-line15-2arrow-rotate-circle.svg",
              onClick: () {
                _onClickChangeSubTask();
              }));
    }

    return list;
  }

  // ---------- BottomSheet commands --------- \
  void _openTaskBottomSheet() {
    _closeBottomSheet();
    openATask(task);
  }

  Future<void> _onClickChangeSubTask() async {
    _closeBottomSheet();
    await controller.changeSubTaskToTask(task);
    tasksController.reload();
  }

  void _copyTaskLink() {
    _closeBottomSheet();

    Clipboard.setData(ClipboardData(
            text: task.taskUrl ?? "${Constants.appDomain}/task/${task.id}"))
        .then((_) {
      Popup.instance.showSnackBar(
          message: LocaleKeys.task_longPress_copied.tr,
          type: SnackbarType.success);
    });
  }

  Future<void> _cloneTask() async {
    if (Get.isSnackbarOpen) {
      await Future.delayed(const Duration(milliseconds: 300));
    }
    _closeBottomSheet();
    controller.duplicateTask(
      task.id,
      (newTask) {
        controller.reload();
        openATask(newTask);
      },
      (error) {
        controller.handleError(error, StackTrace.fromString(error.toString()));
      },
    );
  }

  void _dependencyTask() async {
    if (Get.isSnackbarOpen) {
      await Future.delayed(const Duration(milliseconds: 300));
    }
    _closeBottomSheet();
    final data =
        await Get.toNamed(TaskRouterName.taskDependencyScreen, arguments: task);
    if (data != null) {
      tasksController.reload();
    }
  }

  void _deleteTask() async {
    if (Get.isSnackbarOpen) {
      await Future.delayed(const Duration(milliseconds: 300));
    }
    _closeBottomSheet();
    await Popup.instance.showBottomSheet(
      _ConfirmDeletingTaskDialog(
        task: task,
        taskHeaderModel: controller.taskHeaderModel,
        controller: controller,
        isTaskDependency: (task.waitingTasks?.length ?? 0) > 0 ||
            (task.blockingTasks?.length ?? 0) > 0,
      ),
    );
  }

  void _closeBottomSheet() {
    if (Get.isBottomSheetOpen ?? false) {
      Get.back();
    }
  }

  void archiveTaskDialog() async {
    if (Get.isSnackbarOpen) {
      await Future.delayed(const Duration(milliseconds: 300));
    }
    _closeBottomSheet();
    await Popup.instance.showBottomSheet(ConfirmArchiveDialog(
      data: task,
      isArchived: task.isArchived,
      onConfirmed: () async {
        _closeBottomSheet();
        controller.archiveTask(task);
      },
      onCancel: _closeBottomSheet,
    ));
  }
}

class _ConfirmDeletingTaskDialog extends StatelessWidget {
  const _ConfirmDeletingTaskDialog({
    required this.controller,
    required this.task,
    required this.taskHeaderModel,
    this.isTaskDependency = false,
  });

  final TaskHeaderModel taskHeaderModel;

  final Task task;
  final TaskListController controller;

  final bool isTaskDependency;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SvgWidget(
            isTaskDependency
                ? "assets/images/ic24-line15-dependency.svg"
                : "assets/images/ic24-line15-trash.png",
            width: 54,
            height: 59,
            color: GPColor.functionNegativePrimary,
          ),
          Text(
            isTaskDependency
                ? LocaleKeys.task_deleteDependencies.tr
                : "${LocaleKeys.task_confirmDelete_title.tr} \"${task.title}\"?",
            style: textStyle(GPTypography.headingLarge),
          ),
          const SizedBox(height: 8),
          Text(
            isTaskDependency
                ? LocaleKeys.task_contentDeleteTaskDependency.tr
                : LocaleKeys.task_longPress_confirmDeleting.tr,
            style: textStyle(GPTypography.bodyLarge),
          ),
          const SizedBox(height: 24),
          Row(
            children: [
              Expanded(
                child: TextButton(
                    onPressed: _closeBottomSheet,
                    style: TextButton.styleFrom(
                        backgroundColor: GPColor.bgSecondary,
                        shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.all(Radius.circular(8))),
                        padding: const EdgeInsets.all(12)),
                    child: Text(
                        isTaskDependency
                            ? LocaleKeys.alert_later.tr
                            : LocaleKeys.task_confirmDelete_cancel.tr,
                        style: textStyle(GPTypography.headingMedium))),
              ),
              const SizedBox(
                width: 12,
              ),
              Expanded(
                child: TextButton(
                    onPressed: _deleteTask,
                    style: TextButton.styleFrom(
                        backgroundColor: GPColor.functionNegativeSecondary,
                        shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.all(Radius.circular(8))),
                        padding: const EdgeInsets.all(12)),
                    child: Text(LocaleKeys.task_longPress_delete.tr,
                        style: textStyle(GPTypography.headingMedium)
                            ?.mergeColor(GPColor.functionNegativePrimary))),
              )
            ],
          )
        ],
      ).paddingAll(24),
    );
  }

  void _deleteTask() async {
    _closeBottomSheet();
    final Response<dynamic> response = await controller.deleteTask(task.id);
    final result = response.data;
    if (response.statusCode.isSuccess == true) {
      controller.removeTask(task);
      Popup.instance.showSnackBar(
          message: LocaleKeys.task_longPress_deleted.tr,
          type: SnackbarType.success);
    } else {
      Popup.instance.showAlert(
          title: LocaleKeys.task_longPress_deleteTask.tr,
          message:
              result['message'] ?? LocaleKeys.task_longPress_accessDenied.tr);
    }
  }

  void _closeBottomSheet() {
    if (Get.isBottomSheetOpen ?? false) {
      Get.back();
    }
  }
}
