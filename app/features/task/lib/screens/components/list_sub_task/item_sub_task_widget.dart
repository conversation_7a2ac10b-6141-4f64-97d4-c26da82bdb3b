import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_task/models/task/task.dart';
import 'package:gp_feat_task/routes/router_name.dart';
import 'package:gp_feat_task/screens/components/list_sub_task/controller/item_sub_task/item_sub_task_controller.dart';
import 'package:gp_feat_task/screens/components/list_sub_task/controller/list_task/list_sub_task_controller.dart';
import 'package:gp_feat_task/screens/components/list_sub_task/list_sub_task.dart';
import 'package:gp_feat_task/screens/components/list_sub_task/widget/check_box_widget.dart';
import 'package:gp_feat_task/screens/components/list_sub_task/widget/content_item_widget.dart';
import 'package:gp_feat_task/screens/mini-task/general/task_general_controller.dart';
import 'package:gp_feat_task/utils/extentions/response_status_checker_ext.dart';
import 'package:gp_feat_task/utils/extentions/sub_task/get_data_assign_sub_task.dart';

enum TypeFunctionCallParent {
  ///reload section parent
  reloadSection,

  ///reload list parent not section in task,
  reloadListParent,
}

class ItemSubtaskWidget extends StatelessWidget {
  ItemSubtaskWidget({
    super.key,
    required this.subTask,
    this.listSubTaskController,
    this.showNameProject = true,
    this.paddingRightItem = 16,
    this.functionCallSetDataParent,
    this.tagListTask,
    this.tagTaskGeneral,
    this.isShowTitleSubTask = false,
  }) {
    controller = ItemSubTaskController(
      tag: listSubTaskController?.tag ??
          ConstantItemSubTaskController.defaultKey(subTask.id),
      parentTag: listSubTaskController?.parentTag ??
          ConstantItemSubTaskController.defaultKey(subTask.id),
    );
  }

  final Task subTask;

  final ListSubTaskController? listSubTaskController;

  late final ItemSubTaskController controller;

  final bool showNameProject;

  final double paddingRightItem;

  final Function(TypeFunctionCallParent)? functionCallSetDataParent;
  final String? tagListTask;
  final String? tagTaskGeneral;
  final bool isShowTitleSubTask;

  final ExpandableController expandableController =
      ExpandableController(initialExpanded: true);

  @override
  Widget build(BuildContext context) {
    return Material(
      color: GPColor.bgSecondary,
      child: ExpandableNotifier(
        controller: expandableController,
        child: GetBuilder<ItemSubTaskController>(
            global: false,
            init: controller,
            initState: (_) {
              controller.setData(
                  listSubTaskController, subTask, functionCallSetDataParent);
            },
            tag: subTask.id,
            assignId: true,
            builder: (controller) {
              return Expandable(
                collapsed: Container(
                    width: double.infinity, color: Colors.white, height: 0),
                expanded: Material(
                  color: GPColor.contentInversePrimary,
                  child: GestureDetector(
                    onTap: () {
                      controller.openATask(subTask);
                    },
                    onLongPress: () {
                      _onLongPress(controller, listSubTaskController);
                    },
                    child: Column(
                      children: [
                        Container(
                          color: GPColor.contentInversePrimary,
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CheckBoxWidget(
                                controller: controller,
                                subTask: subTask,
                              ),
                              ContentItemWidget(
                                showNameProject: showNameProject,
                                isShowTitleSubTask: isShowTitleSubTask,
                                controller: controller,
                                subTask: subTask,
                              ),
                              if (!showNameProject) buttonShowSubtask(),
                            ],
                          ),
                        ),
                        Obx(() {
                          return Expandable(
                            collapsed: const SizedBox(),
                            expanded: controller.expandedSubTask.value
                                ? ListSubTask(
                                    task: subTask,
                                    paddingRightItem: paddingRightItem,
                                    isShowNameProject: showNameProject,
                                    tag: listSubTaskController?.tag ??
                                        ConstantItemSubTaskController
                                            .defaultKey(subTask.id),
                                    parentTag:
                                        listSubTaskController?.parentTag ??
                                            ConstantItemSubTaskController
                                                .defaultKey(subTask.id),
                                    needToRefreshData: true,
                                    functionCallParent: (value) async {
                                      await controller.getDataSubTask();
                                      controller.reloadListDeleteArchi();
                                      if (value ==
                                          TypeFunctionCallParent
                                              .reloadSection) {
                                        (functionCallSetDataParent ??
                                            () {})(value);
                                      }
                                    },
                                  )
                                : const SizedBox(),
                          );
                        })
                      ],
                    ),
                  ),
                ),
              );
            }),
      ),
    );
  }

  buttonShowSubtask() {
    return GetBuilder<ItemSubTaskController>(
        global: false,
        id: ConstantItemSubTaskController.kSubListIconIdTag(controller.tag),
        init: controller,
        builder: (controller) {
          return (subTask.subTasks ?? []).isNotEmpty
              ? GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    controller.toggleExpandSubTask();
                    controller.getDataSubTask();
                  },
                  child: SizedBox(
                      width: 36,
                      height: 52,
                      child: Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 8, vertical: 16),
                          child: Obx(
                            () => RotatedBox(
                              quarterTurns:
                                  controller.expandedSubTask.value ? -6 : 0,
                              child: SvgWidget(
                                  "assets/images/ic20-fill-chevron-down.svg",
                                  color: GPColor.contentSecondary),
                            ),
                          ))),
                )
              : const SizedBox(
                  width: 36,
                  height: 52,
                );
          // });
        });
  }

  Future _onLongPress(ItemSubTaskController controller,
      ListSubTaskController? listSubTaskController) async {
    Get.closeCurrentSnackbar();
    await Popup.instance.showBottomSheet(
      BottomSheetActionWidget(data: actions(controller, listSubTaskController)),
    );
  }

  List<BottomSheetActionModel> actions(ItemSubTaskController controller,
      ListSubTaskController? listSubTaskController) {
    bool isArchived = subTask.isArchived;
    var list = [
      BottomSheetActionModel(
          displayName: LocaleKeys.task_longPress_view.tr,
          iconAsset: "assets/images/ic24-line15-feed.png",
          onClick: () {
            _openTaskBottomSheet(controller);
          }),
      BottomSheetActionModel(
          displayName: LocaleKeys.task_longPress_copyUrl.tr,
          iconAsset: "assets/images/ic24-line15-link.png",
          onClick: _copyTaskLink),
    ];

    if (!subTask.isArchived) {
      list.add(
        BottomSheetActionModel(
            displayName: LocaleKeys.task_longPress_clone.tr,
            iconAsset: "assets/images/ic24-line15-2square.svg",
            onClick: () {
              _cloneTask(controller, listSubTaskController);
            }),
      );
    }

    if (subTask.projectId != null && (subTask.depth ?? 0) < 1) {
      final dependencyAction = BottomSheetActionModel(
          displayName: LocaleKeys.task_dependencies.tr,
          iconAsset: "assets/images/ic24-line15-dependency-3square.svg",
          onClick: _dependencyTask);
      list.add(dependencyAction);
    }

    final listUserCreator =
        SetDataAssignSubTask.getListSaveCreator(controller.subTask.value);

    if (haveRemovePermissionSubTask(subTask, listUserCreator)) {
      final deleteAction = BottomSheetActionModel(
          displayName: LocaleKeys.task_longPress_deleteTask.tr,
          iconAsset: "assets/images/ic24-line15-trash.png",
          textColor: GPColor.functionNegativePrimary,
          iconColor: GPColor.functionNegativePrimary,
          onClick: () {
            _deleteTask(controller);
            functionCallSetDataParent
                ?.call(TypeFunctionCallParent.reloadListParent);
          });
      list.add(deleteAction);
    }

    if (!isArchived && (subTask.depth ?? 0) > 0) {
      list.insert(
          list.length - 1,
          BottomSheetActionModel(
              displayName: LocaleKeys.task_changeSubTaskToTask.tr,
              iconAsset: "assets/images/ic24-line15-2arrow-rotate-circle.svg",
              onClick: () {
                _onClickChangeSubTask(controller);
                functionCallSetDataParent
                    ?.call(TypeFunctionCallParent.reloadSection);
              }));
    }

    return list;
  }

  // ---------- BottomSheet commands --------- \
  void _openTaskBottomSheet(ItemSubTaskController controller) {
    _closeBottomSheet();
    controller.openATask(subTask);
  }

  void _dependencyTask() async {
    if (Get.isSnackbarOpen) {
      await Future.delayed(const Duration(milliseconds: 300));
    }
    _closeBottomSheet();
    Get.toNamed(TaskRouterName.taskDependencyScreen, arguments: subTask);
  }

  void _copyTaskLink() {
    _closeBottomSheet();

    Clipboard.setData(ClipboardData(
            text:
                subTask.taskUrl ?? "${Constants.appDomain}/task/${subTask.id}"))
        .then((_) {
      Popup.instance.showSnackBar(
          message: LocaleKeys.task_longPress_copied.tr,
          type: SnackbarType.success);
    });
  }

  Future<void> _cloneTask(ItemSubTaskController controller,
      ListSubTaskController? listSubTaskController) async {
    if (Get.isSnackbarOpen) {
      await Future.delayed(const Duration(milliseconds: 300));
    }
    _closeBottomSheet();
    controller.duplicateTask(
      subTask.id,
      (newTask) {
        controller.openATask(newTask);

        controller.genericController<TaskGeneralController>()?.reload();
        listSubTaskController?.reloadList();
        functionCallSetDataParent
            ?.call(TypeFunctionCallParent.reloadListParent);
      },
      (error) {
        controller.handleError(error, StackTrace.fromString(error.toString()));
      },
    );
  }

  void _onClickChangeSubTask(ItemSubTaskController controller) {
    _closeBottomSheet();
    controller.changeSubTaskToTask();
  }

  void _deleteTask(ItemSubTaskController controller) async {
    if (Get.isSnackbarOpen) {
      await Future.delayed(const Duration(milliseconds: 300));
    }
    _closeBottomSheet();
    await Popup.instance.showBottomSheet(
      _ConfirmDeletingTaskDialog(
        task: subTask,
        controller: controller,
      ),
    );
  }

  void _closeBottomSheet() {
    if (Get.isBottomSheetOpen ?? false) {
      Get.back();
    }
  }
}

class _ConfirmDeletingTaskDialog extends StatelessWidget {
  const _ConfirmDeletingTaskDialog({
    required this.task,
    required this.controller,
  });

  final Task task;
  final ItemSubTaskController controller;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SvgWidget(
            "assets/images/ic24-line15-trash.png",
            width: 54,
            height: 59,
            color: GPColor.functionNegativePrimary,
          ),
          Text(
            "${LocaleKeys.task_confirmDelete_title.tr} \"${task.title}\"?",
            style: textStyle(GPTypography.headingLarge),
          ),
          const SizedBox(height: 8),
          Text(
            LocaleKeys.task_longPress_confirmDeleting.tr,
            style: textStyle(GPTypography.bodyLarge),
          ),
          const SizedBox(height: 24),
          Row(
            children: [
              Expanded(
                child: TextButton(
                    onPressed: _closeBottomSheet,
                    style: TextButton.styleFrom(
                        backgroundColor: GPColor.bgSecondary,
                        shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.all(Radius.circular(8))),
                        padding: const EdgeInsets.all(12)),
                    child: Text(LocaleKeys.task_confirmDelete_cancel.tr,
                        style: textStyle(GPTypography.headingMedium))),
              ),
              const SizedBox(
                width: 12,
              ),
              Expanded(
                child: TextButton(
                    onPressed: _deleteTask,
                    style: TextButton.styleFrom(
                        backgroundColor: GPColor.functionNegativeSecondary,
                        shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.all(Radius.circular(8))),
                        padding: const EdgeInsets.all(12)),
                    child: Text(LocaleKeys.task_longPress_delete.tr,
                        style: textStyle(GPTypography.headingMedium)
                            ?.mergeColor(GPColor.functionNegativePrimary))),
              )
            ],
          )
        ],
      ).paddingAll(24),
    );
  }

  void _deleteTask() async {
    _closeBottomSheet();
    final Response<dynamic> response = await controller.deleteTask(task.id);
    final result = response.data;
    if (response.statusCode.isSuccess == true) {
      controller.reloadListDeleteArchi();
      await controller.reloadInforParent();
      Popup.instance.showSnackBar(
          message: LocaleKeys.task_longPress_subTaskDeleted.tr,
          type: SnackbarType.success);
    } else {
      Popup.instance.showAlert(
          title: LocaleKeys.task_longPress_deleteTask.tr,
          message:
              result['message'] ?? LocaleKeys.task_longPress_accessDenied.tr);
    }
  }

  void _closeBottomSheet() {
    if (Get.isBottomSheetOpen ?? false) {
      Get.back();
    }
  }
}
