import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_task/models/task/task.dart';
import 'package:gp_feat_task/screens/components/task/filter_status_subtask/controller/selection_switch_button_controller.dart';
import 'package:gp_feat_task/screens/mini-task/show_hide_subtask/show_subtask_donetask_ext.dart';

class BottomSheetSelectionWithSwitchWidget extends StatelessWidget {
  const BottomSheetSelectionWithSwitchWidget(
      {this.title,
      required this.data,
      required this.openFromMyTask,
      super.key});

  final String? title;
  final List<BottomSheetWithSwitchModel> data;
  final bool openFromMyTask;

  // check title
  bool get hasTitle => title != null && title!.isNotEmpty;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        top: false,
        child: GetBuilder<SelectionSwitchButtonController>(
          assignId: true,
          init: SelectionSwitchButtonController()
            ..onInitData(openFromMyTask, data),
          builder: (controller) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                if (hasTitle)
                  Text(title!,
                      style: textStyle(
                        GPTypography.headingMedium,
                      )).paddingSymmetric(vertical: 10),
                if (hasTitle) Divider(color: GPColor.bgSecondary, thickness: 1),
                _FilterListView(
                  data: data,
                  controllerSelectionButton: controller,
                ),
              ],
            );
          },
        ));
  }
}

// list filter
class _FilterListView extends StatelessWidget {
  const _FilterListView(
      {required this.data, required this.controllerSelectionButton});

  final List<BottomSheetWithSwitchModel> data;
  final SelectionSwitchButtonController controllerSelectionButton;

  @override
  Widget build(BuildContext context) {
    return ObxValue<RxBool>((loadingScreen) {
      if (loadingScreen.value) {
        return const _ShimmerItem();
      }

      return ListView.builder(
        padding: const EdgeInsets.all(0),
        physics: const ClampingScrollPhysics(),
        shrinkWrap: true,
        itemCount: data.length,
        itemBuilder: (_, index) {
          return _Item(
            item: data[index],
            controllerSelectionButton: controllerSelectionButton,
          );
        },
      );
    }, controllerSelectionButton.loadingScreen);
  }
}

// item
class _Item extends StatelessWidget {
  _Item({
    required this.item,
    required this.controllerSelectionButton,
  }) {
    status.value = item.status;
  }

  final BottomSheetWithSwitchModel item;
  final RxBool status = false.obs;
  final SelectionSwitchButtonController controllerSelectionButton;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: Text(
            item.displayName,
            style: textStyle(GPTypography.bodyLarge),
          ).paddingSymmetric(horizontal: 12),
        ),
        ObxValue<RxBool>(
          (data) => CupertinoSwitch(
            onChanged: ((value) {
              // update ui
              status.value = value;
              // update status
              item.status = value;
              controllerSelectionButton.putData(item);
            }),
            activeColor: GPColor.functionAccentWorkSecondary,
            value: status.value,
          ),
          status,
        ),
      ],
    ).paddingAll(12);
  }
}

class _ShimmerItem extends StatelessWidget {
  const _ShimmerItem();

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: const EdgeInsets.all(2),
      physics: const ClampingScrollPhysics(),
      shrinkWrap: true,
      itemCount: 2,
      itemBuilder: (_, index) {
        return Container(
          margin: const EdgeInsets.only(bottom: 5),
          child: Shimmer.fromColors(
              baseColor: Colors.grey[300]!,
              highlightColor: Colors.grey[100]!,
              enabled: true,
              child: Container(
                width: 100,
                height: 40,
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8)),
              )),
        );
      },
    ).paddingAll(12);
  }
}

class BottomSheetWithSwitchModel {
  BottomSheetWithSwitchModel(
      {required this.displayName, required this.value, this.status = false});

  final String displayName;
  final TaskFilterStatus value;
  bool status = false;

  BottomSheetWithSwitchModel clone() {
    return BottomSheetWithSwitchModel(
        displayName: displayName, value: value, status: status);
  }

  String get getKey {
    // key map with api
    switch (value) {
      case TaskFilterStatus.done:
        return "status";
      case TaskFilterStatus.showSubtask:
        return "include_subtask";
      default:
        return "";
    }
  }

  /// - Cho phép hiển thị subtask:
  /// <br>-----> `"include_subtask"` : `true` (ẩn/hiện subtask)"
  /// - Cho phép hiển thị theo trạng thái công việc:
  /// <br>-----> `"status"` : `"0, 1, 2"` (chuỗi các trạng thái (`TaskStatus.value`), cách nhau bởi dấu phảy)"

  dynamic get getValue {
    switch (value) {
      case TaskFilterStatus.done:
        return filterListTask(status);
      case TaskFilterStatus.showSubtask:
        return status;
      default:
        return "";
    }
  }
}
