import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/extensions/date_time_extension.dart';
import 'package:gp_core/navigator/platform_navigator.dart';
import 'package:gp_core/shared_features/custom_repeat/string.dart';
import 'package:gp_core/utils/gp_sentry.dart';
import 'package:gp_feat_task/domain/use_case/task_detail_use_case.dart';
import 'package:gp_feat_task/models/comment/comment.dart';
import 'package:gp_feat_task/models/section/section.dart';
import 'package:gp_feat_task/models/task/participants_model.dart';
import 'package:gp_feat_task/models/task/permissions_model.dart';
import 'package:gp_feat_task/models/task/task.dart';
import 'package:gp_feat_task/models/task/user_role.dart';
import 'package:gp_feat_task/routes/router_name.dart';
import 'package:gp_feat_task/screens/mini-task/folder/task_folder_controller.dart';
import 'package:gp_feat_task/screens/mini-task/general/task_general_controller.dart';
import 'package:gp_feat_task/screens/mini-task/general/task_general_list_controller.dart';
import 'package:gp_feat_task/screens/mini-task/project/model/task_project_mode.dart';
import 'package:gp_feat_task/screens/mini-task/project/model/task_project_model.dart';
import 'package:gp_feat_task/screens/mini-task/project/project_list_controller.dart';
import 'package:gp_feat_task/screens/mini-task/project/search/item_search_model.dart';
import 'package:gp_feat_task/screens/mini-task/project/search/project_search_screen.dart';
import 'package:gp_feat_task/screens/mini-task/task-list/model/task_list.dart';
import 'package:gp_feat_task/screens/mini-task/task-list/project_task_list_controller.dart';
import 'package:gp_feat_task/screens/sub_tasks/controller/sub_task_controller.dart';
import 'package:gp_feat_task/screens/task_collab/controllers/collab_checker.dart';
import 'package:gp_feat_task/screens/task_create/model/task_creation_state.dart';
import 'package:gp_feat_task/screens/task_create/nav/task_create_argument.dart';
import 'package:gp_feat_task/screens/task_create/widgets/component_confirm_popup_sheet.dart';
import 'package:gp_feat_task/screens/task_details/comment/input/controller.dart';
import 'package:gp_feat_task/screens/task_details/comment/input/task_create_ext.dart';
import 'package:gp_feat_task/screens/task_details/comment/medias/media.dart';
import 'package:gp_feat_task/screens/task_details/comment/medias/medias_controller.dart';
import 'package:gp_feat_task/screens/task_details/description/task_description_controller.dart';
import 'package:gp_feat_task/screens/task_details/reply_comment/controller/reply_comment_controller.dart';
import 'package:gp_feat_task/screens/task_details/upload/components/confirm_delete_dialog.dart';
import 'package:gp_feat_task/screens/task_details/upload/model/task_attachment_file.dart';
import 'package:gp_feat_task/screens/task_details/upload/pages/upload_controller.dart';
import 'package:gp_feat_task/screens/task_repo/task_repo.dart';
import 'package:gp_feat_task/screens/tasks/model/task_screen_argument.dart';
import 'package:gp_feat_task/screens/tasks/tasks_screen.dart';
import 'package:gp_feat_task/service/comment_api.dart';
import 'package:gp_feat_task/service/task_api.dart';
import 'package:gp_feat_task/utils/extentions/response_status_checker_ext.dart';
import 'package:gp_feat_task/utils/extentions/sub_task/get_data_assign_sub_task.dart';

import '../mini-task/folder/components/confirm_archive_popup.dart';
import '../mini-task/section/section_function.dart';
import 'extension/prelude.dart';
import 'task_create_base_controller.dart';
import 'widgets/confirm_cancel_upload_medias.dart';

export 'extension/prelude.dart';

part './confirm_discard_draft_task.dart';

enum TaskCreateScreenGetXKeys { listAttachments }

extension StringValue on TaskCreateScreenGetXKeys {
  String get key {
    switch (this) {
      case TaskCreateScreenGetXKeys.listAttachments:
        return '';
    }
  }
}

class TaskCreateController extends TaskCreateBaseController
    with
        TaskPermissionMixin,
        GetSingleTickerProviderStateMixin,
        DuplicateTaskFunction,
        SectionFunction,
        TaskCreateTagMixin,
        TaskFromMessageMixin,
        TaskCreateSubControllers,
        TaskCreateCheckListMixin,
        RRuleString {
  TaskCreateController({
    required super.tag,
    required super.parentTag,
  });

  final TextEditingController titleController = TextEditingController();

  List<GPUserModel> listUserCreator = [];

  final FocusNode titleFocusNode = FocusNode();

  final TaskAPI apiService = TaskAPI();

  // new task repo
  final TaskRepo taskRepo = TaskRepo();
  late TaskDetailUseCase taskDetailUseCase;

  Timer? debounceTimer;

  /// Current mode of the create task screen
  Rx<TaskCreationState> taskCreationState = TaskCreationState.initialized.obs;

  /// To bind and display attachment files section on UI
  Rx<List<TaskAttachmentFile>> attachmentFiles = Rx(<TaskAttachmentFile>[]);

  /// to detect user roles
  Rx<List<UserTaskRole>> roles = Rx([]);

  RxBool showEmptyState = false.obs;

  bool canLoadMore = true;

  Rx<bool> hasMoreButton = true.obs;

  TaskCreateArgument? taskCreateArgument;

  final RxString taskListName = LocaleKeys.task_my_task.tr.obs;

  List<int> projectMemberIds = [];

  bool isRtf = false;
  bool openFromDeepLink = false;

  Project? project;

  String? taskId;

  RxBool isArchived = false.obs;
  RxBool isFetchingTask = false.obs;
  RxBool isSubtask = false.obs;
  RxBool reloadTaskDetail = false.obs;
  RxBool reloadDependencyTask = false.obs;

  double titleElevation = 0.0;

  RxInt preCount = 0.obs;

  RxString taskListId = ''.obs;

  ParticipantsModel participantsModel =
      ParticipantsModel(watcherIds: [], assigneeIds: [], creatorIds: []);
  PermissionsModel permissionsModel = PermissionsModel(
    createSubtask: false,
    deleteTask: false,
    updateTask: false,
  );

  Map<String, PermissionsModel> mapCheckPermissionSubTask = {};

  String get repeatRule {
    if (task.rRule?.isNotEmpty ?? false) {
      final startString = task.rRule!
          .substring(0, task.rRule!.indexOf("FREQ"))
          .replaceAll("RRULE:", "");
      final rRuleString = task.rRule!
          .substring(task.rRule!.indexOf("FREQ"), task.rRule!.length);
      final localTime = localDateTimeFromRRule(startString);
      final rRuleModel = RRuleModel(rRuleString, startDate: localTime);
      final rRule = rRuleModel.getDisplayName(localTime, hideStartTime: true);
      final createTime =
          "${LocaleKeys.task_datetimePicker_duplicateCreateTime.tr} ${localTime.formathhmmAMPM}";
      final dueDateTime = DateTimeUtils.instance
          .convert24hStringTimeToDateTimeToday(
              task.recurrenceSettings?.dueDateTime);
      final dueDate =
          "${LocaleKeys.task_datetimePicker_duplicateDueDate.tr} ${dueDateTime?.formathhmmAMPM}";
      return dueDateTime != null
          ? "$rRule
$createTime
$dueDate"
          : "$rRule
$createTime";
    } else {
      return "";
    }
  }

  bool get isSubTask => this is SubTaskController;

  bool get isMyTask =>
      task.projectId == null &&
      taskCreationState.value == TaskCreationState.created;

  bool get isHomeless {
    try {
      // task vô gia cư, không có project/taskList
      if ((task.projectId != null && task.projectId!.isNotEmpty) ||
          (task.taskListId != null && task.taskListId!.isNotEmpty)) {
        return false;
      }
    } catch (ex) {}

    return true;
    // return taskListName.value == LocaleKeys.task_my_task.tr;
  }

  // ---------- permissions ---------- \
  bool get isTaskCreated =>
      taskCreationState.value == TaskCreationState.created;

  /// auto open edit
  bool get canEditTitle =>
      roles.value.canEditTitle ||
      isProjectMember() ||
      checkContainerUserActionTask();

  bool get canEditDueDate =>
      roles.value.canEditDueDate ||
      isProjectMember() ||
      checkContainerUserActionTask();

  bool get canEditPriority =>
      roles.value.canEditPriority ||
      isProjectMember() ||
      checkContainerUserActionTask();

  bool get canEditAssignee =>
      roles.value.canEditAssignee ||
      isProjectMember() ||
      checkContainerUserActionTask();

  bool get canEditStatus =>
      roles.value.canEditStatus ||
      isProjectMember() ||
      checkContainerUserActionTask();

  bool get canEditAttachmentFile =>
      roles.value.canEditAttachmentFile ||
      isProjectMember() ||
      checkContainerUserActionTask();

  bool get canEditDescription =>
      roles.value.canEditDescription ||
      isProjectMember() ||
      checkContainerUserActionTask();

  bool get canEditRemoveTask =>
      roles.value.canEditRemoveTask || isProjectMember();

  bool get canPickTaskList =>
      roles.value.isNotEmpty ||
      isProjectMember() ||
      checkContainerUserActionTask();

  bool get canEditRemoveTodo =>
      (roles.value.canEditRemoveTodoTask || isProjectMember()) &&
          !isArchived.value ||
      checkContainerUserActionTask();

  bool get isFromTaskMessage => Get.arguments is Task && task.id.isEmpty;

  bool haveRemovePermission(Task task, List<UserTaskRole> roles) {
    if (task.taskListId?.isEmpty ?? true && roles.canEditRemoveTask) {
      return true;
    }
    if (task.taskListId?.isNotEmpty ?? false) {
      if (canEditRemoveTask) {
        return true;
      }
    }
    return false;
  }

  // end of permissions

  // count assignees
  int get countNumberAssignees =>
      task.assignees == null ? 0 : task.assignees!.length;

  @override
  void onInit() {
    super.onInit();

    onInitExt();
  }

  // ----------- init extension ----------- \
  bool pushedFromTaskList = false;

  void onInitExt() async {
    initTaskSubController();
    taskDetailUseCase = TaskDetailUseCase(_commentAPIService);

    // create new Task or update current Task
    if (Get.arguments is Task) {
      task = Get.arguments;
      taskId = task.id;
      taskListId.value = task.taskListId ?? '';
      listTag.value = task.listTag ?? [];
      isSubtask.value = task.parentTaskId != null;

      task.openTaskFromDeepLink = task.openTaskFromDeepLink ?? false;
      await checkGetPermissionApi();

      if (isFromTaskMessage) {
        _initTaskFromChatMessage();

        await activeTaskIfNeeded(task.projectId);

        await fetchProject();
      } else {
        // if (!isSubTask) {
        _initTaskWithId();
        // }
      }
    } else if (Get.arguments is Map) {
      Map<String, dynamic> args = Get.arguments;
      navCommentId = args["commentId"];
      taskId = args["taskId"];
      if (taskId == null || taskId?.isEmpty == true) {
        return;
      }

      task = Task(id: taskId!);
      if (args["parentCommentId"] != null) {
        task.commentId = args["parentCommentId"];
        task.replyCommentId = args["commentId"];
      }
      taskCreationState.value = TaskCreationState.created;
      task.openTaskFromDeepLink = true;
      checkGetPermissionApi();
      fetchTaskDetails();

      openFromDeepLink = true;
    } else {
      _initTaskFromTaskCreateArgument();
    }

    listUserCreator = SetDataAssignSubTask.getListSaveCreator(task);

    task.openTaskFromDeepLink = openFromDeepLink;
    updatedTask = task;

    // pushed from task list
    final previousRoute = Get.routing.previous;
    if (previousRoute == TaskRouterName.tasks ||
        previousRoute ==
            RouterName.routeWithoutAnimation(TaskRouterName.tasks)) {
      pushedFromTaskList = true;
    }

    initInputCommentController();

    getDetailTaskWaiting();

    logDebug("task -> $task");

    _bind();
  }

  Future<void> getDetailTaskWaiting() async {
    try {
      if (task.id.isEmpty) return;

      if (task.blockingIds?.isEmpty == true &&
          task.waitingIds?.isEmpty == true) {
        return;
      }

      final res = await apiService.getDetailWaitingTask(task.id);

      if (res == null) return;

      List<Task> dataTaskApi = res.data;
      if (dataTaskApi.isNotEmpty) {
        if ((task.waitingTasks ?? []).isNotEmpty && task.waitingTasks != null) {
          for (int i = 0; i < task.waitingTasks!.length; i++) {
            for (int j = 0; j < dataTaskApi.length; j++) {
              if (task.waitingTasks![i].id == dataTaskApi[j].id) {
                task.waitingTasks![i] = dataTaskApi[j];
              }
            }
          }
        }
        if ((task.blockingTasks ?? []).isNotEmpty &&
            task.blockingTasks != null) {
          for (int i = 0; i < task.blockingTasks!.length; i++) {
            for (int j = 0; j < dataTaskApi.length; j++) {
              if (task.blockingTasks![i].id == dataTaskApi[j].id) {
                task.blockingTasks![i] = dataTaskApi[j];
              }
            }
          }
        }
      }
    } catch (e, s) {
      GPCoreTracker().appendError(
        'Flutter.task.getDetailTaskWaiting',
        data: {'error': e, 'stacktrace': s},
      );
      GPCoreTracker().sendLog(
        message: 'Flutter.task.getDetailTaskWaiting',
        trackerType: GPTrackerType.task,
      );
    }
    reloadDependencyTask.value = !reloadDependencyTask.value;
  }

  void _initTaskFromChatMessage() {
    _updateUIElementsWith(task: task);

    _bind();

    initTaskData(task);

    roles.value = UserTaskRole.values;

    taskCreationState.value = TaskCreationState.initialized;
  }

  void _initTaskWithId() {
    if (Get.parameters["scrollToChecklist"] == "1") {
      isScrollToChecklist = true;
    }
    taskListName.value = task.taskListName ?? "";
    // section.value = SectionModel(
    //     id: task.sectionId,
    //     name: task.sectionName,
    //     taskListId: task.taskListId);
    _getSectionFromTask(task, isShowError: false);
    if (task.isDuplicating == true) {
      statusController.initData(task.taskStatus,
          drafting: true, archived: false);
      _prepareDuplicate();
    } else {
      taskCreationState.value = TaskCreationState.created;
      isArchived.value = task.isArchived;
      statusController.initData(task.taskStatus,
          drafting: false, archived: task.isArchived);
      _updateUIElementsWith(task: task, isShowError: false);

      //_initTaskData(task);

      fetchTaskDetails();
    }

    // nav to comment if needed
    // includeActivityLogs.value =
    //     task.commentId != null && task.commentId!.isNotEmpty;
    navCommentId = task.commentId;
    navCommentReply = task.replyCommentId;
  }

  void _initTaskFromTaskCreateArgument() {
    task = Task(id: '');
    roles.value = [UserTaskRole.creator];
    statusController.initData(task.taskStatus, drafting: true, archived: false);

    // has TaskStatus passed from Get.arguments
    if (Get.arguments is TaskCreateArgument) {
      taskCreateArgument = Get.arguments;
      if (taskCreateArgument != null) {
        if (taskCreateArgument!.section != null) {
          section.value = taskCreateArgument!.section!;
          task.sectionId = section.value.id;
          task.sectionName = section.value.name;
        }
        task.projectId = taskCreateArgument?.projectId ?? "";
        task.taskListId = taskCreateArgument?.taskListId ?? "";
        taskListId.value = taskCreateArgument?.taskListId ?? "";
        // task vô gia cư -> lấy tên taskList mặc định
        // task tạo từ project (có taskListId) -> lấy tên taskListName của taskCreateArgument
        if (task.taskListId?.isNotEmpty ?? false) {
          taskListName.value = taskCreateArgument?.taskListName ?? "";
        }
        task.taskListName = taskListName.value;
      }
      fetchProject();
    } else {
      taskCreateArgument = TaskCreateArgument();
    }

    _updateUIElementsWith(task: task);
  }

  void _prepareDuplicate() {
    taskCreationState.value = TaskCreationState.duplicating;

    _updateUIElementsWith(task: task);

    initTaskData(task, ignoreStatus: true);

    sourceForDuplicateTask = task;

    updatedTask = sourceForDuplicateTask;

    listUserCreator = SetDataAssignSubTask.getListSaveCreator(task);

    todoTask.value = task.subTasks ?? [];

    // check roles
    initRoles();

    inputCommentController?.mentionController
        .setProjectId(task.projectId ?? "");
    taskCreateArgument = TaskCreateArgument(taskListId: task.taskListId);
  }

  /// execute binding logics
  void _bind() {
    titleController.addListener(_validateAndUpdateTaskCreationStateIfNeeded);
    titleFocusNode.addListener(_listenUpdateTitle);
    if (taskCreationState.value == TaskCreationState.initialized) {
      titleFocusNode.requestFocus();
    }
    scrollController.addListener(() {
      final lastTitleElevation = titleElevation;
      if (scrollController.offset <=
              scrollController.position.minScrollExtent &&
          !scrollController.position.outOfRange) {
        titleElevation = 0;
      } else {
        titleElevation = 0.5;
      }
      if (titleElevation != lastTitleElevation) {
        update([TaskCreateControllerConst.taskTitleKey]);
      }
    });
  }

  void _listenUpdateTitle() {
    if (task.isDuplicating == true) {
      //do nothing
      return;
    }
    // automatically update task when user dismiss title textfield focus
    if (taskCreationState.value == TaskCreationState.created) {
      if (!titleFocusNode.hasFocus) {
        if (titleController.text.trim().isNotEmpty) {
          // call api to update task if title is not empty
          if (task.title != titleController.text) {
            // Only update task that has been created
            if (taskCreationState.value != TaskCreationState.created) return;
            updateTaskTitle(title: titleController.text, taskId: task.id);
          }
        } else {
          // if title is empty then so an alert to remind user that field is required
          _showErrorDialog(Get.context!);
        }
      }
    } else {
      task.title = titleController.text;
    }
  }

  /// validate and update data during user enters title
  /// to update [taskCreationState], all logic like enter title, add assignees,
  /// add files, etc. should be validate in this function
  void _validateAndUpdateTaskCreationStateIfNeeded() {
    // for created task don't need to validate
    if (taskCreationState.value == TaskCreationState.created) {
      return;
    }

    // require title to able to finish creating task
    if (titleController.text.trim().isEmpty) {
      taskCreationState.value = TaskCreationState.initialized;
      return;
    }

    // detect first time user enters title, change the creation state to drafting
    if (taskCreationState.value == TaskCreationState.initialized &&
        titleController.text.trim().isNotEmpty) {
      taskCreationState.value = TaskCreationState.drafting;
    }
  }

  /// to show error dialog
  _showErrorDialog(BuildContext context) {
    if ((Get.isDialogOpen ?? true)) {
      return;
    }
    Popup.instance.showAlert(
      title: LocaleKeys.taskCreation_failure_generalTitle.tr,
      message: LocaleKeys.taskCreation_failure_emptyTaskTitle.tr,
    );
  }

  // end of init extension \

  // ---------- attachments ---------- \
  void pushToUploadScreen({
    FileType? fileType,
    GPFilePickerResult? pickerResult,
  }) async {
    // check limit 16 attachment files
    // final int count = attachmentFiles.value.length +
    //     (pickerResult?.filePickerResult?.count ?? 0) +
    //     (pickerResult?.xfiles?.length ?? 0);
    // if (count > 16) {
    //   showPopUpLimit16AttachmentFiles();
    //   return;
    // }

    // navigate to upload files screen arguments to be sent are PickerResult and Task
    // and the callback is List<TaskAttachmentFile> if the task is updated,
    // in scenario creating task flow, the callback result is List<UploadResponseModel>
    // that have been uploaded before
    final dynamic result = await Get.toNamed(TaskRouterName.upload, arguments: [
      fileType,
      pickerResult?.filePickerResult ?? pickerResult?.gpXFiles,
      task,
      attachmentFiles.value,
      OnUploadCallback(onAttachmentFileUpdated: onAttachmentFileUpdated)
    ]);
    if (result is List<TaskAttachmentFile>) {
      task.attachmentFiles = result;
      _updateUIElementsWith(task: task);
    } else {
      // if user swipe to back on ios or tap back button on android device,
      // can't detect attachment files changes so fetch the task again
      // to make sure the data is correct and only update task that has been created
      if (taskCreationState.value != TaskCreationState.created) return;
      fetchTaskDetails();
    }
  }

  /// this method will be called from UploadController
  /// should be used only for creating task flow, because if task is updated
  /// then can fetch task details to get attachment files
  void onAttachmentFileUpdated(List<TaskAttachmentFile> attachmentFile) {
    if (!isFromTaskMessage) {
      attachmentFile.removeWhere((element) => element.url == null);
    }
    if (taskCreationState.value != TaskCreationState.created) {
      task.attachmentFiles = attachmentFile;
      _updateUIElementsWith(task: task);
    }
  }

  /// to be called when user selects the last item on attachment section that exceed display limitation
  void onSeeAllAttachmentFilesTapped() {
    hideMentions();
    hideEmojiKeyboard();
    // make sure keyboard is dismissed
    dismissKeyboard();

    //  check role
    if (!canEditAttachmentFile) return;

    //
    pushToUploadScreen();
  }

  void selectAttachmentFile(TaskAttachmentFile file) {
    hideMentions();
    hideEmojiKeyboard();
    // make sure keyboard is dismissed
    dismissKeyboard();

    onAttachmentFileSelected(
        file, DownloadSource(type: DownloadSourceIdType.task, id: task.id),
        list: attachmentFiles.value, onReceiveProgress: (progress) {
      if (progress < 1 && !file.isLoading) {
        file.isLoading = true;
        update([TaskCreateScreenGetXKeys.listAttachments]);
      }
      if (progress == 1) {
        file.isLoading = false;
        update([TaskCreateScreenGetXKeys.listAttachments]);
      }
    });
  }

  void showFilePickerBottomSheet() {
    if (!canEditAttachmentFile) return;

    hideMentions();
    hideKeyboards();
    // make sure keyboard is dismissed
    dismissKeyboard();

    // // check
    // if (attachmentFiles.value.length >= 16) {
    //   showPopUpLimit16AttachmentFiles();
    //   return;
    // }

    // check role
    // if (!roles.value.canEditAttachmentFile) return;

    Popup.instance
        .showBottomSheet(FilePickerBottomSheet(onSelected: (fileType) async {
      var result = await GPPicker.instance.pick(fileType);
      SystemChrome.setSystemUIOverlayStyle(systemUiOverlayStyle);

      if (result != null) {
        pushToUploadScreen(fileType: fileType, pickerResult: result);
      }
    }));
  }

  //

  // ---------- comments ---------- \
  final CommentAPI _commentAPIService = CommentAPI();

  final RxList<Comment> comments = RxList();

  List<String> listCommentLocalAdd = [];

  String? navCommentId;
  String? navCommentReply;

  String nextLink = '';
  String preLink = '';

  final RxBool includeActivityLogs = false.obs;

  final RefreshController refreshController = RefreshController();

  final AutoScrollController scrollController =
      AutoScrollController(axis: Axis.vertical);

  Future _navToACommentId() async {
    if (navCommentId == null) return;
    if (navCommentId!.isEmpty) return;
    if (comments.isEmpty) return;

    Comment? comment =
        comments.firstWhereOrNull((element) => element.id == navCommentId);

    if (comment == null) return;
    navCommentId = "";

    if (navCommentReply != null) {
      comment.replyHighLightComment = navCommentReply;
    }

    int index =
        comments.indexWhere((element) => element.id == (comment.id ?? '')) + 1;

    await 100.milliseconds.delay();
    await _scrollToFirstComment(indexScroll: index);

    //scroll to replay comment
    if (navCommentReply != null) {
      await 100.milliseconds.delay();
      final value = await taskDetailUseCase.getHeightScrollCommentReply(
          task: task, navCommentReply: navCommentReply);
      await _scrollToLastComment(
          positionScroll: scrollController.offset + value);
    }

    if (navCommentReply == null) {
      comment.rxUseHighlightBg.value = true;
      await Future.delayed(const Duration(seconds: 3));
      comment.rxUseHighlightBg.value = false;
    }
  }

  Future<void> getListComment({bool refresh = false, loadmore = false}) async {
    try {
      if (refresh) {
        // cập nhật lại trạng thái loadMore: có thể loadMore khi force refresh data
        refreshController.footerMode?.value = LoadStatus.canLoading;

        nextLink = '';
        final response = await _commentAPIService.getListComment(
          taskId: task.id,
          includeActivityLogs: includeActivityLogs.value,
          highlight: navCommentId,
        );
        comments.value = response.data ?? [];

        // update next link to perform loadmore later
        nextLink = response.links?.next ?? "";
        preLink = response.links?.prev ?? "";
        preCount.value = response.links?.beforeCount ?? 0;

        _navToACommentId();
      } else if (loadmore) {
        // check has next data
        if (nextLink.isEmpty) {
          refreshController.loadNoData();
          return;
        }

        final response = await _commentAPIService.getNextComment(
          next: nextLink,
        );

        refreshController.loadComplete();

        final listComment = filterCommentAddLocal(response.data ?? []);

        comments.addAll(listComment);

        // update next link to perform loadmore later
        nextLink = response.links?.next ?? "";
      }
      getDetailTaskWaiting();
      updateComment();
    } catch (ex, s) {
      GPCoreTracker().appendError(
        'Flutter.task.getListComment',
        data: {'error': ex, 'stacktrace': s},
      );
      GPCoreTracker().sendLog(
        message: 'Flutter.task.getListComment',
        trackerType: GPTrackerType.task,
      );

      logDebug("$ex, $s");
    }
  }

  void updateComment() {
    update([TaskCreateControllerConst.taskCommentsKey]);
  }

  List<Comment> filterCommentAddLocal(List<Comment> listComment) {
    List<Comment> listCommentData = listComment;

    if (listCommentLocalAdd.isNotEmpty) {
      for (final item in listCommentLocalAdd) {
        listCommentData.removeWhere((data) => data.id == item);
      }
    }

    return listCommentData;
  }

  Future<void> loadPreComment() async {
    // check has next data
    if (preLink.isEmpty) {
      refreshController.loadNoData();
      return;
    }

    final response = await _commentAPIService.getNextComment(
      next: preLink,
    );

    comments.value = [...response.data ?? [], ...comments];
    // update next link to perform loadmore later
    preLink = response.links?.prev ?? "";
    preCount.value = response.links?.beforeCount ?? 0;
    updateComment();
  }

  Future<void> onLoadMoreComment() async {
    try {
      await getListComment(loadmore: true);
    } catch (e, s) {
      handleError(e, s);
    }
  }

  Comment newCommentLocal(
    String value, {
    List<CommentMediaModel>? commentMediaLocals,
    String? idParent,
  }) {
    return Comment(
      targetId: task.id,
      targetType: 'mini-task',
      text: value,
      mentions: inputCommentController?.textEditingController.mentions.toList(),
      user: User(
        avatar: Constants.avatar() ?? "",
        avatarThumbPattern: Constants.avatar() ?? "",
        displayName: Constants.displayName(),
        fullName: Constants.displayName(),
        userId: Constants.userId(),
      ),
      commentAs: CommentAs(authorId: Constants.userId(), authorType: 'user'),
      dataSource: 3,
      parentId: idParent,
    )
      ..commentMediaLocals = commentMediaLocals
      ..createdAt
      ..updatedAt = DateTime.now().microsecondsSinceEpoch;
  }

  Future afterCreateNewLocalComment(Comment comment) async {
    comment.text = inputCommentController?.textEditingController.text;
    comment.mentions =
        inputCommentController?.textEditingController.mentions.toList();

    inputCommentController?.clearText();
    inputCommentController?.clearMedias();
    dismissKeyboard();
  }

  Future scrollToBottom() async {
    await 200.milliseconds.delay();
    await _scrollToLastComment();
  }

  Future scrollToTop() async {
    // await 200.milliseconds.delay();
    return await _scrollToFirstComment();
  }

  Future scrollToPositon(double px) async {
    await 200.milliseconds.delay();
    await scrollController.animateTo(px,
        curve: Curves.easeOut, duration: const Duration(milliseconds: 200));
  }

  /// [commentMedias] nullable, [CommentMediasController] is nullable
  Future<Comment?> createNewComment(Comment comment) async {
    try {
      final response = await _commentAPIService.createComment(comment);

      // await scrollToBottom();
      return response.data;
    } catch (e, s) {
      handleError(e, s);
    }

    return null;
  }

  Future<void> _scrollToLastComment({double? positionScroll}) async {
    await scrollController.animateTo(
        positionScroll ?? scrollController.position.maxScrollExtent,
        curve: Curves.easeOut,
        duration: const Duration(milliseconds: 200));
  }

  Future<void> _scrollToFirstComment({int? indexScroll}) async {
    return await scrollController.scrollToIndex(
      indexScroll ?? 1,
      preferPosition: AutoScrollPosition.begin,
    );
  }

  Future editComment(Comment comment) async {
    try {
      inputCommentController?.clearText();
      inputCommentController?.createCommentMode = CreateCommentMode.create;
      inputCommentController?.clearMedias();

      dismissKeyboard();

      final response = await _commentAPIService.editComment(comment);
      final newComment = response.data;
      if (comment.parentId != null &&
          Get.isRegistered<ReplyCommentController>(tag: comment.parentId)) {
        Get.find<ReplyCommentController>(tag: comment.parentId)
            .reloadReplyComment(
                typeStatusSendComment: CreateCommentMode.edit,
                commentValue: newComment);
      } else {
        comments[comments
            .indexWhere((element) => element.id == newComment.id)] = newComment;
      }
      updateComment();
    } catch (e, s) {
      handleError(e, s);
    }
  }

  Future deleteComment(Comment comment) async {
    final id = comment.id;
    if (id == null) return;
    try {
      await _commentAPIService.deleteComment(comment.id ?? '');
      if (comment.parentId != null &&
          Get.isRegistered<ReplyCommentController>(tag: comment.parentId)) {
        Get.find<ReplyCommentController>(tag: comment.parentId)
            .reloadReplyComment(
                typeStatusSendComment: CreateCommentMode.delete,
                commentValue: comment);
        updateComment();
      } else {
        comments.removeWhere((element) => element.id == id);
        await _fetchTaskDetail();
      }
    } catch (e, s) {
      handleError(e, s);
    }
  }

  //

  // ----------- description ----------- \
  final Rx<String?> taskDescription = Rx<String?>(null);

  bool get hasTaskDescription {
    return taskDescription.value is String && taskDescription.value!.isNotEmpty;
  }

  /// to be called when user tap on add content/description button
  void onAddContent() async {
    if (isArchived.value == true) {
      return;
    }

    hideMentions();
    dismissKeyboard();

    // check role
    if (!canEditDescription) return;

    // The Task description will return a result is updated description
    // if there is no update then the original description will be returned
    var result = await Get.toNamed(
      TaskRouterName.taskDescription,
      arguments: TaskDescriptionArgument(
        task: task,
        onTaskDescriptionCallback: OnTaskDescriptionCallback(
          updateIsRTF: (value) {
            isRtf = value;
          },
        ),
      ),
    );

    if (result is String) {
      // update UI
      taskDescription.value = result;
      task.description = result;
      if (taskCreationState.value == TaskCreationState.created) {
        await updateTaskDescription(
            description: result, taskId: task.id, isRtf: isRtf);
      }
    }
  }

  /// to be called when user taps on a detectable in description text view
  void onTapDescriptionElement(String element) {
    final TextElementType? type = Utils.textElementType(element);
    if (type is TextElementType) {
      switch (type) {
        case TextElementType.hashtag:
          Deeplink.openHashtag(element);
          break;
        case TextElementType.url:
          launchUrl(Uri.parse(element));
          break;
        case TextElementType.phonenumber:
          launchUrl(Uri.parse('tel://$element'));
          break;
        case TextElementType.tag:
          Deeplink.openUser(element);
          break;
      }
    } else {
      logDebug('Unable to detect element type with input: $element');
    }
  }

  //

  // ---------- project ---------- \
  Future<String> createTaskList(
    String taskListName,
    String projectId, {
    String? folderId,
  }) async {
    /*
      Tạo task list default nếu:
        - collab group đã active Task
        - collab group không có bất cứ task list nào
      return taskListId
    */
    try {
      var params = {
        "name": taskListName,
        "project_id": projectId,
      };

      if (folderId != null && folderId.isNotEmpty) {
        params.addAll({
          "folder_id": folderId,
        });
      }

      final result = await taskService.createTaskList(params);
      Utils.showSuccessSnackBar(
          LocaleKeys.taskList_createTaskListSuccessfully.tr);

      return result["data"]["id"];
    } catch (e, s) {
      handleError(e, s);
    }

    return "";
  }

  Future<void> fetchProject() async {
    if (task.projectId == null || task.projectId!.isEmpty) return;

    try {
      project = await taskService.getProjectInfo(task.projectId!);

      updateTaskListInfoForCollab();
    } catch (e, s) {
      GPCoreTracker().appendError(
        'Flutter.task.fetchProject',
        data: {'error': e, 'stacktrace': s},
      );
      GPCoreTracker().sendLog(
        message: 'Flutter.task.fetchProject',
        trackerType: GPTrackerType.collabTask,
      );

      logDebug("ERROR when fetch project: ${e.toString()}");
    }
  }

  Future getProjectMembers(Task task) async {
    if (task.projectId?.isNotEmpty ?? false) {
      project = await taskService.getProjectInfo(task.projectId ?? "");
      CollabChecker.instance
          .setStatus(isOpenFromCollabGroup: project?.isNormalProject == false);

      projectMemberIds.clear();
      projectMemberIds.addAll(project?.memberIds ?? []);
    }
  }

  //

  // ---------- repo ---------- \
  Future<Task?> updateTaskTitle(
      {required String title, required String taskId}) async {
    isLoading.value = true;
    try {
      if (taskId.isEmpty) return null;
      final response =
          await taskService.editTaskTitle(taskId: taskId, title: title);
      isLoading.value = false;
      isTaskEdited = true;
      reloadTaskList();
      return response.data;
    } catch (e, s) {
      handleError(e, s);
    }

    return null;
  }

  Future<Task?> updateTaskProjectTaskList(
      {required String taskListId,
      required String taskId,
      required String sectionId}) async {
    isLoading.value = true;
    try {
      if (taskId.isEmpty) return null;
      final response = await taskService.editTaskProjectTaskList(
          taskId: taskId, taskListId: taskListId, sectionId: sectionId);
      isLoading.value = false;
      isTaskEdited = true;
      reloadTaskGeneral();
      return response.data;
    } catch (e, s) {
      handleError(e, s);
    }

    return null;
  }

  Future<Task?> updateTaskDescription({
    required String description,
    required String taskId,
    required bool isRtf,
  }) async {
    if (taskId.isEmpty) return null;
    isLoading.value = true;
    try {
      final response = await taskService.editTaskDescription(
          description: description, taskId: taskId, isRtf: isRtf);
      isLoading.value = false;
      isTaskEdited = true;
      return response.data;
    } catch (e, s) {
      handleError(e, s);
    }

    return null;
  }

  Future<Task?> updateTaskAttachmentFiles({
    required List<TaskAttachmentFile> attachmentFiles,
    required String taskId,
  }) async {
    if (taskId.isEmpty) return null;
    isLoading.value = true;
    try {
      final response = await taskService.editTaskAttachmentFiles(
          attachmentFiles: attachmentFiles, taskId: taskId);
      isLoading.value = false;
      isTaskEdited = true;
      return response.data;
    } catch (e, s) {
      handleError(e, s);
    }

    return null;
  }

  Future<Task?> updateTaskPriority({
    required TaskPriority priority,
    required String taskId,
  }) async {
    if (taskId.isEmpty) return null;
    isLoading.value = true;
    try {
      final response = await taskService.editTaskPriority(
          priority: priority.value, taskId: taskId);
      isLoading.value = false;
      isTaskEdited = true;

      updatedTask = response.data;
      reloadTaskList();
      return response.data;
    } catch (e, s) {
      handleError(e, s);
    }

    return null;
  }

  Future<void> updateTaskProgress({
    required TaskStatus progress,
    required String taskId,
  }) async {
    if (taskId.isEmpty) return;
    isLoading.value = true;
    try {
      final response = await taskService.editTaskProgress(
          progress: progress.value, taskId: taskId);
      isLoading.value = false;
      isTaskEdited = true;

      updatedTask = response.data;

      reloadTaskList();

      // function is update task (It not need start show ui)
      // --> update color ticker done
      getDetailTaskWaiting();
    } catch (e, s) {
      handleError(e, s);
      rethrow;
    }
  }

  Future<Task?> updateTaskDueDate({
    int? dueDateMillisecond,
    int? startDateMillisecond,
    required String taskId,
    String? rRule,
    RecurrenceSettings? recurrenceSettings,
  }) async {
    isLoading.value = true;
    if (taskId.isEmpty) {
      isLoading.value = false;
      return null;
    }
    try {
      final response = await taskService.editTaskDueDate(
          dueDate: dueDateMillisecond,
          taskId: taskId,
          startDate: startDateMillisecond,
          rRule: rRule,
          recurrenceSettings: recurrenceSettings);
      isLoading.value = false;
      isTaskEdited = true;
      reloadTaskList();
      return response.data;
    } catch (e, s) {
      if (e is ForbiddenException) {
        // Popup.instance.showAlert(message: LocaleKeys.task_dueDate403.tr);
        reloadData();
        // return null;
      }

      handleError(e, s);
    }

    return null;
  }

  Future<Task?> updateTaskAssigneesAndWatchers({
    required String taskId,
    List<Assignee>? assignees,
    List<Assignee>? watchers,
  }) async {
    if (taskId.isEmpty) return null;
    isLoading.value = true;
    try {
      List<GPUserModel>? assigneeList;
      List<GPUserModel>? watcherList;
      if (assignees != null) {
        assigneeList = assignees.map((e) => GPUserModel(id: e.id)).toList();
      }
      if (watchers != null) {
        watcherList = watchers.map((e) => GPUserModel(id: e.id)).toList();
      }
      final response = await taskService.editTaskAssigneeAndWatcher(
        assignees: assigneeList,
        watchers: watcherList,
        taskId: taskId,
      );
      isLoading.value = false;
      isTaskEdited = true;
      reloadTaskList();
      return response.data;
    } catch (e, s) {
      handleError(e, s);
    }

    return null;
  }

  /// update task (function Update task)
  /// tạo callback function khi update task ở task create (update khi click done subtask)
  TaskFunctionCallBack funcUpdateTask() {
    return TaskFunctionCallBack(onLoading: () {
      isLoading.value = true;
    }, onSucces: () {
      isLoading.value = false;
    }, onError: (e) {
      handleError(e, StackTrace.fromString(e.toString()));
    });
  }

  @override
  void onClose() {
    titleController.dispose();
    titleFocusNode.dispose();
    debounceTimer?.cancel();
    for (TextEditingController e in todoTitleControllers) {
      e.dispose();
    }
    for (FocusNode e in todoTitleFocusNodes) {
      e.dispose();
    }
  }

  void onFocusNodeEvent(index) async {
    if (!isTodoSubmit) {
      if (todoTitleFocusNodes[index].hasFocus) {
        isFocusTodoEdit.value = true;
        oldTodoTitle = todoTitleControllers[index].text.trim();
        update(["textField$index"]);
      } else {
        // Bỏ focus todo
        isFocusTodoEdit.value = false;
        if (todoTitleControllers[index].text.trim().isEmpty) {
          if (oldTodoTitle.isEmpty) {
            // Todo mới tạo chưa submit
            todoTask.value.removeAt(index);
            update(["checklist"]);
          } else {
            // Todo đã tạo từ trước
            todoTitleControllers[index].text = oldTodoTitle;
            update(["textField$index"]);
          }
        } else {
          if (oldTodoTitle != todoTitleControllers[index].text.trim()) {
            // Title thay đổi thì submit
            isTodoSubmit = true;
            onSubmitTextField(
                todoTitleControllers[index].text, index, reloadTaskList, false);
            isTodoSubmit = false;
          }
          update(["textField$index"]);
        }
      }
    }
  }

  void resetTodoTitle() {
    todoTitleControllers.clear();
    todoTitleFocusNodes.clear();
  }

  /// change all binding ui element with input task
  void _updateUIElementsWith(
      {required Task task, bool isShowError = true}) async {
    await _getSectionFromTask(task, isShowError: isShowError);
    titleController.text = task.title ?? '';
    taskDescription.value = task.description?.trim();
    if (task.attachmentFiles is List) {
      attachmentFiles.value = task.attachmentFiles!;
      update([TaskCreateScreenGetXKeys.listAttachments]);
    }
  }

  // get section infor from task
  Future _getSectionFromTask(Task task, {bool isShowError = true}) async {
    // get color for section
    if (task.sectionId == null || task.taskListId == null) {
      return;
    }
    SectionModel sectionTemp = await getSectionInfor(
        task.sectionId, task.taskListId,
        isShowError: isShowError);
    section.value = SectionModel(
        id: task.sectionId,
        name: task.sectionName,
        taskListId: task.taskListId,
        color: sectionTemp.color);
  }

  /// on fired when user tap on appbar finish button when state = [TaskCreationState.drafting]
  void onFinishButtonPressed() async {
    // make sure keyboard is dismissed
    dismissKeyboard();

    if (!isLoading.value) {
      _setNewTaskTodo();
      await _createTask();
    }
  }

  void updateSection(Task newTask) {
    task.sectionId = newTask.sectionId;
    task.sectionName = newTask.sectionName;
    updatedTask = task;
  }

  void _setNewTaskTodo() {
    _saveLastTodo();
    removeEmptyTodo();
    task.setSubTasks(todoTask.value);
  }

  void _saveLastTodo() {
    if (todoTask.value.isNotEmpty) {
      final lastIndex = todoTask.value.length - 1;
      if (todoTitleControllers[lastIndex].text.isNotEmpty &&
          todoTitleFocusNodes[lastIndex].hasFocus) {
        todoTask.value[lastIndex].title = todoTitleControllers[lastIndex].text;
      }
    }
  }

  Task? sourceForDuplicateTask;

  Future _fetchTaskDetail({bool? isLoadingScreen}) async {
    isFetchingTask.value = isLoadingScreen ?? true;
    isLoading.value = isLoadingScreen ?? true;
    try {
      var response = await apiService.getTaskInfo(task.id);
      task = response.data;
      todoTask.value = task.subTasks ?? [];
      isArchived.value = task.isArchived;
      task.assignees = response.data.assignees;
      taskListId.value = response.data.taskListId ?? '';

      await getProjectMembers(task);
      // update loading indicators
      isFetchingTask.value = false;
      isLoading.value = false;
      refreshController.refreshCompleted();
      refreshController.loadComplete();

      // Update UI
      _updateUIElementsWith(task: task);

      initTaskData(task);

      // check roles
      initRoles();

      inputCommentController?.mentionController
          .setProjectId(task.projectId ?? "");

      // has more button on appbar
      // if user can remove task or can go to task list (2 options on more button)
      // then set hasMoreButton = true
      // if (!haveRemovePermission(task, roles.value) && pushedFromTaskList) {
      //   hasMoreButton.value = false;
      // } else {
      //   hasMoreButton.value = true;
      // }
      //auto set show more
      hasMoreButton.value = true;
    } catch (e, s) {
      GPCoreTracker().appendError(
        'Flutter.task._fetchTaskDetail',
        data: {'error': e, 'stacktrace': s},
      );
      GPCoreTracker().sendLog(
        message: 'Flutter.task._fetchTaskDetail',
        trackerType: GPTrackerType.collabTask,
      );

      isFetchingTask.value = false;
      isLoading.value = false;
      logDebug(e);
      if (e is UnauthorisedException) {
        showAlertViewTask(LocaleKeys.validation_permTaskUpdateContent.tr,
            isDismissible: false);
      } else if (e is ForbiddenException) {
        // hiển thị màn emptyView
        showEmptyState.value = true;
      } else {
        showEmptyState.value = true;
      }
      // Deeplink.saveLog("$runtimeType", {
      //   "enpoint": "get task info",
      //   "result": "failed",
      //   "error": e.toString(),
      //   "task": task.id,
      //   "user": Constants.userId(),
      //   "workspace": Constants.workspaceId()
      // });
    }
  }

  Future<void> checkGetPermissionApi() async {
    openFromDeepLink = task.openTaskFromDeepLink ?? false;
    if (openFromDeepLink) {
      await getPermissionTaskApi();
    }
  }

  Future<void> getPermissionTaskApi() async {
    try {
      final res = await apiService.getPermissionTask(task.id);
      permissionsModel = res.data;
    } catch (e, s) {
      GPCoreTracker().appendError(
        'FluttFlutter.task.getPermissionTaskApi',
        data: {'error': e, 'stacktrace': s},
      );
      GPCoreTracker().sendLog(
        message: 'Flutter.task.getPermissionTaskApi',
        trackerType: GPTrackerType.task,
      );
    }
  }

  bool checkContainerUserActionTask() {
    if (task.projectId?.isNotEmpty ?? false) {
      return true;
    }
    if (openFromDeepLink) {
      return permissionsModel.createSubtask ||
          permissionsModel.deleteTask ||
          permissionsModel.updateTask;
    }

    return true;
  }

  Future<PermissionsModel> checkPermissionSubTask(String taskId) async {
    final data = await apiService.getPermissionTask(taskId);
    return data.data;
  }

  bool checkCanEditSubTask(Task taskParent, Task subTask) {
    if (taskParent.openTaskFromDeepLink ?? false) {
      return mapCheckPermissionSubTask[subTask.id]?.updateTask ?? false;
    }

    return taskParent.isArchived == false;
  }

  bool checkCanChangeTaskToSubTask(Task taskParent, Task subTask) {
    if (taskParent.openTaskFromDeepLink ?? false) {
      return mapCheckPermissionSubTask[subTask.id]?.updateTask ?? false;
    }

    return !taskParent.isArchived &&
        taskCreationState.value != TaskCreationState.initialized &&
        taskCreationState.value != TaskCreationState.drafting;
  }

  void fetchTaskDetails({bool? isLoadingScreen}) {
    // chỉ call khi get detail success, và chạy độc lập, không ảnh hưởng tới các luồng khác
    _fetchTaskDetail(isLoadingScreen: isLoadingScreen).then((value) async {
      todoTask.value = task.subTasks ?? [];
      countTodoTaskDone();
      await getListComment(refresh: true);
      if (isScrollToChecklist) await scrollToCheckList();
    });
  }

  void countTodoTaskDone() {
    numberTodoTaskDone = 0;
    for (Task task in todoTask.value) {
      if (task.isDoneStatus) numberTodoTaskDone++;
    }
    update(["checklist"]);
  }

  Future<void> reloadData() async {
    nextLink = '';
    fetchTaskDetails();
  }

  void reloadNewData() {
    _fetchTaskDetail().then((value) async {
      initTaskData(task);
      initRoles();
      await getListComment(refresh: true);
    });
  }

  Future<void> changeSubTaskToTask(
      Task task, int index, bool isBackScreen) async {
    try {
      if (task.id.isEmpty) {
        task = todoTask.value[index];
      }

      await apiService.changeSubTaskToTask(task.id);
      if (isBackScreen) {
        dismissKeyboard();
        confirmDiscardDraft(onConfirmed: () async {
          // tránh trường hợp auto focus
          dismissKeyboard();

          titleFocusNode.unfocus();

          await onBackPressed(needRedirectToTaskList: false);

          // tránh trường hợp auto focus
          dismissKeyboard();
        });
      } else {
        fetchTaskDetails();
      }

      reloadTaskGeneral();

      tasksControllerByParentTag()?.updateListByATask(task);

      Popup.instance.showSnackBar(
          type: SnackbarType.success,
          message: '${LocaleKeys.task_changed.tr} ',
          messageBold1: '“${task.title}” ',
          message2: LocaleKeys.task_changedJob.tr);
    } catch (e, s) {
      handleError(e, s);
    }
  }

  /// call api to create new task with all information that user had entered
  Future _createTask() async {
    // validate fields
    final Task taskForCreate = Task(id: '');
    isLoading.value = true;

    // unfocus textfields
    // titleFocusNode.unfocus();

    _setTaskData(taskForCreate);

    try {
      // update task list id ở luồng tạo task từ tin nhắn
      String taskListIdDefault = await createDefaultTaskListForCollabIfNeeded();
      if (taskListIdDefault.isNotEmpty) {
        taskForCreate.taskListId = taskListIdDefault;
      }

      final response = await apiService.createNewTask(taskForCreate);
      isLoading.value = false;
      final Task createdTask = response.data;
      task = createdTask;

      // mark need update to return to list task screen
      isTaskEdited = true;

      if (needActiveTaskCollab) {
        Deeplink.channel.invokeMethod('taskCreateResult', {
          "result": {"message": "collab_actived_task"}
        }).catchError((e, s) {
          GPCoreTracker().appendError(
            'Flutter.task._createTask',
            data: {'error': e, 'stacktrace': s},
          );
          GPCoreTracker().sendLog(
            message: 'Flutter.task._createTask',
            trackerType: GPTrackerType.task,
          );

          logDebug("needActiveTaskCollab taskCreateResult error -> $e");
        });
      }

      onBackPressed(needRedirectToTaskList: true);

      // Công việc “Task’s title - đây là đoạn tiêu đề có độ dài trung bình ...” đã được tạo.
      const maxlength =
          'Task’s title - đây là đoạn tiêu đề có độ dài trung bình'.length;
      var taskTitle = task.title ?? '';
      if (taskTitle.length > maxlength) {
        taskTitle = '${taskTitle.substring(0, maxlength)} ...';
      }
      if (taskCreateArgument?.isInCollabGroup == true) {
        //do nothing
      } else {
        Popup.instance.showSnackBar(
            type: SnackbarType.success,
            message: '${LocaleKeys.task_task_lowercase.tr} ',
            messageBold1: '“$taskTitle” ',
            message2: LocaleKeys.task_created.tr);
      }
    } catch (e, s) {
      handleError(e, s);
    }
  }

  void initTaskData(Task task, {bool ignoreStatus = false}) {
    _initSubControllersData();

    _initTaskListName();
  }

  void initRoles() {
    listUserCreator = SetDataAssignSubTask.getListSaveCreator(task);

    roles.value = detectUserTaskRole(task);
  }

  void _initSubControllersData({bool ignoreStatus = false}) {
    if (ignoreStatus) {
      //do nothing
    } else {
      assigneeController.initData(task, needCheckData: isFromTaskMessage);
      dueDateController.initData(task);
      priorityController.initData(task);
      statusController.initData(task.taskStatus, archived: task.isArchived);
    }
  }

  void _initTaskListName() {
    // task vô gia cư  ======> taskListName = "Công việc của tôi"
    taskListName.value =
        ((Get.arguments is! TaskCreateArgument && isHomeless) ||
                task.taskListName == null)
            ? LocaleKeys.task_my_task.tr
            : task.taskListName ?? "";
  }

  void _setTaskData(Task task) {
    task.setAssignee(assigneeController.executors);
    task.setWatcher(assigneeController.watchers);
    task.setDueDate(dueDateController.millisecondDueDate);
    task.setStartDate(dueDateController.millisecondStartDate);
    task.setPriority(priorityController.pickedTaskPriority);
    task.setStatus(statusController.taskStatus.value.value);
    task.setSubTasks(todoTask.value);
    task.setListTag(listTag.value);
    task.setRRule(dueDateController.rRule);
    task.setRecurrenceSettings(dueDateController.taskRecurrenceSettings);

    task.title = titleController.text;

    // description
    task.description = taskDescription.value ?? '';

    // attachment files
    task.attachmentFiles = attachmentFiles.value;

    task.contentRtf = isRtf;

    // task.projectId = taskCreateArgument?.projectId;
    task.taskListId = taskCreateArgument?.taskListId;
    task.sectionId = section.value.id;
  }

  void pickTaskList() async {
    final isNormalProject = project?.isNormalProject == false;

    Get.delete<ProjectListController>(tag: tag);
    Get.delete<ProjectTaskListController>(tag: tag);
    Get.delete<TaskFolderController>(tag: tag);

    final projectListController = Get.put(
        ProjectListController(
          tag: tag,
          parentTag: parentTag,
          taskProjectMode: TaskProjectMode.picker,
          visible: project?.visible ?? false,
          projectID: task.projectId,
        ),
        tag: tag);
    Get.put(
      ProjectTaskListController(
          parentTagStr: tag, taskProjectMode: TaskProjectMode.picker),
      tag: tag,
    );
    Get.put(
      TaskFolderController(
          parentTagStr: tag, taskProjectMode: TaskProjectMode.picker),
      tag: tag,
    );
    TaskList? result = await Get.bottomSheet(
        ProjectSearchScreen(
          controller: projectListController,
          itemSearch: ItemSearchModel(
            projectId: task.projectId,
            folderId: task.folderId,
            taskListId: task.taskListId,
          ),
          inCollabGroup: isNormalProject,
          projectCollab: isNormalProject ? project : null,
        ).paddingOnly(bottom: Utils.getBottomSheetPadding()),
        isScrollControlled: true,
        isDismissible: true,
        enableDrag: true);

    if (result != null) {
      taskListId.value = result.id;
      final hasTaskWaiting = (task.waitingTasks ?? []).isNotEmpty;
      final hasTaskBlocking = (task.blockingTasks ?? []).isNotEmpty;
      if (hasTaskWaiting || hasTaskBlocking) {
        await Popup.instance.showBottomSheet(
          ComponentConfirmBottomSheet(
            onConfirmed: () async {
              openCloneTask(
                task,
                idResultTask: result.id,
              );
            },
            title: LocaleKeys.task_longPress_clone.tr,
            confirmButtonTitle: LocaleKeys.task_longPress_clone.tr,
            message: LocaleKeys.task_contentDuplicateTaskDependency.tr,
            cancelButtonTitle: LocaleKeys.alert_cancel.tr,
            icon: "assets/images/ic16-line15-minus-circle.svg",
            colorIcon: GPColor.functionNegativePrimary,
          ),
        );
      } else {
        final isMoveToProject = task.projectId != result.projectId;
        taskListName.value = result.name;
        task.taskListId = result.id;
        task.projectId = result.projectId;
        task.folderId = result.folderId;
        taskCreateArgument?.taskListId = result.id;
        taskCreateArgument?.projectId = result.projectId;

        if (result.defaultSection?.id?.isNotEmpty == true) {
          task.sectionId = result.defaultSection?.id;
          task.sectionName = result.defaultSection?.name;
          section.value = result.defaultSection!;

          if (isTaskCreated) {
            updateTaskProjectTaskList(
                taskListId: result.id,
                taskId: task.id,
                sectionId: result.defaultSection?.id ?? "");
          } else if (isMoveToProject) {
            // Task tạo mới mà chọn project khác hiện tại thì xoá assignee, watcher, tag đã chọn đi
            _clearDataWhenChangeProject();
          }
        }
      }
    }
  }

  void _clearDataWhenChangeProject() {
    assigneeController.executors.clear();
    assigneeController.watchers.clear();
    listTag.value.clear();
    task.setAssignee([]);
    task.setListTag([]);
    task.setWatcher([]);
    update(['changeTag']);
  }

  /// to be called when user taps on back button
  Future<bool> onBackPressed({required bool needRedirectToTaskList}) async {
    dynamic result;
    if (isTaskEdited) {
      result = [TaskResultAction.refresh, task, isTaskEdited];
    }
    return _confirmCancelUploadingMedias(result, needRedirectToTaskList);
  }

  Future<bool> _confirmCancelUploadingMedias(
      dynamic result, bool needRedirectToTaskList) async {
    void _handleBack() {
      if (needRedirectToTaskList == false || sourceForDuplicateTask == null) {
        if (taskCreateArgument?.isInCollabGroup == true) {
          PlatformNavigator.pop(task.title);
        } else {
          Utils.back(result: result);
        }

        return;
      }
      bool pushFromNotification =
          Get.isRegistered<TaskGeneralController>() == false;

      bool isHomeless = sourceForDuplicateTask?.taskListId?.isEmpty == true ||
          sourceForDuplicateTask?.taskListId == null;
      if (pushFromNotification) {
        if (isHomeless) {
          Get.offNamed(
            TaskRouterName.taskMain,
          );
        } else {
          Get.offNamed(
            TaskRouterName.tasks,
            arguments: TaskScreenArguments(
              taskListId: sourceForDuplicateTask?.taskListId,
              taskListName: sourceForDuplicateTask?.taskListName,
              projectId: sourceForDuplicateTask?.projectId,
              isArchive: false,
            ),
          );
        }
      } else {
        Utils.back(result: result);
      }
    }

    bool _onBack() {
      if (inputCommentController == null) {
        _handleBack();
        return true;
      }

      if (inputCommentController!.isEmojiVisible.value == false) {
        _handleBack();
        return true;
      } else {
        hideEmojiKeyboard();
        return false;
      }
    }

    if (comments.isNotEmpty) {
      bool isUploadingOrError = false;
      for (var element in comments) {
        if (element.isUploadingOrError) {
          isUploadingOrError = true;
          await Popup.instance.showBottomSheet(
            ConfirmCancelUploadMedias(onConfirmed: () async {
              await inputCommentController?.commentMediasController
                  ?.cancelUploadMedias(
                      commentMediaLocals: element.commentMediaLocals);

              if (GetPlatform.isIOS) {
                Utils.back(result: result);
              } else {
                Get.back(result: result);
              }
            }),
          );
        }
      }

      if (!isUploadingOrError) {
        return _onBack();
      }
    } else {
      return _onBack();
    }

    return false;
  }

  void onMoreButtonTapped() {
    hideMentions();
    // make sure keyboard is dismissed
    dismissKeyboard();

    List<BottomSheetActionModel> actions = [];

    bool canCloneTask = openFromDeepLink && permissionsModel.updateTask;
    bool canDeleteTask = openFromDeepLink && permissionsModel.deleteTask;
    bool isNotArchived = !isArchived.value;

    if (canCloneTask) {
      actions.add(BottomSheetActionModel(
        displayName: LocaleKeys.task_longPress_clone.tr,
        iconAsset: "assets/images/ic24-line15-2square.svg",
        onClick: _cloneTask,
      ));
    } else {
      if (isNotArchived && !openFromDeepLink) {
        actions.add(BottomSheetActionModel(
          displayName: LocaleKeys.task_longPress_clone.tr,
          iconAsset: "assets/images/ic24-line15-2square.svg",
          onClick: _cloneTask,
        ));
      }
    }

    if ((task.depth ?? 0) < 1) {
      final archiveAction = BottomSheetActionModel(
          displayName: isArchived.value
              ? LocaleKeys.task_unarchive.tr
              : LocaleKeys.task_archive.tr,
          iconAsset: isArchived.value
              ? "assets/images/ic24-line15-archive-box-xmark.png"
              : "assets/images/ic24-line15-archive-box.png",
          onClick: _archiveTaskDialog);
      actions.add(archiveAction);
    }

    // if (roles.value.canEditRemoveTodoTask) {
    if (task.projectId != null && (task.depth ?? 0) < 1) {
      final dependencyAction = BottomSheetActionModel(
          displayName: LocaleKeys.task_dependencies.tr,
          iconAsset: "assets/images/ic24-line15-dependency-3square.svg",
          onClick: _goToTaskDependency);
      actions.add(dependencyAction);
    }
    // }

    final removeAction = BottomSheetActionModel(
        displayName: LocaleKeys.task_longPress_deleteTask.tr,
        iconAsset: "assets/images/ic24-line15-trash.png",
        textColor: GPColor.functionNegativePrimary,
        iconColor: GPColor.functionNegativePrimary,
        onClick: () {
          _removeTaskDialog(isSubTask: isSubTask);
        });

    // if (haveRemovePermission(task, roles.value)) {
    //   if (!isSubTask) {
    //     actions.add(removeAction);
    //   }
    // } else if (isSubTask &&
    //     (openFromDeepLink && permissionsModel.deleteTask || haveRemovePermissionSubTask(task, listUserCreator))) {
    //   actions.add(removeAction);
    // } else if (canDeleteTask) {
    //   actions.add(removeAction);
    // }

    // ToanNM rewrite logic
    if (!isSubTask) {
      if (haveRemovePermission(task, roles.value)) {
        actions.add(removeAction);
      }
    } else {
      if (canDeleteTask || haveRemovePermissionSubTask(task, listUserCreator)) {
        actions.add(removeAction);
      }
    }

    //auto show task list
    final action = BottomSheetActionModel(
        displayName: LocaleKeys.task_bottom_sheet_item_to_task_list.tr,
        iconAsset: "assets/images/ic24-line15-check-list.png",
        textColor: GPColor.contentPrimary,
        onClick: () {
          _redirectToTaskList(isSubTask);
        });
    actions.insert(0, action);

    if (!isArchived.value && (task.depth ?? 0) > 0) {
      final action = BottomSheetActionModel(
          displayName: LocaleKeys.task_changeSubTaskToTask.tr,
          iconAsset: "assets/images/ic24-line15-2arrow-rotate-circle.svg",
          onClick: () {
            closeBottomSheet();
            changeSubTaskToTask(task, 0, true);
          });
      actions.insert(actions.length - 1, action);
    }

    Popup.instance.showBottomSheet(
      BottomSheetActionWidget(data: actions),
    );
  }

  void _cloneTask({Task? taskItem}) {
    _closeBottomSheet();
    openCloneTask(taskItem ?? task);
  }

  Future openCloneTask(Task task, {String? idResultTask}) async {
    Get.offAndToNamed(TaskRouterName.middleTransition);
    duplicateTask(task.id, (newTask) {
      Future.delayed(const Duration(milliseconds: 400)).then((value) {
        Get.offNamed(
            RouterName.routeWithoutAnimation(TaskRouterName.taskCreate),
            arguments: newTask);

        reloadTaskList();
      });
    }, (error) => handleError(error, StackTrace.fromString(error.toString())),
        idProjectTaskDuplicate: idResultTask);
  }

  // /// check open new tab controller
  Future<void> openCreateTask(Task task, {int? index}) async {
    if (task.id.isEmpty && index != null) {
      task = todoTask.value[index];
    }

    dismissKeyboard();

    hideMentions();

    task.openTaskFromDeepLink = openFromDeepLink;
    Get.toNamed(
      TaskRouterName.subTask,
      arguments: task,
      preventDuplicates: false,
      parameters: {"parent_tag": tag},
    )?.then((value) async {
      if (value is List && value.first == TaskResultAction.refresh) {
        isTaskEdited = true;
      }
      await Future.delayed(const Duration(seconds: 1));
      fetchTaskDetails(isLoadingScreen: false);
    });
    // result format from taskCreate screen
    fetchTaskDetails(isLoadingScreen: false);
  }

  Future<void> _redirectToTaskList(bool isSubTask) async {
    closeBottomSheet();

    // TODO (toannm) _redirectToTaskList
    if (task.projectId != null) {
      await Get.toNamed(TaskRouterName.tasks,
          arguments: TaskScreenArguments(
            taskListId: task.taskListId,
            taskListName: task.taskListName,
            projectId: task.projectId,
          ));
    } else {
      Get.toNamed(TaskRouterName.taskMain);
    }
    // if (!isSubTask) {
    //   if (Get.isRegistered<TaskCreateController>()) {
    //     final task = Get.find<TaskCreateController>().task;
    //     if (task.projectId != null) {
    //       await Get.toNamed(TaskRouterName.tasks,
    //           arguments: TaskScreenArguments(
    //             taskListId: task.taskListId,
    //             taskListName: task.taskListName,
    //             projectId: task.projectId,
    //           ));
    //     } else {
    //       Get.toNamed(TaskRouterName.taskGeneral);
    //     }
    //   }
    // } else {
    //   if (Get.isRegistered<SubTaskController>(tag: getKeySubTask)) {
    //     final task = Get.find<SubTaskController>(tag: getKeySubTask).task;
    //     if (task.projectId != null) {
    //       await Get.toNamed(TaskRouterName.tasks,
    //           arguments: TaskScreenArguments(
    //             taskListId: task.taskListId,
    //             taskListName: task.taskListName,
    //             projectId: task.projectId,
    //           ));
    //     } else {
    //       Get.toNamed(TaskRouterName.taskGeneral);
    //     }
    //   }
    // }
  }

  void _removeTaskDialog({bool isSubTask = false}) async {
    _closeBottomSheet();
    await Popup.instance.showBottomSheet(
      ConfirmDeletingDialog(
        title:
            '${LocaleKeys.task_confirmDelete_title.tr} "${task.title ?? ''}"?',
        message: (task.waitingTasks ?? []).isNotEmpty
            ? LocaleKeys.task_contentDeleteTaskDependency.tr
            : LocaleKeys.task_longPress_confirmDeleting.tr,
        onConfirmed: () {
          _removeTask(isSubTask: isSubTask);
        },
        cancelButtonTitle: LocaleKeys.task_confirmDelete_cancel.tr,
      ),
    );
  }

  void _archiveTaskDialog() async {
    _closeBottomSheet();
    await Popup.instance.showBottomSheet(ConfirmArchiveDialog(
      data: task,
      isArchived: isArchived.value,
      onConfirmed: () {
        _closeBottomSheet();
        _archiveTask();
      },
      onCancel: closeBottomSheet,
    ));
  }

  Future _goToTaskDependency() async {
    _closeBottomSheet();
    final data =
        await Get.toNamed(TaskRouterName.taskDependencyScreen, arguments: task);
    if (data != null) {
      _closeBottomSheet();
      isTaskEdited = true;
      reloadData();
    }
  }

  void _removeTask({bool isSubTask = false}) async {
    try {
      isLoading.value = true;
      _closeBottomSheet();
      Response<dynamic> response = await apiService.deleteTask(task.id);
      Map<String, dynamic> result = response.data;
      isLoading.value = false;
      if (response.statusCode.isSuccess) {
        isTaskEdited = true;
        onBackPressed(needRedirectToTaskList: false);
        Popup.instance.showSnackBar(
            message: isSubTask
                ? LocaleKeys.task_longPress_subTaskDeleted.tr
                : LocaleKeys.task_longPress_deleted.tr,
            type: SnackbarType.success);
      } else {
        Popup.instance.showAlert(
            title: LocaleKeys.task_longPress_deleteTask.tr,
            message:
                result['message'] ?? LocaleKeys.task_longPress_accessDenied.tr);
      }
    } catch (error, s) {
      logDebug("get list error $error");
      handleError(error, s);
    }
  }

  bool showAlertPermissionIfNeeded(bool Function() condition) {
    if (!condition()) {
      // Popup.instance.showAlert(
      //     title: LocaleKeys.validation_permTaskUpdateTitle.tr,
      //     message: LocaleKeys.validation_permTaskUpdateTitle.tr);
      return true;
    }

    return false;
  }

  bool isProjectMember() {
    return projectMemberIds.contains(int.parse(Constants.userId()));
  }

  void setValueOverIndexTag(int index) {
    debounceTimer?.cancel();
    debounceTimer = Timer(const Duration(milliseconds: 500), () {
      valueOverIndexTag = index;
      update(['changeTag']);
    });
  }
}

class TaskCreateControllerConst {
  static String taskTitleKey = "taskTitle";
  static String taskCommentsKey = "taskComments";
}

extension PopupFunction on TaskCreateController {
  void showAlertViewTask(String message, {isDismissible = true}) {
    Popup.instance.showAlert(
        title: LocaleKeys.validation_permTaskViewTitle.tr,
        message: message,
        doneBtn: BgSecondaryButton(LocaleKeys.task_understand.tr, () {
          // dismiss alert
          Get.back();
          // close createScreen
          Utils.back();
        }),
        isDismissible: isDismissible);
  }

  void _closeBottomSheet() {
    if (Get.isBottomSheetOpen ?? false) {
      Get.back();
    }
  }

  Future<void> _archiveTask() async {
    bool isActionArchive =
        task.isArchived == false; // the another is actionUnArchive
    bool isActionUnArchive = !isActionArchive;
    try {
      var response = await apiService.archiveTask([task.id], isActionArchive);

      if (response != null) {
        Utils.back();

        reloadTaskList();

        if (isActionUnArchive) {
          if (Get.isRegistered<ProjectListController>(tag: parentTag)) {
            Get.find<ProjectListController>(tag: parentTag).reload();
          }
        }

        Popup.instance.showSnackBar(
            message: isActionArchive
                ? LocaleKeys.archive_archiveTaskSuccess.tr
                : LocaleKeys.archive_unarchiveTaskSuccess.tr,
            type: SnackbarType.success);
      }

      return response;
    } catch (error, s) {
      handleError(error, s);
      logDebug("archiveFolder error $error");
    }
  }
}
