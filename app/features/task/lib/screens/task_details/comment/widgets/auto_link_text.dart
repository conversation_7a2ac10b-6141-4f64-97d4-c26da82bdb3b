import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_task/models/comment/comment.dart';

typedef LinkTapCallback = void Function(String);

RegExp _phoneRegExp = RegExp('[0-9]{8,11}');
RegExp _emailRegExp = RegExp(
    r"[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*");
RegExp _linksRegExp = RegExp(
    r"(https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|www\.[a-zA-Z0-9][a-zA-Z0-9-]+[a-zA-Z0-9]\.[^\s]{2,}|https?:\/\/(?:www\.|(?!www))[a-zA-Z0-9]+\.[^\s]{2,}|www\.[a-zA-Z0-9]+\.[^\s]{2,})");

class AutolinkText extends StatelessWidget {
  final String text;
  final List<Mentions>? mentionList;
  final LinkTapCallback? onWebLinkTap, onPhoneTap, onEmailTap;
  final Function(String)? onUserTap;
  final TextStyle textStyle, linkStyle;
  final TextStyle? mentionStyle;
  final bool humanize;

  const AutolinkText(
      {super.key,
      required this.text,
      required this.textStyle,
      required this.linkStyle,
      this.mentionStyle,
      this.mentionList,
      this.onWebLinkTap,
      this.onEmailTap,
      this.onPhoneTap,
      this.onUserTap,
      this.humanize = false});

  _onLinkTap(String link, _MatchType type, String? mentionId) {
    switch (type) {
      case _MatchType.phone:
        if (onPhoneTap != null) {
          onPhoneTap!(link);
        }
        break;
      case _MatchType.email:
        if (onEmailTap != null) {
          onEmailTap!(link);
        }
        break;
      case _MatchType.link:
        if (onWebLinkTap != null) {
          onWebLinkTap!(link);
        }
        break;
      case _MatchType.user:
        if (onUserTap != null) {
          onUserTap!(mentionId ?? '');
        }
        break;
      case _MatchType.none:
        break;
    }
  }

  String _getTypes() {
    String types = '';
    if (onWebLinkTap != null) types += 'web';
    if (onEmailTap != null) types += 'email';
    if (onPhoneTap != null) types += 'phone';
    if (mentionList != null) types += 'user';
    return types;
  }

  List<TextSpan> _buildTextSpans() {
    return _findMatches(
            text: text,
            types: _getTypes(),
            humanize: humanize,
            mentionList: mentionList)
        .map((match) {
      if (match.type == _MatchType.none) {
        return TextSpan(text: match.text, style: textStyle);
      }
      final recognizer = TapGestureRecognizer();
      recognizer.onTap =
          () => _onLinkTap(match.text, match.type, match.mentionId);
      return TextSpan(
          text: match.text,
          style: match.type != _MatchType.user
              ? linkStyle
              : mentionStyle ?? textStyle,
          recognizer: recognizer);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return RichText(
      text: TextSpan(children: _buildTextSpans()),
    );
  }
}

enum _MatchType { phone, email, link, user, none }

class _MatchedString {
  final _MatchType type;
  final String text;
  //Using only for _MatchType.user
  final String? mentionId;

  _MatchedString({required this.text, required this.type, this.mentionId});

  @override
  String toString() {
    return text;
  }
}

List<_MatchedString> _findMatches(
    {required String text,
    required String types,
    bool humanize = false,
    List<Mentions>? mentionList}) {
  List<_MatchedString> matched = [
    _MatchedString(type: _MatchType.none, text: text)
  ];

  if (types.contains('user')) {
    List<_MatchedString> newMatched = [];
    for (_MatchedString matchedBefore in matched) {
      if (matchedBefore.type == _MatchType.none) {
        newMatched.addAll(_findMentionUsers(text, mentionList ?? []));
      } else {
        newMatched.add(matchedBefore);
      }
    }
    matched = newMatched;
  }

  if (types.contains('phone')) {
    List<_MatchedString> newMatched = [];
    for (_MatchedString matchedBefore in matched) {
      if (matchedBefore.type == _MatchType.none) {
        newMatched
            .addAll(_findLinksByType(matchedBefore.text, _MatchType.phone));
      } else {
        newMatched.add(matchedBefore);
      }
    }
    matched = newMatched;
  }

  if (types.contains('email')) {
    List<_MatchedString> newMatched = [];
    for (_MatchedString matchedBefore in matched) {
      if (matchedBefore.type == _MatchType.none) {
        newMatched
            .addAll(_findLinksByType(matchedBefore.text, _MatchType.email));
      } else {
        newMatched.add(matchedBefore);
      }
    }
    matched = newMatched;
  }

  if (types.contains('web')) {
    List<_MatchedString> newMatched = [];
    for (_MatchedString matchedBefore in matched) {
      if (matchedBefore.type == _MatchType.none) {
        final webMatches =
            _findLinksByType(matchedBefore.text, _MatchType.link);
        for (_MatchedString webMatch in webMatches) {
          if (webMatch.type == _MatchType.link &&
              (webMatch.text.startsWith('http://') ||
                  webMatch.text.startsWith('https://')) &&
              humanize) {
            newMatched.add(_MatchedString(
                text: webMatch.text
                    .substring(webMatch.text.startsWith('http://') ? 7 : 8),
                type: _MatchType.link));
          } else {
            newMatched.add(webMatch);
          }
        }
      } else {
        newMatched.add(matchedBefore);
      }
    }
    matched = newMatched;
  }

  return matched;
}

RegExp? _getRegExpByType(_MatchType type) {
  switch (type) {
    case _MatchType.phone:
      return _phoneRegExp;
    case _MatchType.email:
      return _emailRegExp;
    case _MatchType.link:
      return _linksRegExp;
    default:
      return null;
  }
}

List<_MatchedString> _findLinksByType(String text, _MatchType type) {
  List<_MatchedString> output = [];
  final matches = _getRegExpByType(type)?.allMatches(text);
  int endOfMatch = 0;
  if (matches != null) {
    for (Match match in matches) {
      final before = text.substring(endOfMatch, match.start);
      if (before.isNotEmpty) {
        output.add(_MatchedString(text: before, type: _MatchType.none));
      }
      final lastCharacterIndex =
          text[match.end - 1] == ' ' ? match.end - 1 : match.end;
      output.add(_MatchedString(
          type: type, text: text.substring(match.start, lastCharacterIndex)));
      endOfMatch = lastCharacterIndex;
    }
  }
  final endOfText = text.substring(endOfMatch);
  if (endOfText.isNotEmpty) {
    output.add(_MatchedString(text: endOfText, type: _MatchType.none));
  }
  return output;
}

List<_MatchedString> _findMentionUsers(String text, List<Mentions> mentions) {
  String temp = '';
  List<_MatchedString> output = [];
  for (var element in mentions) {
    final int start = element.offset;
    final int end = element.length;
    if (end > start) {
      final String subStr = text.substring(start, end);

      if (start > temp.length) {
        _MatchedString notHighLightText =
            _MatchedString(text: subStr, type: _MatchType.none);
        output.add(notHighLightText);
        temp += subStr;
      }

      try {
        _MatchedString highLightText = _MatchedString(
            text: subStr, type: _MatchType.user, mentionId: element.mentionId);
        output.add(highLightText);
        temp += subStr;
      } catch (e) {
        logDebug(e);
      }
    }
  }

  if (temp.length < text.length) {
    _MatchedString lastText = _MatchedString(
        text: text.substring(temp.length, text.length), type: _MatchType.none);
    output.add(lastText);
  }
  return output;
}
