import 'package:flutter/material.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:gp_core/core.dart';
import 'package:gp_markdown/markdown.dart' as md;

abstract class GPDescriptionController extends BaseController
    with GetSingleTickerProviderStateMixin {
  /// task that passed from previous screen

  /// ui binding
  // var textEditingController = TextEditingController();

  // final FocusNode focusNode = FocusNode();

  GPDescriptionController(super.gpConnection) {
    _initRtfController();
  }
  // var displayText = ''.obs;

  /// to store initial description value and check whether user changes the description or not
  /// if don't have any change so that don't need to call update task api
  // var _initialDescription = '';

  /// to change editting mode
  // var isEditable = true.obs;

  /// to make api request
  // final _apiService = TaskAPI();

  late final RtfController rtfController;

  String contentStr();

  void onSave();

  QuillController? editorController;
  Document doc = Document();

  @override
  void onInit() {
    super.onInit();
    // textEditingController.text = task.description ?? '';

    final md.Document document = md.Document(
        extensionSet: md.ExtensionSet.gitHubFlavored, encodeHtml: false);
    final content = contentStr();

    if (content.isNotEmpty) {
      final delta =
          MarkdownToDelta(markdownDocument: document, softLineBreak: true)
              .convert(content);
      doc = Document.fromDelta(delta);
    }

    editorController = QuillController(
        document: doc,
        selection: const TextSelection.collapsed(offset: 0),
        keepStyleOnNewLine: true);
    // _fetchTaskDetails(task.id);

    // const testText = '@hehehee @thainh https://cretezy.com Chức năng 
https://www.figma.com/file

Sau ';
    // _initialDescription = task.description ?? '';

    // bind things
    // _bind();
  }

  @override
  void onClose() {
    // textEditingController.dispose();
  }

  // void _bind() {
  // textEditingController.addListener(() {
  //   displayText.value = textEditingController.text;
  // });
  // }

  // void _fetchTaskDetails(String taskId) async {
  //   // TODO: SHOW LOADING
  //   isLoading.value = true;
  //   try {
  //     var response = await _apiService.getTaskInfo(taskId);
  //     isLoading.value = false;
  //     task = response.data;
  //     // Update UI and cache
  //     _initialDescription = task.description ?? '';
  //     textEditingController.text = task.description ?? '';
  //   } catch (e) {
  //     isLoading.value = false;
  //     // TODO: HANDLE ERROR
  //     rethrow;
  //   }
  // }

  void _initRtfController() {
    rtfController = RtfController();
  }

  void onElementTapped(String element) {
    debugPrint(element);
  }

  // void _editTaskAPI(Task task) async {
  //   isLoading.value = true;
  //   try {
  //     await _apiService.editTask(task).whenComplete(() {
  //       isLoading.value = false;
  //     }).then((value) async {
  //       Get.snackbar(LocaleKeys.alert_alert, "Task has been updated!",
  //           duration: 1.seconds);
  //       await Future.delayed(1.seconds);
  //       Get.back(result: task.description);
  //     });
  //   } catch (e) {
  //     handleError(e);
  //   }
  // }
}
