import 'package:gp_feat_task/models/task/task.dart';
import 'package:gp_feat_task/screens/components/task/filter_status_subtask/selection_with_switch_button_widget.dart';

extension CheckStatus on List<BottomSheetWithSwitchModel>? {
  /// ## Query parameters (api)
  ///
  /// - <PERSON> phép hiển thị subtask:
  /// <br>-----> `"include_subtask"` : `true` (ẩn/hiện subtask)"
  /// - <PERSON> phép hiển thị theo trạng thái công việc:
  /// <br>-----> `"status"` : `"0, 1, 2"` (chuỗi các trạng thái (`TaskStatus.value`), cách nhau bởi dấu phảy)"

  Map<String, dynamic> toJson() {
    Map<String, dynamic> res = {};
    if (this != null) {
      for (var e in this!) {
        res.addAll({e.getKey: e.getValue});
      }
    }
    return res;
  }

  void setStatus(TaskFilterStatus taskFilterStatus, bool isShow) {
    if (this != null) {
      for (var e in this!) {
        if (e.value == taskFilterStatus) {
          e.updateStatus(isShow);
          break;
        }
      }
    }
  }
}

extension FilterTaskDone on BottomSheetWithSwitchModel {
  String filterListTask(bool ishow) {
    // mặc định chỉ show các task chưa hoàn thành
    List<TaskStatus> listTask = [TaskStatus.inprogress, TaskStatus.todo];
    if (ishow) {
      listTask.add(TaskStatus.done);
    }
    return listTask.map((e) => e.value.toString()).join(",");
  }

  void updateStatus(bool status) {
    this.status = status;
  }
}
