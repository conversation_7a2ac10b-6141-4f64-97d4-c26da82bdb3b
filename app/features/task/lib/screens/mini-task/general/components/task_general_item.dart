import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_task/models/task/task.dart';
import 'package:gp_feat_task/models/task/user_role.dart';
import 'package:gp_feat_task/routes/router_name.dart';
import 'package:gp_feat_task/screens/mini-task/folder/components/confirm_archive_popup.dart';
import 'package:gp_feat_task/screens/mini-task/general/components/controller/task_item_controller.dart';
import 'package:gp_feat_task/screens/mini-task/general/components/task_item.dart';
import 'package:gp_feat_task/screens/mini-task/general/model/task_general_header_model.dart';
import 'package:gp_feat_task/screens/mini-task/general/task_general_controller.dart';
import 'package:gp_feat_task/screens/mini-task/general/task_general_list_controller.dart';
import 'package:gp_feat_task/screens/tasks/tasks_screen.dart';
import 'package:gp_feat_task/utils/extentions/response_status_checker_ext.dart';

// ignore: must_be_immutable
class TaskGeneralItem extends StatelessWidget {
  TaskGeneralItem({
    super.key,
    required this.task,
    required this.taskHeaderModel,
    required this.taskGeneralController,
  });

  Task task;

  final TaskGeneralHeaderModel taskHeaderModel;

  final TaskGeneralController taskGeneralController;

  TaskGeneralListController get controller =>
      taskGeneralController.listControllerByHeaderModel(taskHeaderModel);

  Future openATask(Task task) async {
    final result =
        await Get.toNamed(TaskRouterName.taskCreate, arguments: task);
    // result format from taskCreate screen
    // Get.back(result: [TaskResultAction.refresh, task, _needUpdateTaskList]);
    if (result is List) {
      var action = result.first;
      if (action == TaskResultAction.refresh) {
        taskGeneralController.updateListByATask(task);
      }
    }
  }

  final RxBool rxCompleted = false.obs;

  final ExpandableController expandableController =
      ExpandableController(initialExpanded: true);

  @override
  Widget build(BuildContext context) {
    var assignees = task.getAssignees();
    /*
      Bọc Material để tránh trường hợp render bị mất backgroundColor
    */
    // if (expandableController.value == task.isCompleted) {
    //   expandableController.value = false;
    // }
    return Material(
      color: GPColor.bgSecondary,
      child: ExpandableNotifier(
        controller: expandableController,
        child: Expandable(
          collapsed:
              Container(width: double.infinity, color: Colors.white, height: 0),
          expanded: Material(
            color: GPColor.contentInversePrimary,
            child: GestureDetector(
              onTap: () => openATask(task),
              onLongPress: _onLongPress,
              child: GetBuilder<TaskItemController>(
                assignId: true,
                global: false,
                init: TaskItemController()..initData(task),
                tag: task.id,
                builder: (taskItemController) {
                  return TaskItem(
                    assignees: assignees,
                    task: task,
                    taskHeaderModel: taskHeaderModel,
                    taskItemController: taskItemController,
                    taskGeneralController: taskGeneralController,
                  );
                },
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future _onLongPress() async {
    Get.closeCurrentSnackbar();
    await Popup.instance.showBottomSheet(
      BottomSheetActionWidget(data: actions),
    );
  }

  List<BottomSheetActionModel> get actions {
    bool isArchived = task.isArchived;
    var list = [
      BottomSheetActionModel(
          displayName: LocaleKeys.task_longPress_view.tr,
          iconAsset: "assets/images/ic24-line15-feed.png",
          onClick: _openTaskBottomSheet),
      BottomSheetActionModel(
          displayName: LocaleKeys.task_longPress_copyUrl.tr,
          iconAsset: "assets/images/ic24-line15-link.png",
          onClick: _copyTaskLink),
      BottomSheetActionModel(
          displayName: LocaleKeys.task_longPress_clone.tr,
          iconAsset: "assets/images/ic24-line15-2square.svg",
          onClick: _cloneTask),
    ];

    List<UserTaskRole> roles = controller.detectUserTaskRole(task);

    if ((task.depth ?? 0) < 1) {
      final archiveAction = BottomSheetActionModel(
        displayName: isArchived
            ? LocaleKeys.task_unarchive.tr
            : LocaleKeys.task_archive.tr,
        iconAsset: isArchived
            ? "assets/images/ic24-line15-archive-box-xmark.png"
            : "assets/images/ic24-line15-archive-box.png",
        onClick: archiveTaskDialog,
      );
      list.add(archiveAction);
    }

    if (task.projectId != null && (task.depth ?? 0) < 1) {
      final dependencyAction = BottomSheetActionModel(
          displayName: LocaleKeys.task_dependencies.tr,
          iconAsset: "assets/images/ic24-line15-dependency-3square.svg",
          onClick: _dependencyTask);
      list.add(dependencyAction);
    }

    if (roles.canEditRemoveTask || task.projectId != null) {
      final deleteAction = BottomSheetActionModel(
          displayName: LocaleKeys.task_longPress_deleteTask.tr,
          iconAsset: "assets/images/ic24-line15-trash.png",
          textColor: GPColor.functionNegativePrimary,
          iconColor: GPColor.functionNegativePrimary,
          onClick: _deleteTask);
      list.add(deleteAction);
    }

    if (!isArchived && (task.depth ?? 0) > 0) {
      list.insert(
          list.length - 1,
          BottomSheetActionModel(
              displayName: LocaleKeys.task_changeSubTaskToTask.tr,
              iconAsset: "assets/images/ic24-line15-2arrow-rotate-circle.svg",
              onClick: () {
                _onClickChangeSubTask();
              }));
    }

    return list;
  }

  void archiveTaskDialog() async {
    if (Get.isSnackbarOpen) {
      await Future.delayed(const Duration(milliseconds: 300));
    }
    _closeBottomSheet();
    await Popup.instance.showBottomSheet(ConfirmArchiveDialog(
      data: task,
      isArchived: task.isArchived,
      onConfirmed: () async {
        _closeBottomSheet();
        controller.archiveTask(task);
      },
      onCancel: _closeBottomSheet,
    ));
  }

  Future<void> _onClickChangeSubTask() async {
    _closeBottomSheet();
    await controller.changeSubTaskToTask(task);
    // BE mất 1 khoảng thời gian mới update lại data.
    await Future.delayed(const Duration(milliseconds: 300));
    taskGeneralController.updateListByATask(task);
  }

  // ---------- BottomSheet commands --------- \
  void _openTaskBottomSheet() {
    _closeBottomSheet();
    openATask(task);
  }

  void _copyTaskLink() {
    _closeBottomSheet();

    Clipboard.setData(ClipboardData(
            text: task.taskUrl ?? "${Constants.appDomain}/task/${task.id}"))
        .then((_) {
      Popup.instance.showSnackBar(
          message: LocaleKeys.task_longPress_copied.tr,
          type: SnackbarType.success);
    });
  }

  Future<void> _cloneTask() async {
    if (Get.isSnackbarOpen) {
      await Future.delayed(const Duration(milliseconds: 300));
    }
    _closeBottomSheet();
    controller.duplicateTask(
      task.id,
      (newTask) {
        openATask(newTask);

        taskGeneralController.reload();
      },
      (error) {
        controller.handleError(error, StackTrace.fromString(error.toString()));
      },
    );
  }

  void _dependencyTask() async {
    if (Get.isSnackbarOpen) {
      await Future.delayed(const Duration(milliseconds: 300));
    }
    _closeBottomSheet();
    final data =
        await Get.toNamed(TaskRouterName.taskDependencyScreen, arguments: task);
    if (data != null) {
      taskGeneralController.updateListByATask(task);
    }
  }

  void _deleteTask() async {
    if (Get.isSnackbarOpen) {
      await Future.delayed(const Duration(milliseconds: 300));
    }
    _closeBottomSheet();
    await Popup.instance.showBottomSheet(
      _ConfirmDeletingTaskDialog(
          task: task,
          taskHeaderModel: taskHeaderModel,
          controller: controller,
          isTaskDependency: (task.waitingTasks?.length ?? 0) > 0 ||
              (task.blockingTasks?.length ?? 0) > 0),
    );
  }

  void _closeBottomSheet() {
    if (Get.isBottomSheetOpen ?? false) {
      Get.back();
    }
  }
}

class _ConfirmDeletingTaskDialog extends StatelessWidget {
  const _ConfirmDeletingTaskDialog({
    required this.task,
    required this.taskHeaderModel,
    required this.controller,
    this.isTaskDependency = false,
  });

  final TaskGeneralHeaderModel taskHeaderModel;

  final Task task;

  final TaskGeneralListController controller;

  final bool isTaskDependency;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      top: false,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SvgWidget(
            isTaskDependency
                ? "assets/images/ic24-line15-dependency.svg"
                : "assets/images/ic24-line15-trash.png",
            width: 54,
            height: 59,
            color: GPColor.functionNegativePrimary,
          ),
          Text(
            isTaskDependency
                ? LocaleKeys.task_deleteDependencies.tr
                : "${LocaleKeys.task_confirmDelete_title.tr} \"${task.title}\"?",
            style: textStyle(GPTypography.headingLarge),
          ),
          const SizedBox(height: 8),
          Text(
            isTaskDependency
                ? LocaleKeys.task_contentDeleteTaskDependency.tr
                : LocaleKeys.task_longPress_confirmDeleting.tr,
            style: textStyle(GPTypography.bodyLarge),
          ),
          const SizedBox(height: 24),
          Row(
            children: [
              Expanded(
                child: TextButton(
                    onPressed: _closeBottomSheet,
                    style: TextButton.styleFrom(
                        backgroundColor: GPColor.bgSecondary,
                        shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.all(Radius.circular(8))),
                        padding: const EdgeInsets.all(12)),
                    child: Text(
                        isTaskDependency
                            ? LocaleKeys.alert_later.tr
                            : LocaleKeys.task_confirmDelete_cancel.tr,
                        style: textStyle(GPTypography.headingMedium))),
              ),
              const SizedBox(
                width: 12,
              ),
              Expanded(
                child: TextButton(
                    onPressed: _deleteTask,
                    style: TextButton.styleFrom(
                        backgroundColor: GPColor.functionNegativeSecondary,
                        shape: const RoundedRectangleBorder(
                            borderRadius: BorderRadius.all(Radius.circular(8))),
                        padding: const EdgeInsets.all(12)),
                    child: Text(LocaleKeys.task_longPress_delete.tr,
                        style: textStyle(GPTypography.headingMedium)
                            ?.mergeColor(GPColor.functionNegativePrimary))),
              )
            ],
          )
        ],
      ).paddingAll(24),
    );
  }

  void _deleteTask() async {
    _closeBottomSheet();
    final Response<dynamic> response = await controller.deleteTask(task.id);
    final result = response.data;
    if (response.statusCode.isSuccess == true) {
      controller.removeTask(task);
      Popup.instance.showSnackBar(
          message: LocaleKeys.task_longPress_deleted.tr,
          type: SnackbarType.success);
    } else {
      Popup.instance.showAlert(
          title: LocaleKeys.task_longPress_deleteTask.tr,
          message:
              result['message'] ?? LocaleKeys.task_longPress_accessDenied.tr);
    }
  }

  void _closeBottomSheet() {
    if (Get.isBottomSheetOpen ?? false) {
      Get.back();
    }
  }
}
