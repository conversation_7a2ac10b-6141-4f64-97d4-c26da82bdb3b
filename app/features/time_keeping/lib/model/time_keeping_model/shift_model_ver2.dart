// ignore_for_file: non_nullable_equals_parameter

import 'package:gp_core/core.dart';
import 'package:gp_feat_time_keeping/model/approval/approval_request.dart';
import 'package:gp_feat_time_keeping/model/approval/explanation_request.dart';
import 'package:gp_feat_time_keeping/model/time_keeping_model/attendance_model.dart';
import 'package:gp_feat_time_keeping/model/time_keeping_model/fixed_working_shift.dart';
import 'package:gp_feat_time_keeping/model/time_keeping_model/flexible_working_shift.dart';

import 'enum/enum.dart';

part 'shift_model_ver2.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class ShiftModelVer2 {
  @JsonKey(defaultValue: '')
  final String id;

  @JsonKey(defaultValue: '--')
  String name;

  @Json<PERSON>ey(
    name: 'type',
    unknownEnumValue: JsonKey.nullForUndefinedEnumValue,
  )
  final ShiftType? shiftType;

  final FixedWorkingShift? fixedWorkingHour;

  final FlexibleWorkingShift? flexibleWorkingHour;

  List<AttendanceModel?>? attendances;

  List<ApprovalRequest>? approvalRequests;

  List<ExplanationRequest>? explanationRequests;

  final bool isOverTime;

  @JsonKey(name: 'valid_type')
  ShiftValidType shiftValidType;

  String? code;
  double? totalWorkingHours;

  // List<ApprovalRequest>? approvalRequests;
  bool? isShiftFromYesterDay = false;

  String? shiftOnDateId = '';

  bool? isExpanded = false;

  ShiftModelVer2({
    required this.id,
    this.name = '--',
    this.shiftValidType = ShiftValidType.future,
    this.shiftType,
    this.fixedWorkingHour,
    this.flexibleWorkingHour,
    this.attendances,
    this.code,
    this.totalWorkingHours,
    this.approvalRequests,
    this.isOverTime = false,
  }) {
    if (name.isEmpty && id.isEmpty) {
      name = LocaleKeys.timeKeeping_noShiftName.tr;
    }
  }

  bool get isNoShift =>
      id.isEmpty && approvalRequests == null ||
      approvalRequests?.isEmpty == true;

  bool isAllowedCheckInFromPreviousDay(DateTime date) {
    final shiftEnableCheckIn = fixedWorkingHour?.getTimeEnableCheckIn(date);
    final shiftStartTime = fixedWorkingHour?.getStartTime(date);

    return shiftEnableCheckIn?.isAfter(shiftStartTime ?? DateTime.now()) ??
        false;
  }

  factory ShiftModelVer2.fromJson(Map<String, dynamic> json) =>
      _$ShiftModelVer2FromJson(json);

  Map<String, dynamic> toJson() => _$ShiftModelVer2ToJson(this);

  // ShiftType get getShiftType {
  //   return ShiftType.values.firstWhere((element) => element.value == type);
  // }

  /// E.g: "09:15 AM - 03:15 PM""
  String get shiftDurationRangeText {
    String fixed = fixedWorkingHour?.showDurationTime ?? '';
    // working hours = 4.5 => "4 giờ 30 phút"
    // working hours = 5.33333333 => "5 giờ 20 phút"
    num tempTime = (flexibleWorkingHour?.workingHours ?? 0) * 60;
    if (isNoShift && tempTime == 0) return '--';

    String flex = "${tempTime ~/ 60} ${LocaleKeys.timeKeeping_flexHours.tr}";
    if (tempTime % 60 != 0) {
      flex += ' ${(tempTime % 60).round()} ${LocaleKeys.timeKeeping_minute.tr}';
    }

    return shiftType == ShiftType.fixed ? fixed : flex;
  }

  String formatStartTimeFromCurrentTime(DateTime currentTime) {
    /*
      Ca cố định:       Bắt đầu ca
      Ca linh hoạt:     Bắt đầu ca
      Khi không có ca:  Chấm công vào
      https://docs.google.com/spreadsheets/d/13dhIR6FTyAauLNiUoE5FVRiqBI02fLZm/edit#gid=120840676
    */
    final bool isFixed = shiftType == ShiftType.fixed;
    String strStart = isNoShift
        ? LocaleKeys.timeKeeping_checkIn.tr
        : LocaleKeys.timeKeeping_startTime.tr;

    final String displayStartTime =
        id.isEmpty && shiftValidType == ShiftValidType.hasRequest
            ? ''
            : fixedWorkingHour?.displayStartTime ?? '';
    strStart += isFixed ? ': $displayStartTime' : '';
    if (isShiftFromYesterDay ?? false) {
      strStart +=
          ' - ${DateFormat('dd/MM/yyyy').format(currentTime.subtract(const Duration(days: 1)))}';
    } else if (isShiftToTomorrow) {
      strStart += ' - ${DateFormat('dd/MM/yyyy').format(currentTime)}';
    }

    return strStart;
  }

  /// E.g: "Được phép chấm công từ: 12:00 AM""
  String get limitedStartTimeText {
    return shiftType == ShiftType.fixed
        ? fixedWorkingHour != null
            ? '${LocaleKeys.timeKeeping_ableToCheckIn.tr}: ${fixedWorkingHour?.displayEnableCheckIn}'
            : ''
        : flexibleWorkingHour?.checkInAt != null
            ? '${LocaleKeys.timeKeeping_checkInBefore.tr}: ${flexibleWorkingHour?.checkInAt}'
            : '';
  }

  String formatEndTimeFromCurrentTime(DateTime currentTime) {
    /*
      Ca cố định:       Kết thúc ca
      Ca linh hoạt:     Kết thúc ca
      Khi không có ca:  Chấm công ra
    */
    final bool isFixed = shiftType == ShiftType.fixed;
    String strEnd = isNoShift
        ? LocaleKeys.timeKeeping_checkOut.tr
        : LocaleKeys.timeKeeping_endTime.tr;

    final String displayEndTime =
        id.isEmpty && shiftValidType == ShiftValidType.hasRequest
            ? ''
            : fixedWorkingHour?.displayEndTime ?? '';
    strEnd += isFixed ? ': $displayEndTime' : '';

    if (isShiftFromYesterDay ?? false) {
      strEnd += ' - ${DateFormat('dd/MM/yyyy').format(currentTime)}';
    } else if (isShiftToTomorrow) {
      strEnd +=
          ' - ${DateFormat('dd/MM/yyyy').format(currentTime.add(const Duration(days: 1)))}';
    }

    return strEnd;
  }

  // E.g: "Được phép chấm công đến: 12:00 AM"
  String get limitedEndTimeText {
    final durationTime = isNoShift ? '' : shiftDurationRangeText;
    return shiftType == ShiftType.fixed
        ? fixedWorkingHour?.workingTime?.checkOutTo != null
            ? '${LocaleKeys.timeKeeping_ableToCheckOut.tr}: ${fixedWorkingHour?.displayEnableCheckOut}'
            : ''
        : durationTime.isNotEmpty
            ? '${LocaleKeys.timeKeeping_totalWorkingTime.tr}: $shiftDurationRangeText'
            : '';
  }

  String get checkInTimeText {
    if (attendanceModelCheckIn == null) {
      return '';
    }

    return "${LocaleKeys.timeKeeping_checkInAt.tr}: ${convertTimestampToTime(attendanceModelCheckIn?.time)}";
  }

  String get lateCheckInTimeText {
    // chưa chấm công vào hoặc chấm đúng giờ
    if (attendanceModelCheckIn == null ||
        attendanceModelCheckIn?.invalidMinutes == 0 ||
        attendanceModelCheckIn?.invalidMinutes == null) {
      return '';
    }

    return "${LocaleKeys.timeKeeping_lateIn.tr} ${attendanceModelCheckIn?.invalidMinutes} ${LocaleKeys.timeKeeping_minute.tr}";
  }

  String get checkOutEarlyTimeText {
    // chưa chấm công ra vào hoặc chấm đúng giờ (attendanceModelCheckOut)
    if (attendanceModelCheckOut == null ||
        attendanceModelCheckOut?.invalidMinutes == 0) {
      return '';
    }

    return "${LocaleKeys.timeKeeping_earlyOut.tr} ${attendanceModelCheckOut?.invalidMinutes} ${LocaleKeys.timeKeeping_minute.tr}";
  }

  String get checkoutTimeText {
    if (attendanceModelCheckOut == null) {
      return '';
    }

    return "${LocaleKeys.timeKeeping_checkOutAt.tr}: ${convertTimestampToTime(attendanceModelCheckOut?.time)}";
  }

  String get latestCheckoutTimeText {
    if (attendanceModelCheckOut == null) {
      return '';
    }

    return "${LocaleKeys.timeKeeping_lastCheckOut.tr}: ${convertTimestampToTime(attendanceModelCheckOut?.time)}";
  }

  /// Hiển thị thời gian check in check out
  /// khi attendances chỉ có 2 value checkin và checkout
  String checkInCheckOut({bool isOnCheckInTime = false}) {
    if (shiftValidType == ShiftValidType.future &&
        isOnCheckInTime == false &&
        (attendances?.isEmpty ?? true)) {
      return LocaleKeys.timeKeeping_unavailableCheckIn.tr;
    }

    final checkIn = attendances?.firstWhereOrNull(
        (element) => element?.attendanceType == AttendanceType.checkIn);
    AttendanceModel? checkOut = attendances?.firstWhereOrNull(
        (element) => element?.attendanceType == AttendanceType.checkOut);

    if ((attendances?.length ?? 0) >= 2) {
      checkOut = attendances?.last;
    }

    final checkInTime = checkIn?.timeSheetCheckInOutTime ??
        LocaleKeys.timeKeeping_notCheckIn.tr;
    final checkOutTime = checkOut?.timeSheetCheckInOutTime ??
        LocaleKeys.timeKeeping_notCheckIn.tr;
    final time = "$checkInTime - $checkOutTime";

    return time;
  }

  /// Hiển thị thời gian đi muộn về sớm
  /// khi attendances chỉ có 2 value checkin và checkout
  /// E.g: "-- / --""
  String get goLateLeaveEarly {
    final checkIn = attendances?.firstWhereOrNull(
        (element) => element?.attendanceType == AttendanceType.checkIn);
    final checkOut = attendances?.firstWhereOrNull(
        (element) => element?.attendanceType == AttendanceType.checkOut);
    final goLate = checkIn == null
        ? "--"
        : checkIn.invalidMinutes == 0
            ? '--'
            : "${checkIn.invalidMinutes} ${LocaleKeys.timeKeeping_minute.tr}";
    final leaveEarly = checkOut == null
        ? "--"
        : checkOut.invalidMinutes == 0
            ? '--'
            : "${checkOut.invalidMinutes} ${LocaleKeys.timeKeeping_minute.tr}";

    return "$goLate / $leaveEarly";
  }

  /// cho trường hợp chỉ có 2 val checkin và checkout
  AttendanceModel? get checkIn => attendances?.firstWhereOrNull(
      (element) => element?.attendanceType == AttendanceType.checkIn);

  AttendanceModel? get checkOut => attendances?.firstWhereOrNull(
      (element) => element?.attendanceType == AttendanceType.checkOut);

  String? convertTimestampToTime(String? timestamp) {
    if (timestamp == null || timestamp.isEmpty) {
      return null;
    }
    DateTime dateTime =
        DateTime.fromMillisecondsSinceEpoch(int.parse(timestamp) * 1000);
    String formattedTime = DateFormat('hh:mm a').format(dateTime);
    return formattedTime;
  }

  @override
  bool operator ==(dynamic other) {
    return (other is ShiftModelVer2) &&
        id == other.id &&
        name == other.name &&
        shiftType == other.shiftType &&
        shiftValidType == other.shiftValidType;
  }

  @override
  int get hashCode => id.hashCode;

  String getPreviewImageCheckInOut(AttendanceModel? attendanceModel) {
    return (attendanceModel?.thumbnails?.isNotEmpty ??
            attendanceModel?.thumbnail?.isNotEmpty ??
            false)
        ? Utils.imageThumb(
            attendanceModel?.thumbnails?.first ??
                attendanceModel?.thumbnail ??
                '',
            "120x160",
          )
        : (attendanceModel?.images?.isNotEmpty ?? false)
            ? attendanceModel!.images!.first
            : '';
  }

  List<XFile>? getPreviewsImagesCheckInOutLocal(
    AttendanceModel? attendanceModel,
  ) {
    return attendanceModel?.xFiles;
  }

  List<String> get getSrcImagesCheckOut {
    return attendanceModelCheckOut?.images ?? [];
  }

  List<String> get getSrcImagesCheckIn {
    return attendanceModelCheckIn?.images ?? [];
  }

  String get getOfficeCheckIn {
    return (attendanceModelCheckIn != null)
        ? attendanceModelCheckIn!.officeCheckInOut
        : '';
  }

  String get getOfficeCheckOut {
    return (attendanceModelCheckOut != null)
        ? attendanceModelCheckOut!.officeCheckInOut
        : attendanceModelCheckIn?.officeCheckInOut ?? '';
  }

  AttendanceModel? get attendanceModelCheckIn {
    /*
      attendances sắp xếp theo thời gian giảm dần:
      - checkin:  attendances.last  -> bản ghi đầu tiên
      - checkout: attendances.first -> bản ghi cuối cùng
    */
    final AttendanceModel? ret = attendances?.lastWhereOrNull(
        (element) => element?.attendanceType == AttendanceType.checkIn);
    return ret;
  }

  AttendanceModel? get attendanceModelCheckOut {
    /*
      attendances sắp xếp theo thời gian giảm dần:
      - checkin:  attendances.last  -> bản ghi đầu tiên
      - checkout: attendances.first -> bản ghi cuối cùng
    */
    final AttendanceModel? ret = attendances?.firstWhereOrNull(
        (element) => element?.attendanceType == AttendanceType.checkOut);
    return ret;
  }

  bool get isFuture {
    return shiftValidType == ShiftValidType.future;
  }

  String get totalWorkingTime {
    const notHaveWorkingTime = "--";
    if (totalWorkingHours == -1) {
      return notHaveWorkingTime;
    } else {
      return totalWorkingHours?.hoursToHoursMinutes ?? notHaveWorkingTime;
    }
  }

  set setIsShiftFromYesterday(bool isFromYesterday) {
    isShiftFromYesterDay = isFromYesterday;
  }

  set setShiftOnDateId(String id) {
    shiftOnDateId = id;
  }

  bool get isShiftToTomorrow {
    // là ca qua đêm, nhưng không phải ca từ D0-1 -> D0
    return ((fixedWorkingHour?.isOvernight ?? false) &&
        !(isShiftFromYesterDay ?? false));
  }

  set setIsExpanded(bool isExpanded) {
    this.isExpanded = isExpanded;
  }

  bool get canCheckInFromPreviousDay =>
      fixedWorkingHour?.canCheckInFromPreviousDay() ?? false;

  bool isDisableShiftOvernight(DateTime currentTime) {
    if (isShiftFromYesterDay == true) {
      // if (attendanceModelCheckOut?.invalidMinutes == 0) {
      //   return true;
      // }
      final DateTime timeDisable = fixedWorkingHour
              ?.getTimeEnableCheckOut(currentTime.subtract(1.days)) ??
          fixedWorkingHour?.getStartTime(currentTime) ??
          currentTime;
      if (currentTime.isAfter(timeDisable)) {
        return true;
      }
    }
    return false;
  }

  set setLeaveRequests(List<ApprovalRequest>? requests) {
    approvalRequests = requests;
  }

  set setValidType(ShiftValidType type) {
    shiftValidType = type;
  }

  /// This must be `isShiftApproved` for better meaning
  /// refactor from `hasApprovedRequest` to `isShiftApproved`
  bool get isShiftApproved {
    final approvedRequests = approvalRequests?.firstWhereOrNull(
        (element) => element.stateType == ApprovalRequestState.approved);
    if (approvedRequests != null) {
      return true;
    }
    return false;
  }

  bool get hasApprovalRequests =>
      approvalRequests != null && approvalRequests!.isNotEmpty;

  bool get hasExplanationRequests =>
      explanationRequests != null && explanationRequests!.isNotEmpty;

  ApprovalRequest? get latestApprovalRequestByCreatedTime {
    if (approvalRequests == null || approvalRequests!.isEmpty) {
      return null;
    }

    return approvalRequests!.reduce((a, b) {
      final aCreatedAt = DateTime.parse(a.createdAt ?? '');
      final bCreatedAt = DateTime.parse(b.createdAt ?? '');
      return aCreatedAt.isAfter(bCreatedAt) ? a : b;
    });
  }

  ExplanationRequest? get latestExplanationRequestByCreatedTime {
    if (explanationRequests == null || explanationRequests!.isEmpty) {
      return null;
    }

    return explanationRequests!.reduce((a, b) {
      final aCreatedAt = a.createdAt;
      final bCreatedAt = b.createdAt;

      if (aCreatedAt == null || bCreatedAt == null) {
        return a;
      }

      return aCreatedAt.isAfter(bCreatedAt) ? a : b;
    });
  }

  String get shiftCode => (code?.isEmpty ?? true) ? "--" : code ?? "";
}
