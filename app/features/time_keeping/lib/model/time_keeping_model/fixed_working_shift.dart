import 'package:gp_feat_time_keeping/model/time_keeping_model/working_time.dart';
import 'package:gp_shared_dep/gp_shared_dep.dart';

part 'fixed_working_shift.g.dart';

@JsonSerializable(fieldRename: FieldRename.snake)
class FixedWorkingShift {
  @JsonKey(defaultValue: false)
  bool? isOvernight;
  WorkingTime? workingTime;

  FixedWorkingShift({this.isOvernight, this.workingTime});

  factory FixedWorkingShift.fromJson(Map<String, dynamic> json) =>
      _$FixedWorkingShiftFromJson(json);

  Map<String, dynamic> toJson() => _$FixedWorkingShiftToJson(this);

  DateTime? getTimeEnableCheckIn(DateTime startDate) {
    return convertStringTimeToDateTimeToday(
        workingTime?.checkInFrom, startDate);
  }

  DateTime? getTimeEnableCheckOut(DateTime startDate) {
    return convertStringTimeToDateTimeToday(
        workingTime?.checkOutTo,
        (isOvernight ?? false)
            ? startDate.add(const Duration(days: 1))
            : startDate);
  }

  DateTime? getStartTime(DateTime startDate) {
    return convertStringTimeToDateTimeToday(workingTime?.startTime, startDate);
  }

  DateTime? getEndTime(DateTime startDate) {
    return convertStringTimeToDateTimeToday(
        workingTime?.endTime,
        (isOvernight ?? false)
            ? startDate.add(const Duration(days: 1))
            : startDate);
  }

  String get displayStartTime {
    return '${workingTime?.startTime}';
  }

  String get displayEndTime {
    return '${workingTime?.endTime}';
  }

  String get displayEnableCheckIn {
    return workingTime?.checkInFrom != null
        ? '${workingTime?.checkInFrom}'
        : '';
  }

  String get displayEnableCheckOut {
    return workingTime?.checkOutTo != null ? '${workingTime?.checkOutTo}' : '';
  }

  String get showDurationTime {
    return '${workingTime?.startTime} - ${workingTime?.endTime}';
  }

  /// validate time format as 'hh:mm AM/PM/am/pm'
  bool isValidTimeFormat(String time) {
    RegExp timeRegex = RegExp(r'^(0[1-9]|1[0-2]):[0-5][0-9]\s(AM|PM|am|pm)$');
    return timeRegex.hasMatch(time);
  }

  /// input: strTime = "09:00 PM" --> output = datetime.date + time (21:00:00:0000)"
  DateTime? convertStringTimeToDateTimeToday(
      String? strTime, DateTime dateTime) {
    if (strTime == null || !isValidTimeFormat(strTime)) {
      return null;
    }
    final parsedDateTime = DateFormat('hh:mm a').parse(strTime);
    return DateTime(dateTime.year, dateTime.month, dateTime.day,
        parsedDateTime.hour, parsedDateTime.minute);
  }

  bool canCheckInFromPreviousDay() {
    if (workingTime?.startTime?.isEmpty == true ||
        workingTime?.checkInFrom?.isEmpty == true) {
      return false;
    }

    final startTime = DateFormat.jm().parse(displayStartTime); // giờ bắt đầu ca
    final checkInFrom =
        DateFormat.jm().parse(displayEnableCheckIn); // giờ cho phép chấm công

    return startTime.isBefore(checkInFrom);
  }
}
