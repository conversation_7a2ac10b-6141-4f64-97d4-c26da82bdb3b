import 'dart:async';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';

class OverlayTooltipController extends ChangeNotifier {
  late Function() onShow;
  late Function() onHide;

  void toggle() {
    notifyListeners();
  }

  void show() {
    onShow();
  }

  void hide() {
    onHide();
  }
}

/// This is a custom tooltip widget that displays a tooltip
/// with an arrow pointing to the target widget.
///
/// Source: https://github.com/stassop/flutter_animated_tooltip
class OverlayTooltipView extends StatefulWidget {
  /// The content of the tooltip.
  final String content;

  /// The global key of the target widget to which the tooltip points.
  final GlobalKey? targetGlobalKey = GlobalKey();

  /// The delay before showing the tooltip.
  final Duration? delay;

  /// The theme data for the tooltip.
  final ThemeData? theme;

  /// The child widget that triggers the tooltip.
  final Widget? child;

  final OverlayTooltipController controller;

  OverlayTooltipView({
    super.key,
    required this.content,
    this.delay,
    this.theme,
    required this.controller,
    this.child,
  });

  @override
  State<OverlayTooltipView> createState() => _OverlayTooltipViewState();
}

class _OverlayTooltipViewState extends State<OverlayTooltipView>
    with SingleTickerProviderStateMixin {
  final _overlayController = OverlayPortalController();

  late final AnimationController _animationController = AnimationController(
    duration: const Duration(milliseconds: 200),
    vsync: this,
  );

  late final Animation<double> _scaleAnimation;

  late double? _tooltipTop;
  late double? _tooltipBottom;
  late Alignment _tooltipAlignment;
  late Alignment _transitionAlignment;
  late Alignment _arrowAlignment;

  bool _isInverted = false;
  Timer? _delayTimer;

  final _arrowSize = const Size(16.0, 8.0);
  final _tooltipMinimumHeight = 140;

  @override
  void initState() {
    super.initState();
    widget.controller.onShow = _showTooltip;
    widget.controller.onHide = _hideTooltip;
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.delay != null) {
        _delayTimer = Timer(widget.delay!, _toggle);
      }

      _animationController.addListener(() {
        if (!mounted) return;
        setState(() {});
      });
    });

    _scaleAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    );

    widget.controller.addListener(() {
      _toggle();
    });
  }

  @override
  void dispose() {
    _delayTimer?.cancel();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = widget.theme ?? _getDefaultTheme(context);

    return OverlayPortal(
      controller: _overlayController,
      overlayChildBuilder: (context) {
        return Stack(
          children: [
            // ModalBarrier để chặn sự kiện tap bên ngoài tooltip
            ModalBarrier(
              dismissible: true,
              onDismiss: () {
                _toggle(); // Ẩn tooltip
              },
              color: Colors.transparent, // Đảm bảo không có màu nền
            ),
            Positioned(
              top: _tooltipTop,
              bottom: _tooltipBottom,
              child: ScaleTransition(
                alignment: _transitionAlignment,
                scale: _scaleAnimation,
                child: Theme(
                  data: theme,
                  child: SizedBox(
                    width: MediaQuery.of(context).size.width,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (_isInverted) _buildTooltipArrow(theme, true),
                        _buildTooltipContent(theme),
                        if (!_isInverted) _buildTooltipArrow(theme, false),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
      child: Center(
          child: IgnorePointer(
              ignoring: _overlayController.isShowing, child: widget.child!)),
    );
  }

  ThemeData _getDefaultTheme(BuildContext context) {
    return ThemeData(
      useMaterial3: true,
      brightness: Theme.of(context).brightness == Brightness.light
          ? Brightness.dark
          : Brightness.light,
    );
  }

  Widget _buildTooltipArrow(ThemeData theme, bool isInverted) {
    return Align(
      alignment: _arrowAlignment,
      child: TooltipArrow(
        size: _arrowSize,
        isInverted: isInverted,
        color: theme.canvasColor,
      ),
    );
  }

  Widget _buildTooltipContent(ThemeData theme) {
    return Align(
      alignment: _tooltipAlignment,
      child: ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 3 / 4,
        ),
        child: IntrinsicWidth(
          child: Container(
            decoration: BoxDecoration(
                color: Colors.black, borderRadius: BorderRadius.circular(8)),
            child: Padding(
              padding: const EdgeInsets.all(10.0),
              child: Align(
                alignment: Alignment.center,
                child: GestureDetector(
                  onTap: _toggle,
                  child: Center(
                    child: Text(
                      widget.content,
                      style: textStyle(GPTypography.bodyMedium)
                          ?.copyWith(color: Colors.white),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _toggle() {
    _delayTimer?.cancel();
    _animationController.stop();
    if (_overlayController.isShowing) {
      _animationController.reverse().then((_) => _overlayController.hide());
    } else {
      _updatePosition();
      _overlayController.show();
      _animationController.forward();
    }
  }

  void _showTooltip() {
    _delayTimer?.cancel();
    _animationController.stop();
    _updatePosition();
    _overlayController.show();
    _animationController.forward();
  }

  void _hideTooltip() {
    _delayTimer?.cancel();
    _animationController.stop();
    if (_overlayController.isShowing) {
      _animationController.reverse().then((_) => _overlayController.hide());
    }
  }

  void _updatePosition() {
    final Size contextSize = MediaQuery.of(context).size;
    final BuildContext targetContext =
        widget.targetGlobalKey?.currentContext ?? context;
    final targetRenderBox = targetContext.findRenderObject() as RenderBox;
    final targetOffset = targetRenderBox.localToGlobal(Offset.zero);
    final targetSize = targetRenderBox.size;

    final tooltipFitsAboveTarget = targetOffset.dy - _tooltipMinimumHeight >= 0;
    final tooltipFitsBelowTarget =
        targetOffset.dy + targetSize.height + _tooltipMinimumHeight <=
            contextSize.height;

    _tooltipTop = tooltipFitsAboveTarget
        ? null
        : tooltipFitsBelowTarget
            ? targetOffset.dy + targetSize.height
            : null;
    _tooltipBottom = tooltipFitsAboveTarget
        ? contextSize.height - targetOffset.dy
        : tooltipFitsBelowTarget
            ? null
            : targetOffset.dy + targetSize.height / 2;

    _isInverted = _tooltipTop != null;

    // Tính toán vị trí ngang của tooltip
    double tooltipX = targetOffset.dx + targetSize.width / 2;

    _tooltipAlignment = Alignment(
      (tooltipX / contextSize.width) * 2 - 1.0,
      _isInverted ? 1.0 : -1.0,
    );

    _transitionAlignment = Alignment(
      (targetOffset.dx + targetSize.width / 2) / contextSize.width * 2 - 1.0,
      _isInverted ? -1.0 : 1.0,
    );

    _arrowAlignment = Alignment(
      (targetOffset.dx + targetSize.width / 2) /
              (contextSize.width - _arrowSize.width) *
              2 -
          1.0,
      _isInverted ? 1.0 : -1.0,
    );
  }
}

class TooltipArrowPainter extends CustomPainter {
  final Size size;
  final Color color;
  final bool isInverted;

  final double cornerRadius;

  TooltipArrowPainter({
    required this.size,
    required this.color,
    required this.isInverted,
    this.cornerRadius = 2.5,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();

    final arrowWidth = size.width;
    final arrowHeight = size.height;

    if (isInverted) {
      // Mũi tên hướng lên trên
      path.moveTo(0.0, arrowHeight);
      path.lineTo(arrowWidth / 2 - cornerRadius, 0.0);

      // Bo tròn
      path.arcToPoint(
        Offset(arrowWidth / 2 + cornerRadius, 0.0),
        radius: Radius.circular(cornerRadius),
        clockwise: true,
      );

      path.lineTo(arrowWidth, arrowHeight);
    } else {
      // Mũi tên hướng xuống dưới
      path.moveTo(0.0, 0.0);
      path.lineTo(arrowWidth / 2 - cornerRadius, arrowHeight);

      // Bo tròn
      path.arcToPoint(
        Offset(arrowWidth / 2 + cornerRadius, arrowHeight),
        radius: Radius.circular(cornerRadius),
        clockwise: false,
      );

      path.lineTo(arrowWidth, 0.0);
    }

    path.close();

    canvas.drawShadow(path, Colors.black, 4.0, false);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class TooltipArrow extends StatelessWidget {
  final Size size;
  final Color color;
  final bool isInverted;

  const TooltipArrow({
    super.key,
    this.size = const Size(12.0, 2.0),
    this.color = Colors.white,
    this.isInverted = false,
  });

  @override
  Widget build(BuildContext context) {
    return Transform.translate(
      offset: Offset(-size.width / 2, isInverted ? 4 : 0),
      child: CustomPaint(
        size: size,
        painter: TooltipArrowPainter(
          size: size,
          color: color,
          isInverted: isInverted,
        ),
      ),
    );
  }
