import 'package:doc_widget/doc_widget.dart';
import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';

/// ```dart
///  final widget = IconWidget(
///    bgColor: Colors.green,
///    pathIcon: "assets/images/ic16-fill-calendar.svg","
///  );
/// ```
@docWidget
class IconWidget extends StatelessWidget {
  const IconWidget({super.key, required this.bgColor, required this.pathIcon});

  final Color bgColor;
  final String pathIcon;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
          color: bgColor, borderRadius: BorderRadius.circular(30)),
      child: Center(
          child: SvgWidget(
        pathIcon,
        color: GPColor.bgPrimary,
      )),
    );
  }
}
