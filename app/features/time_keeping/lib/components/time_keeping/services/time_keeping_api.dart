import 'dart:convert';

import 'package:device_safety_info/device_safety_info.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_time_keeping/components/time_keeping/repo/time_keeping_repository.dart';
import 'package:gp_feat_time_keeping/model/request/check_in_out_request.dart';
import 'package:gp_feat_time_keeping/model/response/check_in_out_response.dart';
import 'package:gp_feat_time_keeping/model/response/office_response.dart';
import 'package:gp_feat_time_keeping/model/time_keeping_model/shift_and_time.dart';
import 'package:gp_feat_time_keeping/model/time_keeping_model/shift_and_times.dart';

const _secretKey =
    'acff2145b1b9d61f5eafd5a358bfa205f28798af797a0b91889c82a34d36ad20';
Duration securityCurrentTimeDifferent = Duration.zero;
const Duration checkInTimeRange = Duration(minutes: 5);

/// update lại difference time giữa BE và client,
/// chính xác khi time ở BE > và < client.
void updateSecurityCurrentTime(DateTime currentTime) {
  securityCurrentTimeDifferent = currentTime.difference(DateTime.now());
}

class TimeKeepingApi extends TimeKeepingRepo with _SecurityMixin {
  final ApiService _service = ApiService(Constants.timeKeepingDomain,
      gpDomainChecker: GPDomainChecker.timeKepping);

  ApiResponse<ShiftAndTime> _apiResponse(dynamic data) =>
      ApiResponse<ShiftAndTime>.fromJson(
          data, (p0) => ShiftAndTime.fromJson(p0));

  ApiResponse<CheckInOutResponse> _apiCheckInResponse(dynamic data) =>
      ApiResponse<CheckInOutResponse>.fromJson(
          data, (p0) => CheckInOutResponse.fromJson(p0));

  ApiResponse<CheckInOutResponse> _apiCheckOutResponse(dynamic data) =>
      ApiResponse<CheckInOutResponse>.fromJson(
          data, (p0) => CheckInOutResponse.fromJson(p0));

  ApiResponse<OfficeResponse> _apiOfficeResponse(dynamic data) =>
      ApiResponse<OfficeResponse>.fromJson(
          data, (p0) => OfficeResponse.fromJson(p0));

  @override
  Future<ApiResponse<ShiftAndTime>> getShiftAndTime({
    required DateTime dateTime,
  }) async {
    try {
      Map<String, dynamic> params = {
        'date': DateFormat("yyyy-MM-dd").format(dateTime),
      };
      final response =
          await _service.getData(endPoint: '/shift-on-dates', query: params);
      ApiResponse<ShiftAndTime> result = _apiResponse(response.data);

      return result;
    } catch (e, s) {
      logDebug('getShiftAndTime -> $s');
      rethrow;
    }
  }

  @override
  Future<ShiftAndTimes?> getShiftAndTimePeriod({
    required DateTime fromDate,
    required DateTime toDate,
  }) async {
    try {
      Map<String, dynamic> params = {
        'from': DateFormat("yyyy-MM-dd").format(fromDate),
        'to': DateFormat("yyyy-MM-dd").format(toDate)
      };
      final response = await _service.getData(
          endPoint: '/shift-on-dates/period', query: params);

      if (response.data == null || response.data['data'] == null) return null;

      ShiftAndTimes result = ShiftAndTimes.fromJson(response.data['data']);

      return result;
    } catch (e, s) {
      logDebug('getShiftAndTimePeriod error: $s');
      rethrow;
    }
  }

  @override
  Future<ApiResponse<CheckInOutResponse>> checkIn(
    CheckInOutRequest request,
  ) async {
    try {
      const endPoint = '/attendances/check-in';
      final body = request.toJson();
      final headers = await getSecurityHeaders(
        endPoint: endPoint,
        body: body,
      );

      final response = await _service.postData(
        endPoint: endPoint,
        body: body,
        headers: headers,
      );

      ApiResponse<CheckInOutResponse> result =
          _apiCheckInResponse(response.data);
      return result;
    } catch (e, s) {
      logDebug('checkIn -> $s');
      rethrow;
    }
  }

  @override
  Future<ApiResponse<CheckInOutResponse>> checkOut(
    CheckInOutRequest request,
  ) async {
    try {
      const endPoint = '/attendances/check-out';
      final body = request.toJson();
      final headers = await getSecurityHeaders(
        endPoint: endPoint,
        body: body,
      );

      final response = await _service.postData(
        endPoint: endPoint,
        body: body,
        headers: headers,
      );
      ApiResponse<CheckInOutResponse> result =
          _apiCheckOutResponse(response.data);
      return result;
    } catch (e, s) {
      logDebug('checkOut -> $s');
      rethrow;
    }
  }

  @override
  Future<ApiResponse<CheckInOutResponse>> checkInWithoutShift(
    CheckInOutRequest request,
  ) async {
    try {
      const endPoint = '/attendances/check-in-out';
      final body = request.toJson();
      final headers = await getSecurityHeaders(
        endPoint: endPoint,
        body: body,
      );

      final response = await _service.postData(
        endPoint: endPoint,
        body: body,
        headers: headers,
      );
      ApiResponse<CheckInOutResponse> result =
          _apiCheckInResponse(response.data);
      return result;
    } catch (e, s) {
      logDebug('checkInWithoutShift -> $s');
      rethrow;
    }
  }

  @override
  Future<ApiResponse<OfficeResponse>> getNearestOffice(
    double lat,
    double long, {
    required CheckInOutRequestPositionInfo clientPositionInfo,
  }) async {
    try {
      final params = <String, dynamic>{
        'lat': lat,
        'long': long,
      };
      params.addAll(clientPositionInfo.toJson());

      final response = await _service.getData(
        endPoint: '/attendances/nearest-office',
        query: params,
      );
      ApiResponse<OfficeResponse> result = _apiOfficeResponse(response.data);
      return result;
    } catch (e, s) {
      logDebug('getNearestOffice: $s');
      rethrow;
    }
  }

  // @override
  // Future<List<OfficeResponse>> getListOffice() async {
  //   try {
  //     final response = await _service.getData(endPoint: '/admin/offices');
  //     if (response.data['data'] == null) {
  //       return [];
  //     }
  //     ListAPIResponse<OfficeResponse>? result =
  //         _apiListOfficeResponse(response.data);
  //     return result.data;
  //   } catch (e) {
  //     rethrow;
  //   }
  // }
}

mixin _SecurityMixin {
  ///
  /// Cơ chế:
  /// - Algorithm: SHA-256
  /// - Message: <relativeURL> + <timestamp> + <secretKey> + <body>
  ///
  /// Với:
  /// - relativeURL: URL tương đối của request.
  /// timestamp: Thời gian hiện tại dưới dạng Unix.
  /// secretKey: Chuỗi khóa bí mật dùng để tạo chữ ký HMAC.
  /// body: Nội dung body của request.
  ///
  /// Ví dụ:
  /// - relativeURL: "/attendances/check-in""
  /// - timestamp: "1629780000": second"
  /// - secretKey: "secret_key""
  /// - body: `{"key1":"value1","key2":"value2"}`"
  /// =>> Message = /attendances/check-in1629780000secret_key{"key1":"value1","key2":"value2"}"
  /// =>> Hashed Message = dda79ac79911176c63146ae383287c62947ffd7b25d01516cc1344a643b2bc33
  ///
  /// Client tin cậy gửi chữ ký HMAC(HMAC-Signature) và thời gian hiện tại(timestamp) trong header của request:
  /// - x-gapo-timestamp: Thời gian hiện tại dưới dạng Unix.
  /// - x-gapo-signature: Chữ ký HMAC của message.
  Future<Map<String, dynamic>> getSecurityHeaders({
    required String endPoint,
    required Map<String, dynamic> body,
  }) async {
    bool isRealDevice =
        Constants.isProduction ? await DeviceSafetyInfo.isRealDevice : true;

    if (!isRealDevice) {
      return {
        'device_info':
            'Anyone trying to checkin with similator: ${Constants.getDeviceName()}',
      };
    }

    final bodyStr = jsonEncode(body);

    final utf8Str = String.fromCharCodes(utf8.encode(bodyStr));

    final checkinDateTime = DateTime.now().add(securityCurrentTimeDifferent);
    final timeStamp =
        (checkinDateTime.millisecondsSinceEpoch ~/ 1000).toString();
    final message = '$endPoint$timeStamp$_secretKey$utf8Str';

    var hmacSha256 = Hmac(sha256, _secretKey.codeUnits);
    var signature = hmacSha256.convert(message.codeUnits);

    logDebug("Debug_Time_Keeping: message: $message");
    logDebug("Debug_Time_Keeping: x-gapo-signature: $signature");

    return {
      'x-gapo-timestamp': timeStamp,
      'x-gapo-signature': signature,
    };
  }
}
