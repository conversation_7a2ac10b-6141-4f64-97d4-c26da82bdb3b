import 'dart:async';
import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:geolocator/geolocator.dart' as geolocator;
import 'package:gp_core/core.dart';
import 'package:gp_core/utils/gp_sentry.dart';
import 'package:gp_feat_time_keeping/model/request/check_in_out_request.dart';
import 'package:location/location.dart';

import '../../../../utils/time_tracking.dart';

const _timeLimitLocationFromGeoLocator = Duration(seconds: 10);
// const _timeLimitLocationFromLocationDependency = Duration(seconds: 10);
const _timeLimitLocationStream = Duration(seconds: 3);
const tagDebug = 'Debug_Time_Keeping';

/// happy case 1x -> 3x, bad case 600 (test on android)
const _locationGoodAccuracyInMeters = 30;
const _locationSpeedDefault = 0; // min 0

final locationAndroidSettings = geolocator.AndroidSettings(
  accuracy: geolocator.LocationAccuracy.bestForNavigation,
  distanceFilter: 0,
  forceLocationManager: true,
  timeLimit: _timeLimitLocationStream,
  intervalDuration: _timeLimitLocationStream,
);

final locationIosSettings = geolocator.AppleSettings(
  accuracy: geolocator.LocationAccuracy.bestForNavigation,
  activityType: geolocator.ActivityType.fitness,
  distanceFilter: 0,
  pauseLocationUpdatesAutomatically: false,
  // Only set to true if our app will be started up in the background.
  showBackgroundLocationIndicator: false,
  allowBackgroundLocationUpdates: true,
  timeLimit: _timeLimitLocationStream,
);

mixin LocationPermission {
  final Location _location = Location();

  Future<LocationDataWrapper?> getUserLocation() async {
    if (!await _isLocationEnabled()) {
      return null;
    }

    if (!await _isPermissionGranted()) {
      return null;
    }

    if (await _getAccuracy() == null) {
      return null;
    }
    final start = DateTime.now();

    /*
      - 06/05/2024: ưu tiên location from `geolocator` dependency
      - 17/05/2024: ưu tiên location from `location` dependency
        - Nếu không lấy được location, chuyển qua xài `geolocator` dependency
        - `geolocator` dependency lấy location rất nhanh,
        nhưng có 1 vài case lấy vị trí chưa thực sự chính xác,
        điển hình là GPS yếu, hoặc mất GPS
        - `location` dependency lấy location chậm hơn,
        mặc dù code đều khá tương đồng ???
    */
    try {
      /*
        20/05/2024: lấy vài kết quả từ location
        chủ yếu để start toàn bộ location sensors
        
        more details, reađ
        `geolocator.Geolocator.getPositionStream`
        This event starts all location sensors on the device
      */
      LocationData? result;
      final res = await _getUserLocations();
      final results = res.toList();

      if (results.isNotEmpty) {
        result = results.last;
        _test(results);
      } else {
        result = await _locationFromGeoLocator();
        _test([result]);
      }

      // _locationFromLocationPackage();
      trackingTimeDiff(
          start, "getUserLocation: ${result.latitude}, ${result.longitude}");

      return LocationDataWrapper(
        connectivityResult: await _logConnectivity(),
        locationData: result,
      );
    } catch (ex, s) {
      logDebug('ex -> $ex, $s');
      if (ex is TimeoutException) {
        throw TimeoutException('location');
      }
    } finally {
      _getLastUserLocation();
    }

    return null;
  }

  /// faster than getting location from `location dependency`
  Future<LocationData> _locationFromGeoLocator() async {
    final geoLocatorResult = await geolocator.Geolocator.getCurrentPosition(
      desiredAccuracy: geolocator.LocationAccuracy.bestForNavigation,
      timeLimit: _timeLimitLocationFromGeoLocator,
    );

    final result = geoLocatorResult.mapPositionToLocationData();

    GPCoreTracker().appendMessage(
      'geoLocator',
      data: {
        'rawData': geoLocatorResult.toJson(),
        'predictResult': result,
      },
    );

    logDebug(
        '$tagDebug: geoLocator geoLocatorResult -> ${geoLocatorResult.toJson()}');
    logDebug('$tagDebug: geoLocator LocationData -> ${result.toJson()}');

    return result;
  }

  /// return location from `location` dependency
  // Future<LocationData?> _locationFromLocationPackage() async {
  //   final isEnableSetting = await _location.changeSettings(
  //     accuracy: GetPlatform.isIOS
  //         ? LocationAccuracy.navigation
  //         : LocationAccuracy.high,
  //   );

  //   logDebug('$_tagDebug: isEnableSetting -> $isEnableSetting');
  //   GPTimeKeepingTracker().appendMessage('isEnableSetting: $isEnableSetting');

  //   final LocationData result = await _location
  //       .getLocation()
  //       .then((value) {
  //         logDebug('$_tagDebug: location -> ${value.toJson()}');

  //         GPTimeKeepingTracker().appendMessage(
  //           'location',
  //           data: {
  //             'location': value.toJson(),
  //           },
  //         );
  //         return value;
  //       })
  //       .timeout(_timeLimitLocationFromLocationDependency)
  //       .catchError((error) async {
  //         return await _locationFromGeoLocator()
  //             .catchError((error) => throw error);
  //       });

  //   _logConnectivity();

  //   return result;
  // }

  /// LocationData look like:
  ///
  /// ```json
  /// location: {}
  ///   accuracy: 65,
  ///   altitude: 11.970333576202393,
  ///   altitude_accuracy: 15.266878128051758,
  ///   floor: <null>,
  ///   heading: -1,
  ///   heading_accuracy: -1,
  ///   is_mocked: False,
  ///   latitude: 20.993343819864364,
  ///   longitude: 105.80762051952244,
  ///   speed: 0,
  ///   speed_accuracy: 0,
  ///   timestamp: 1716285196786
  /// }
  /// ```
  Future<Iterable<LocationData>> _getUserLocations() async {
    final results = await _getUserLocationsFromStream();

    // if (needToRetry) {
    //   // nếu độ chính xác quá thấp, retry
    //   logDebug('$tagDebug: retry _getUserLocations()');

    //   final bool hasAnyGoodAccuracyResult =
    //       results.any((element) => element.isGoodAccuracy);

    //   if (!hasAnyGoodAccuracyResult) {
    //     final retryResults = await _getUserLocations(needToRetry: false);
    //     results.addAll(retryResults);

    //     // retry one more time if needed
    //     final bool needToRetryOneMoreTime = results.length <= 1;
    //     if (needToRetryOneMoreTime) {
    //       results.addAll(await _getUserLocations(needToRetry: false));
    //     }
    //   }
    // }

    return results;
  }

  Future<List<LocationData>> _getUserLocationsFromStream() async {
    final results = <LocationData>[]; // await _locationFromGeoLocator()

    // /*
    //   09/08/2024:
    //   ToanNM đổi lại, lấy 1 lần duy nhất để tăng tốc độ lấy vị trí.
    //   Hiện flow thay đổi, cần call api nearest-office trước khi chấm công
    //   nên cần giảm tối đa thời gian lấy vị trí,
    //   tránh tình trạng user phải chờ lâu mỗi lần chấm công.
    // */

    final subscription = locationFromGeoLocatorStream().listen((event) {
      results.add(event.mapPositionToLocationData());

      GPCoreTracker().appendMessage(
        'locationStream',
        data: {
          'location': event.toJson(),
        },
      );
      logDebug('$tagDebug: _locationFromGeoLocatorStream -> ${event.toJson()}');
    });

    /*
      - accuracy hiểu đơn giản là bán kinh sai số theo đơn vị meter
      - Ở google map chính là vòng tròn xanh mờ ở vị trí của tôi
      - ToanNM test thử
        - happy case: 1x - 3x
        - có case lên tới 600 trên android,
        - accuracy cao khi:
          - khi device khóa màn hình, hoặc device chuyển từ background -> foreground
            => khi lần đầu locationManger run, và bắt đầu chạy trong ~~1s đầu
          - khi device chuyển đổi giữa GPS -> wifi -> 3g
          - sai số tăng khi di chuyển nhanh
    */
    results.sort((a, b) =>
            // sắp xếp theo accuracy từ thấp -> cao
            ((a.accuracy ?? 0).round().compareTo(b.accuracy ?? 0).round())
        // +
        // speed từ thấp -> cao (hạn chế case user di chuyển)
        // (a.speed?.compareTo(b.speed ?? 0) ?? 0),
        );

    await Future.delayed(_timeLimitLocationStream);

    await subscription.cancel();

    return results;
  }

  Future<LocationData?> _getLastUserLocation() async {
    final result = await geolocator.Geolocator.getLastKnownPosition();

    if (result != null) {
      GPCoreTracker().appendMessage(
        'lastLocation',
        data: {'last_location': result.toJson()},
      );

      logDebug('$tagDebug: lastLocation -> ${result.toJson()}');

      return result.mapPositionToLocationData();
    }

    return null;
  }

  ///
  /// ```Markdown
  /// +-------------------+------------+--------------------------+
  /// |                   | Android    | ios                      |
  /// +===================+============+==========================+
  /// | lowest            | 500m       | 3000m                    |
  /// +-------------------+------------+--------------------------+
  /// | low               | 500m       | 1000m                    |
  /// +-------------------+------------+--------------------------+
  /// | medium            | 100 - 500m | 100m                     |
  /// +-------------------+------------+--------------------------+
  /// | high              | 0 - 100m   | 10m                      |
  /// +-------------------+------------+--------------------------+
  /// | best              | 0 - 100m   | ~0m                      |
  /// +-------------------+------------+--------------------------+
  /// | bestForNavigation | 0 - 100m   | Optimized for navigation |
  /// +-------------------+------------+--------------------------+
  ///```
  ///
  Stream<geolocator.Position> locationFromGeoLocatorStream() {
    return geolocator.Geolocator.getPositionStream(
      locationSettings:
          Platform.isAndroid ? locationAndroidSettings : locationIosSettings,
    );
    // return geolocator.Geolocator.getPositionStream(
    //   locationSettings: const geolocator.LocationSettings(
    //     accuracy: geolocator.LocationAccuracy.bestForNavigation,
    //     timeLimit: _timeLimitLocationStream,
    //   ),
    // );
  }

  Future<bool> _isLocationEnabled() async {
    // Check if location service is enable
    if (!await _location.serviceEnabled()) {
      GPCoreTracker().appendMessage('_location.serviceEnabled() == false');

      if (!await _location.requestService()) {
        GPCoreTracker().appendMessage('_location.requestService() == false');

        return false;
      }
    }

    return true;
  }

  Future<bool> _isPermissionGranted() async {
    // Check if permission is granted
    if (await _location.hasPermission() != PermissionStatus.granted) {
      // check denied
      if (await _location.requestPermission() == PermissionStatus.denied) {
        return false;
      }

      if (await _location.requestPermission() ==
          PermissionStatus.deniedForever) {
        await _handlePermissionDeniedForever();
        return false;
      }
    }

    return true;
  }

  Future<geolocator.LocationAccuracyStatus?> _getAccuracy() async {
    try {
      final accuracyStatus = await geolocator.Geolocator.getLocationAccuracy();
      if (accuracyStatus != geolocator.LocationAccuracyStatus.precise) {
        await Get.dialog(
          const DialogNoPreciseLocation(),
        );

        return null;
      }

      logDebug('$tagDebug: accuracyStatus -> $accuracyStatus');

      GPCoreTracker().appendMessage(
        'accuracyStatus',
        data: {'accuracyStatus': accuracyStatus},
      );

      return accuracyStatus;
    } catch (ex) {
      GPCoreTracker().appendError(
        'geo error',
        data: {'error': ex},
      );
    }

    return null;
  }

  Future _handlePermissionDeniedForever() async {
    bool isTheFirstTimeDeniedForever =
        GetStorage().read("locationPermission") ?? false;
    if (!isTheFirstTimeDeniedForever) {
      GetStorage().write("locationPermission", true);
      return;
    }
    return await Get.dialog(
      DialogOpenSystemSetting(
        title: LocaleKeys.timeKeeping_titleLocationSetting.tr,
        content: LocaleKeys.timeKeeping_contentLocationSetting.tr,
      ),
    );
  }

  Future<List<ConnectivityResult>> _logConnectivity() async {
    try {
      final List<ConnectivityResult> result =
          await Connectivity().checkConnectivity();

      logDebug('$tagDebug: connectivity -> $result');

      GPCoreTracker().appendMessage(
        'ConnectivityResult',
        data: {'connectivity': result},
      );

      return result;
    } catch (ex) {
      GPCoreTracker().appendError(
        'ConnectivityResult error',
        data: {'error': ex},
      );
    }

    return [];
  }

  void _test(Iterable<LocationData> results) {
    // if ((kDebugMode || kProfileMode) && Constants.userId() == '1256051015') {
    //   const officeLat = 20.997909189736887;
    //   const officeLong = 105.83752018825948;

    //   var message = '';
    //   LocationData? previousLocationData;
    //   for (var element in results) {
    //     message += '\n accuracy: ${element.accuracy}, speed: ${element.speed}';

    //     if (previousLocationData != null) {
    //       final distance = geolocator.Geolocator.distanceBetween(
    //         previousLocationData.latitude ?? 0,
    //         previousLocationData.longitude ?? 0,
    //         element.latitude ?? 0,
    //         element.longitude ?? 0,
    //       );

    //       message +=
    //           '\n distance to previous location: ${distance.round()} meters';
    //     }

    //     final distanceToOffice = geolocator.Geolocator.distanceBetween(
    //       element.latitude ?? 0,
    //       element.longitude ?? 0,
    //       officeLat,
    //       officeLong,
    //     );

    //     message += '\n distance to office: ${distanceToOffice.round()} meters';
    //     message += '\n\n';

    //     previousLocationData = element;
    //   }

    //   Popup.instance.showAlert(title: "Locations", message: message);
    // }
  }
}

extension _PositionMapperExt on geolocator.Position {
  LocationData mapPositionToLocationData() {
    return LocationData.fromMap({
      'latitude': latitude,
      'longitude': longitude,
      'accuracy': accuracy,
      'altitude': altitude,
      'speed': speed,
      'heading': heading,
      'time': timestamp.millisecondsSinceEpoch.toDouble(),
    });
  }
}

extension _LocationDataExt on LocationData {
  // ignore: unused_element
  bool get isGoodAccuracy => (accuracy ?? 0) <= _locationGoodAccuracyInMeters;
  // ignore: unused_element
  bool get isMoving => speed != _locationSpeedDefault;

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'accuracy': accuracy,
      'altitude': altitude,
      'speed': speed,
      'speed_accuracy': speedAccuracy,
      'heading': heading,
      'time': time,
      'isMock': isMock,
      'verticalAccuracy': verticalAccuracy,
      'headingAccuracy': headingAccuracy,
      'elapsedRealtimeNanos': elapsedRealtimeNanos,
      'elapsedRealtimeUncertaintyNanos': elapsedRealtimeUncertaintyNanos,
      'satelliteNumber': satelliteNumber,
      'provider': provider,
    };
  }
}

class LocationDataWrapper {
  LocationDataWrapper({
    required this.locationData,
    required this.connectivityResult,
  }) {
    clientPositionInfo = CheckInOutRequestPositionInfo(
      accuracy: locationData.accuracy ?? 0,
      speed: locationData.speed ?? 0,
      speedAccuracy: locationData.speedAccuracy ?? 0,
      isMocked: locationData.isMock ?? false,
      timestamp: ((locationData.time ?? 0) ~/ 1000).toDouble(),
      provider: locationData.provider,
    );
  }

  final LocationData locationData;
  final List<ConnectivityResult> connectivityResult;
  late final CheckInOutRequestPositionInfo clientPositionInfo;

  Map<String, dynamic> officeMap = {};

  Map<String, dynamic> toJson() {
    return {
      "client_connectivity": connectivityResult.map((e) => e.name).toList(),
    }..addAll(clientPositionInfo.toJson());
  }
}
