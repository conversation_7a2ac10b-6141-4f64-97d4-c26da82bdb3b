import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/utils/gp_sentry.dart';
import 'package:gp_feat_time_keeping/components/time_keeping/services/time_keeping_api.dart';
import 'package:gp_feat_time_keeping/components/time_keeping/use_case/time_keeping_use_case.dart';
import 'package:gp_feat_time_keeping/model/response/record_my_data.dart';
import 'package:gp_feat_time_keeping/model/time_keeping_model/shift_and_times.dart';
import 'package:gp_feat_time_keeping/model/time_keeping_model/shift_model_ver2.dart';
import 'package:gp_feat_time_keeping/model/time_keeping_model/shift_on_date.dart';

import '../../../model/time_keeping_model/holidays.dart';
import 'work_shift_item_controller_ext/permission_location_mixin.dart';

class TimeKeepingControllerVer2 extends BaseController
    with StateMixin, LocationPermission {
  TimeKeepingControllerVer2() : super(GPConnectionConcrete()) {
    // init usecase
    timeKeepingUseCase = TimeKeepingUseCase(timeKeepingApi);
    // fetch data
    fetchListWorkingShiftToday();
  }

  final List<ShiftModelVer2> listShiftToday = [];
  DateTime currentTime = DateTime.now();

  bool get statusLoading => isLoading.value;
  // usecase + api
  late TimeKeepingUseCase timeKeepingUseCase;
  final TimeKeepingApi timeKeepingApi = TimeKeepingApi();

  // bool get hasRequestApprovaled =>
  //     shiftAndTimeToday.shiftOnDate?.hasRequestApprovaled ?? false;

  bool isCheckingInOut = false;

  late LifeCycleEventHandler lifeCycleEvent;

  final RxBool rxHasShift = false.obs;
  final RxBool rxHasHoliday = false.obs;
  final List<Holidays> holidays = [];

  @override
  void onInit() {
    super.onInit();
    lifeCycleEvent = LifeCycleEventHandler(
      resumeCallBack: () async {
        if (isCheckingInOut == false) fetchListWorkingShiftToday();

        locationFromGeoLocatorStream();
      },
    );
    WidgetsBinding.instance.addObserver(lifeCycleEvent);

    locationFromGeoLocatorStream();
  }

  @override
  void onClose() {
    super.onClose();
    WidgetsBinding.instance.removeObserver(lifeCycleEvent);
  }

  // timeline: D0-1 -> D0(today) -> D0+1
  Future fetchListWorkingShiftToday({bool isRefresh = false}) async {
    try {
      if (!isRefresh) {
        change(null, status: RxStatus.loading());
      }
      // before get list shift -> check office?
      // bool isHasOffice = await _checkOffice();
      // if (!isHasOffice) {
      //   change(false, status: RxStatus.error());
      // } else {

      // final List<ShiftModelVer2> listShift = await _getListShift();
      final List<ShiftModelVer2> listShift = await _getListShiftByPeriod();
      if (listShift.isEmpty) {
        change(null, status: RxStatus.empty());
      } else {
        change(listShift, status: RxStatus.success());
      }
      listShiftToday.clear();
      listShiftToday.addAll(listShift);
      _initExpand();
      update([KeyUpdate.updateTimeTitle]);
      // }
    } catch (e, s) {
      GPCoreTracker().appendError(
        'Flutter:timeKeeping.handleError: gpConnection.isInternetConnected() == false',
        data: {'ex': e, 'stackTrace': s},
      );
      GPCoreTracker().sendLog(
        message:
            'Flutter:timeKeeping.handleError: gpConnection.isInternetConnected() == false',
      );
      change(e, status: RxStatus.error(e.toString()));
    }
  }

  void _clearExpand() {
    // update all expanded = false
    for (var element in listShiftToday) {
      element.setIsExpanded = false;
    }
  }

  void expandShift(int index, {bool? isReloadList}) {
    if (listShiftToday.length == 1) {
      if (listShiftToday[index].isExpanded = true) {
        return;
      }

      listShiftToday[index].setIsExpanded = true;
      update([KeyUpdate.updateExpanded]);

      return;
    }

    bool beforeExpand = listShiftToday[index].isExpanded ?? false;
    _clearExpand();
    // set value isExpanded for shiftId
    listShiftToday[index].setIsExpanded =
        isReloadList != null ? true : !beforeExpand;

    update([KeyUpdate.updateExpanded]);
  }

  void _initExpand() {
    if (listShiftToday.isEmpty) {
      return;
    }
    // nếu là ca linh động hoặc 1 ca cố định duy nhất -> auto open
    if (listShiftToday.length == 1) {
      expandShift(0);
      return;
    }
    bool hasExpand = false;
    for (int i = 0; i < listShiftToday.length; i++) {
      if (!hasExpand) {
        // ưu tiên mở ca chưa hết giờ làm việc
        if (listShiftToday[i].isShiftToTomorrow) {
          expandShift(i);
          hasExpand = true;
          break;
        }
        final bool isShiftFromYesterday =
            listShiftToday[i].isShiftFromYesterDay ?? false;

        final DateTime dateStartShift = isShiftFromYesterday
            ? currentTime.subtract(const Duration(days: 1))
            : currentTime;

        final DateTime? endTime =
            listShiftToday[i].fixedWorkingHour?.getEndTime(dateStartShift);

        final DateTime? checkOutTo = listShiftToday[i]
            .fixedWorkingHour
            ?.getTimeEnableCheckOut(dateStartShift);

        if (currentTime.isBefore(checkOutTo ?? endTime ?? currentTime)) {
          expandShift(i);
          hasExpand = true;
          break;
        }
      }
    }
    // trường hợp giờ hiện tại muộn hơn giờ ra của ca muộn nhất thì mở ca cuối cùng
    if (!hasExpand) {
      expandShift(listShiftToday.length - 1);
      hasExpand = true;
    }

    update([KeyUpdate.updateExpanded]);
  }

  // Future<bool> _checkOffice() async {
  //   List<OfficeResponse> listOffice = await timeKeepingUseCase.getListOffice();
  //   if (listOffice.isNotEmpty) return true;
  //   return false;
  // }

  // Future<List<ShiftModelVer2>> _getListShift() async {
  //   final List<ShiftModelVer2> listShiftToday = [];
  //   // get list shift of D0
  //   final ShiftAndTime shiftAndTimeToday =
  //       await timeKeepingUseCase.getShiftAndTime(currentDate: currentTime);
  //   // get current time
  //   currentTime = shiftAndTimeToday.date;
  //   DateTime date = currentTime.subtract(const Duration(days: 1)); // D0-1
  //   // get list shift of D0-1
  //   final ShiftAndTime shiftAndTimeYesterday =
  //       await timeKeepingUseCase.getShiftAndTime(currentDate: date);
  //   // get shift overnight from D0-1 to D0
  //   final ShiftModelVer2? shiftFromYesterday =
  //       (shiftAndTimeYesterday.shiftOnDate?.shifts ?? [])
  //           .firstWhereOrNull((e) => e.fixedWorkingHour?.isOvernight == true);
  //   // check and add shift overnight from D0-1 to D0
  //   if (shiftFromYesterday != null) {
  //     shiftFromYesterday.setIsShiftFromYesterday = true;
  //     shiftFromYesterday.setShiftOnDateId =
  //         shiftAndTimeYesterday.shiftOnDate?.id ?? '';
  //     if (!shiftFromYesterday.isDisableShiftOvernight(currentTime)) {
  //       listShiftToday.add(shiftFromYesterday);
  //     }
  //   }
  //   // add list shift D0 (today)
  //   if (shiftAndTimeToday.shiftOnDate?.shifts.isNotEmpty ?? false) {
  //     for (var element in shiftAndTimeToday.shiftOnDate!.shifts) {
  //       element.setShiftOnDateId = shiftAndTimeToday.shiftOnDate?.id ?? '';
  //     }
  //   }
  //   listShiftToday.addAll(shiftAndTimeToday.shiftOnDate?.shifts ?? []);
  //   return listShiftToday;
  // }

  Future<List<ShiftModelVer2>> _getListShiftByPeriod() async {
    DateTime fromDate = currentTime.subtract(const Duration(days: 1));
    DateTime toDate = currentTime;

    final ShiftAndTimes? shiftAndTimes =
        await timeKeepingUseCase.getShiftAndTimePeriod(
      fromDate: fromDate,
      toDate: toDate,
    );

    if (shiftAndTimes == null) return [];

    currentTime = shiftAndTimes.date;
    updateSecurityCurrentTime(currentTime);

    if (shiftAndTimes.shiftOnDates?.isEmpty == true) return [];

    final List<ShiftModelVer2> shifts = [];

    holidays.clear();

    ShiftOnDate? shiftAndTimeByDate(DateTime compareDate) {
      final compareDateToLocal =
          compareDate.applyTimeOfDay(hour: 0, minute: 0).toUtc();
      return shiftAndTimes.shiftOnDates?.firstWhereOrNull((element) =>
          element != null && element.date.compareDate(compareDateToLocal));
    }

    final ShiftOnDate? shiftAndTimeYesterday = shiftAndTimeByDate(fromDate);
    final ShiftOnDate? shiftAndTimeToday = shiftAndTimeByDate(currentTime);

    // --------- Yesterday --------- \
    if (shiftAndTimeYesterday != null) {
      final ShiftModelVer2? shiftFromYesterday = shiftAndTimeYesterday.shifts
          ?.firstWhereOrNull(
              (e) => e != null && e.fixedWorkingHour?.isOvernight == true);

      if (shiftFromYesterday != null) {
        shiftFromYesterday.setIsShiftFromYesterday = true;
        shiftFromYesterday.setShiftOnDateId = shiftAndTimeYesterday.id;

        if (!shiftFromYesterday.isDisableShiftOvernight(currentTime)) {
          shifts.add(shiftFromYesterday);
        }
      }
    }

    // --------- Today --------- \
    if (shiftAndTimeToday != null) {
      if (shiftAndTimeToday.shifts?.isNotEmpty == true) {
        final List<ShiftModelVer2> ret =
            shiftAndTimeToday.shifts?.whereNotNull().toList() ?? [];

        for (var element in ret) {
          element.setShiftOnDateId = shiftAndTimeToday.id;
        }

        shifts.addAll(ret);
      }

      holidays.addAll(shiftAndTimeToday.holidays ?? []);
      rxHasHoliday.value = holidays.isNotEmpty;
    }

    rxHasShift.value = shifts.any((element) => !element.isNoShift);

    return shifts;
  }
}

void openHolidayLists(Iterable<Holidays> holidays) {
  Popup.instance.showBottomSheet(SafeArea(
    top: false,
    child: Column(
      mainAxisSize: MainAxisSize.min,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Center(
                child: Text(LocaleKeys.timeKeeping_holiday_list.tr,
                    style: textStyle(GPTypography.headingMedium)),
              ),
            ),
            const IconButton(
              onPressed: Utils.back,
              icon: SvgWidget(
                  Assets.PACKAGES_GP_ASSETS_IMAGES_IC24_LINE15_XMARK_PNG),
            )
          ],
        ),
        Divider(
          color: GPColor.lineTertiary,
          thickness: 1,
        ),
        ...holidays.map((e) {
          return Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                width: 7,
                height: 7,
                decoration: BoxDecoration(
                  color: GPColor.red,
                  shape: BoxShape.circle,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                  child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(e.name, style: textStyle(GPTypography.bodyMedium)),
                  const SizedBox(height: 2),
                  Text(
                    e.displayDateTime(),
                    style: textStyle(GPTypography.bodyMedium)
                        ?.mergeColor(GPColor.contentSecondary),
                  ),
                ],
              ))
            ],
          ).paddingSymmetric(horizontal: 16);
        }),
      ],
    ),
  ));
}

class KeyUpdate {
  static const String updateTimeTitle = 'update_time_title';
  // time_keeping_controller
  static const String updateListShift = 'update_list';
  // work_shift_item_controller
  static const String updateStatusCheckIn = 'update_checkin_status';
  static const String updateStatusCheckOut = 'update_checkout_status';
  static const String updateExpanded = 'update_expand';
}

class LifeCycleEventHandler extends WidgetsBindingObserver {
  final AsyncCallback? resumeCallBack;
  final AsyncCallback? suspendingCallBack;

  LifeCycleEventHandler({
    this.resumeCallBack,
    this.suspendingCallBack,
  });

  @override
  Future<void> didChangeAppLifecycleState(AppLifecycleState state) async {
    switch (state) {
      case AppLifecycleState.resumed:
        if (resumeCallBack != null) {
          await resumeCallBack?.call();
        }
        break;
      case AppLifecycleState.inactive:
      case AppLifecycleState.paused:
      case AppLifecycleState.detached:
      case AppLifecycleState.hidden:
        if (suspendingCallBack != null) {
          await suspendingCallBack?.call();
        }
        break;
    }
  }
}
