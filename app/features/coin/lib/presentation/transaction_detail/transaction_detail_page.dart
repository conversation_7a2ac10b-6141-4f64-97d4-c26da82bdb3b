import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_core/core.dart' hide RefreshIndicator;
import 'package:gp_feat_coin/data/model/request/coin_transaction_details_params.dart';
import 'package:gp_feat_coin/domain/entity/coin_transaction.entity.dart';
import 'package:gp_feat_coin/presentation/transaction_detail/views/coin_information_view.dart';
import 'package:gp_feat_coin/widgets/coin_empty_view.dart';
import 'package:gp_feat_coin/widgets/loading_view.dart';

import 'bloc/bloc.dart';

class CoinTransactionDetailRouteData {
  CoinTransactionDetailRouteData({
    required this.id,
    this.entity,
  });

  final String id;
  final CoinTransactionEntity? entity;
}

class CoinTransactionDetailPage extends StatelessWidget {
  CoinTransactionDetailPage({
    super.key,
    required this.routeData,
  });

  final CoinTransactionDetailRouteData routeData;

  late final defaultEvent = CoinTransactionGetDetailsEvent(
    params: CoinTransactionDetailsParams(transactionId: routeData.id),
    transactionEntity: routeData.entity,
  );

  late final CoinTransactionDetailBloc bloc = CoinTransactionDetailBloc()
    ..add(defaultEvent);

  late final RefreshController refreshController =
      RefreshController(initialRefresh: false);

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => bloc,
      child: Stack(
        children: [
          // --------- Background --------- \
          Container(
            height: Get.height,
            width: Get.width,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  GPColor.greenLight, // Màu bắt đầu (#E0FBE5)
                  GPColor.bgPrimary, // Màu kết thúc (#FFFFFF)
                ],
              ),
            ),
            child: SvgWidget(
              Assets
                  .PACKAGES_GP_ASSETS_IMAGES_SVG_COIN_DETAIL_BACKGROUND_PATTERN_SVG,
              height: Get.height,
              width: Get.width,
            ),
          ),
          SafeArea(
            top: false,
            child: Scaffold(
              // extendBodyBehindAppBar: true,
              backgroundColor: Colors.transparent,
              appBar: AppBar(
                backgroundColor: Colors.transparent,
                leading: const SizedBox.shrink(),
                actions: [
                  CupertinoButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: SizedBox(
                      width: 24,
                      height: 24,
                      child: Center(
                        child: Image.asset(
                          Assets.PACKAGES_GP_ASSETS_IMAGES_IC24_FILL_XMARK_PNG,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 12,
                  )
                ],
                systemOverlayStyle: SystemUiOverlayStyle.light
                    .copyWith(statusBarColor: Colors.black),
                title: Text(
                  LocaleKeys.coin_trading_detail_title.tr,
                  style: textStyle(GPTypography.headingMedium)
                      ?.mergeColor(GPColor.contentPrimary),
                ),
                elevation: 0,
                centerTitle: true,
              ),
              body: Container(
                width: Get.width,
                margin: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: GPColor.bgPrimary,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: BlocConsumer(
                  bloc: bloc,
                  listener: (context, state) {
                    if (state is TransactionDetailsErrorState) {
                      refreshController.refreshFailed();
                    } else if (state is TransactionDetailsLoadedState) {
                      refreshController.refreshCompleted();
                    }
                  },
                  builder: (context, state) {
                    if (state is TransactionDetailsLoadingState) {
                      return CoinSkeletonLoadingView(
                        width: Get.width - 56,
                        padding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 12),
                        margin: const EdgeInsets.only(top: 0),
                      );
                    }
                    if (state is TransactionDetailsErrorState) {
                      return const CoinEmptyView();
                    }

                    if (state is TransactionDetailsLoadedState) {
                      return SmartRefresher(
                        controller: refreshController,
                        onRefresh: () async {
                          bloc.add(defaultEvent);
                        },
                        header: GPPullToRefreshHeader(),
                        enablePullUp: false,
                        child: CoinInformationView(
                          entity: state.entity,
                        ),
                      );
                    }

                    return const CoinEmptyView();
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// class _TransactionStatusWidget extends StatelessWidget {
//   const _TransactionStatusWidget({
//     required this.status,
//   });

//   final CoinTransactionStatus? status;

//   @override
//   Widget build(BuildContext context) {
//     // 17/02: không cần hiển thị status
//     return const SizedBox();

//     if (status == null) return const SizedBox();

//     return Container(
//       padding:
//           const EdgeInsetsDirectional.symmetric(horizontal: 8, vertical: 4),
//       decoration: BoxDecoration(
//         color: status!.bgColor,
//         borderRadius: BorderRadius.circular(8),
//       ),
//       child: Text(
//         status!.displayName,
//         style:
//             textStyle(GPTypography.bodyMedium)?.mergeColor(GPColor.greenDark),
//       ),
//     );
//   }
// }
