import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_coin/domain/domain.dart';
import 'package:gp_feat_coin/domain/entity/gift.entity.dart';
import 'package:gp_feat_coin/presentation/gift_detail/bloc/gift_detail_bloc.dart';
import 'package:gp_feat_coin/presentation/gift_exchange_detail_transaction/views/gift_transaction_detail_images.dart';
import 'package:gp_feat_coin/presentation/gift_exchange_detail_transaction/views/gift_transaction_detail_redeem_gift_cancel_details.dart';
import 'package:gp_feat_coin/presentation/transaction_detail/views/transaction_detail_item_view.dart';
import 'package:gp_feat_coin/presentation/transaction_detail/views/transaction_detail_transfer_widget.dart';

class CoinInformationView extends StatelessWidget {
  const CoinInformationView({
    super.key,
    required this.entity,
  });

  final CoinTransactionEntity? entity;

  @override
  Widget build(BuildContext context) {
    if (entity?.contextType == CoinContextType.redeemGiftCancel) {
      return _RedeemGiftCancelInformationView(entity: entity).paddingAll(16);
    } else {
      return _NormalCoinInformationView(entity: entity).paddingAll(16);
    }
  }
}

class _RedeemGiftCancelInformationView extends StatelessWidget {
  _RedeemGiftCancelInformationView({required this.entity});

  final CoinTransactionEntity? entity;

  late final GiftDetailBloc giftDetailBloc = GiftDetailBloc()
    ..add(
      GiftDetailRequestDetailEvent(
        giftId: entity?.contextId ?? '',
      ),
    );

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // --------- Thời gian --------- \
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: TransactionDetailItemView(
                title: LocaleKeys.coin_trading_detail_time.tr,
                content: entity?.displayDateTime() ?? '',
                shouldShowEndSeparator: true,
              ),
            ),
          ],
        ),
        // --------- Số điểm --------- \
        TransactionDetailItemView(
          title: LocaleKeys.coin_trading_detail_value_title.tr,
          content: entity?.displayRawCoin() ?? '',
          shouldShowEndSeparator: true,
        ),

        if (entity != null)
          GiftTransactionDetailRedeemGiftCancelDetails(
              coinTransactionEntity: entity!, shouldShow: true),

        BlocSelector<GiftDetailBloc, GiftDetailState, GiftEntity?>(
          bloc: giftDetailBloc,
          selector: (state) {
            if (state is GiftDetailLoadedState) {
              return state.giftDetail;
            }
            return null;
          },
          builder: (context, state) {
            if (state == null) {
              return const SizedBox.shrink();
            }
            return GiftTransactionDetailImages(entity: state);
          },
        )
      ],
    );
  }
}

class _NormalCoinInformationView extends StatelessWidget {
  const _NormalCoinInformationView({required this.entity});

  final CoinTransactionEntity? entity;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // --------- Thời gian --------- \
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
              child: TransactionDetailItemView(
                title: LocaleKeys.coin_trading_detail_time.tr,
                content: entity?.displayDateTime() ?? '',
                shouldShowEndSeparator: true,
              ),
            ),
          ],
        ),

        TransactionDetailTransferWidget(
          entity: entity,
          shouldShowEndSeparator: true,
        ),

        TransactionDetailItemView(
          title: LocaleKeys.coin_qr_qr_code_name_title.tr,
          content: entity?.displayQRCodeName() ?? '',
          shouldShow: entity?.isQrTransfer == true,
          shouldShowEndSeparator: true,
        ),

        TransactionDetailItemView(
          title: LocaleKeys.coin_trading_detail_value_title.tr,
          content: entity?.displayRawCoin() ?? '',
          shouldShowEndSeparator: entity?.isQrTransfer == false,
        ),

        TransactionDetailItemView(
          title: LocaleKeys.coin_trading_detail_reason_title.tr,
          content: entity?.displayRawReason() ?? '',
          shouldShow: entity?.isQrTransfer == false,
        ),
      ],
    );
  }
}
