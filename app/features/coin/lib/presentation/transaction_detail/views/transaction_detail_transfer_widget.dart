import 'package:flutter/cupertino.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_coin/domain/entity/coin_transaction.entity.dart';
import 'package:gp_feat_coin/presentation/transaction_detail/views/transaction_detail_dotted_line_separator_view.dart';
import 'package:gp_feat_coin/presentation/transaction_detail/views/transaction_detail_user_from_api_widget.dart';

class TransactionDetailTransferWidget extends StatelessWidget {
  final bool shouldShowStartSeparator;
  final bool shouldShowEndSeparator;

  const TransactionDetailTransferWidget({
    super.key,
    this.entity,
    this.shouldShowStartSeparator = false,
    this.shouldShowEndSeparator = true,
  });

  final CoinTransactionEntity? entity;

  @override
  Widget build(BuildContext context) {
    if (entity == null) return const SizedBox();

    final isRecallPoints = entity?.isRecallPoints ?? false;

    final userWidgets = [
      Text(
        entity!.displayTransferReceiveTitle(isRecallPoints),
        style: textStyle(GPTypography.headingSmall)
            ?.mergeColor(GPColor.contentPrimary),
      ),
      const SizedBox(height: 12),
      if (entity?.hasUserInfo == true && entity != null) ...{
        TransactionDetailUserFromApiWidget(entity: entity!),
      } else ...{
        TransactionDetailUserFromApiWidget(entity: entity),
      }
    ];

    final titleWidget = [
      Text(
        entity!.displayTransferReceiveTitle(!isRecallPoints),
        style: textStyle(GPTypography.headingSmall)
            ?.mergeColor(GPColor.contentPrimary),
      ),
      const SizedBox(height: 8),
      Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              color: GPColor.blueLighter,
              shape: BoxShape.circle,
            ),
            child: SvgWidget(
              Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_FILL_COIN_MONEY_SVG,
              color: GPColor.blueDark,
            ),
          ),
          Text(
            entity?.displayMoneySource() ?? '',
            style: textStyle(GPTypography.headingSmall)
                ?.mergeColor(GPColor.contentPrimary),
          )
        ],
      )
    ];

    if (isRecallPoints) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // --------- thu hồi điểm --------- \
          if (shouldShowStartSeparator)
            TransactionDetailDottedLineSeparatorView()
                .paddingSymmetric(vertical: 12),
          ...userWidgets,
          TransactionDetailDottedLineSeparatorView()
              .paddingSymmetric(vertical: 12),
          ...titleWidget,
          if (shouldShowEndSeparator)
            TransactionDetailDottedLineSeparatorView()
                .paddingSymmetric(vertical: 12),
        ],
      );
    } else {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // --------- Chuyển điểm --------- \
          if (shouldShowStartSeparator)
            TransactionDetailDottedLineSeparatorView()
                .paddingSymmetric(vertical: 12),
          ...titleWidget,
          TransactionDetailDottedLineSeparatorView()
              .paddingSymmetric(vertical: 12),
          ...userWidgets,
          if (shouldShowEndSeparator)
            TransactionDetailDottedLineSeparatorView()
                .paddingSymmetric(vertical: 12),
        ],
      );
    }
  }
}
