part of 'gift_exchange_transaction_detail_bloc.dart';

@immutable
sealed class GiftExchangeTransactionDetailState extends CoreV2BaseState {
  const GiftExchangeTransactionDetailState();
}

final class GiftExchangeTransactionDetailInitial
    extends GiftExchangeTransactionDetailState {}

final class GiftExchangeTransactionDetailLoadedState
    extends GiftExchangeTransactionDetailState {
  final GiftExchangeDetailEntity giftExchangeDetail;

  const GiftExchangeTransactionDetailLoadedState({
    required this.giftExchangeDetail,
  });
}

final class GiftExchangeTransactionStatusUpdatedState
    extends GiftExchangeTransactionDetailState {
  final GiftExchangeDetailEntity giftExchangeDetail;

  const GiftExchangeTransactionStatusUpdatedState({
    required this.giftExchangeDetail,
  });
