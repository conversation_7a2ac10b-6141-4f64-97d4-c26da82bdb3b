import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core_v2/base/bloc/bloc.dart';
import 'package:gp_feat_coin/domain/entity/coin_qr_code.entity.dart';

@immutable
sealed class QrcodeScannerState extends CoreV2BaseState {}

final class QrcodeScannerInitial extends QrcodeScannerState {
  QrcodeScannerInitial();
}

final class QrCodeCameraPaused extends QrcodeScannerState {
  QrCodeCameraPaused();
}

final class QrCodeCameraResumed extends QrcodeScannerState {
  QrCodeCameraResumed();
}

class QrCodeError extends QrcodeScannerState {
  final String message;
  QrCodeError({required this.message});
}

class QrCodeNotFound extends QrCodeError {
  QrCodeNotFound()
      : super(
          message: LocaleKeys.coin_qr_qr_code_not_found.tr,
        );
}

final class QrCodeDetails extends QrcodeScannerState {
  final CoinQrCodeEntity qrCodeDetails;
  QrCodeDetails({required this.qrCodeDetails});
}

final class QrCodeCameraReady extends QrcodeScannerState {
  QrCodeCameraReady();
