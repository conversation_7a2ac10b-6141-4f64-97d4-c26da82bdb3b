import 'package:gp_core_v2/base/bloc/bloc.dart';
import 'package:qr_code_scanner_plus/qr_code_scanner_plus.dart';

sealed class QrCodeScannerEvent extends CoreV2BaseEvent {}

final class QrCodeScannerInitialEvent extends QrCodeScannerEvent {
  QrCodeScannerInitialEvent();
}

final class QrCodeScannedEvent extends QrCodeScannerEvent {
  QrCodeScannedEvent();
}

final class QrCodeProcessingEvent extends QrCodeScannerEvent {
  final Barcode qrCode;
  QrCodeProcessingEvent({required this.qrCode});
}

final class QrCodeSwitchToWalletEvent extends QrCodeScannerEvent {
  QrCodeSwitchToWalletEvent();
}

final class QrCodeProcessingQrCodeFromImageEvent extends QrCodeScannerEvent {
  final String? qrCode;
  QrCodeProcessingQrCodeFromImageEvent({this.qrCode});
}

final class QrCodeStopScanningEvent extends QrCodeScannerEvent {
  QrCodeStopScanningEvent();
}

final class QrCodeStartScanningEvent extends QrCodeScannerEvent {
  QrCodeStartScanningEvent();
}

final class QrCodeCameraReadyEvent extends QrCodeScannerEvent {
  QrCodeCameraReadyEvent();
