/*
 * Created Date: 4/01/2024 14:52:59
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Monday, 17th March 2025 10:16:53
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

// ignore_for_file: public_member_api_docs
// ignore_for_file: use_if_null_to_convert_nulls_to_bools

import 'package:auto_mappr_annotation/auto_mappr_annotation.dart';
import 'package:gp_feat_coin/data/model/response/coin_qr_code_state_response.dart';
import 'package:gp_feat_coin/data/model/response/coin_transaction_response.dart';
import 'package:gp_feat_coin/data/model/response/wallet_info_response.dart';
import 'package:gp_feat_coin/domain/entity/coin_exchange_info.entity.dart';
import 'package:gp_feat_coin/domain/entity/coin_qr_code.entity.dart';
import 'package:gp_feat_coin/domain/entity/coin_transaction.entity.dart';
import 'package:gp_feat_coin/domain/entity/wallet_info.entity.dart';
import 'package:gp_shared/mapper/entity/assignee_entity_mapper.dart';

import 'coin_entity_mapper.auto_mappr.dart';


@AutoMappr(
  [
    // ---------- TicketList ---------- \
    MapType<WalletInfoResponse, WalletInfoEntity>(),
    MapType<CoinTransactionResponse, CoinTransactionEntity>(fields: [
      Field(
        'id',
        custom: CoinEntityMapper.mapId,
      ),
      Field("exchangeInfo", custom: CoinEntityMapper.mapExchangeInfo),
    ]),
    MapType<CoinQrCodeStateResponse, CoinQrCodeEntity>(),
  ],
  includes: [
    AssigneeEntityMapper(),
  ],
  delegates: [
    AssigneeEntityMapper(),
  ],
)
class CoinEntityMapper extends $CoinEntityMapper {
  const CoinEntityMapper();

  static dynamic mapId(CoinTransactionResponse input) {
    return input.transactionId;
  }

  static CoinExchangeInfoEntity mapExchangeInfo(
    CoinTransactionResponse input,
  ) {
    return CoinExchangeInfoEntity(
      id: input.exchangeInfo?.id,
      status: input.exchangeInfo?.status,
    );
  }
}
