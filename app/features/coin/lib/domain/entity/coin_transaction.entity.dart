/*
 * Created Date: Wednesday, 22nd January 2025, 15:09:27
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Monday, 17th March 2025 15:44:06
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_coin/domain/entity/coin_exchange_info.entity.dart';
import 'package:gp_feat_coin/utils/coin_formatter.dart';
import 'package:gp_shared/domain/entity/assignee/assignee.dart';
import 'package:gp_shared/domain/entity/base/base_list.entity.dart';

import 'enums/enums.dart';

final class CoinTransactionEntity extends BaseListEntity {
  CoinTransactionEntity({
    required this.transactionId,
    required this.transactionType,
    required this.transactionStatus,
    required this.contextType,
    required this.walletBalanceChanged,
    this.contextName,
    this.fromUserId,
    this.toUserId,
    this.walletCurrency,
    this.createdAt,
    this.message,
    this.fromUserInfo,
    this.toUserInfo,
    this.contextId,
    this.exchangeInfo,
  }) : super(id: transactionId);

  /// The transaction ID
  final String transactionId;

  final CoinTransactionType transactionType;
  final CoinTransactionStatus transactionStatus;
  final CoinContextType contextType;
  final String? contextName;

  /// The from user id
  /// For an organization wallet, user_id = workspace_id.
  /// For the system, user_id = 0.
  final dynamic fromUserId;

  /// The to user id | - The from user id
  /// - For an organization wallet, user_id = workspace_id.
  /// - For the system, user_id = 0.
  final dynamic toUserId;

  final int walletBalanceChanged;

  final String? walletCurrency;
  final DateTime? createdAt;

  final String? message;

  /// from user or to user
  ///
  /// Nếu Loại giao dịch là "Trao điểm cho thành viên" (toUserId)"
  /// thì hiển thị tên thành viên nhận điểm theo format {avatar} {tên_thành_viên} {phòng_ban} {chức_vụ}
  ///
  /// Nếu Loại giao dịch là "Thu hồi điểm của thành viên" (fromUserId)"
  /// thì hiển thị tên thành viên bị thu hồi theo format {avatar} {tên_thành_viên} {phòng_ban} {chức_vụ}
  AssigneeEntity? userEntity;

  final String? contextId;

  final CoinExchangeInfoEntity? exchangeInfo;

  final AssigneeEntity? fromUserInfo, toUserInfo;

  ///
  /// nếu BE trả về user_info thì sẽ lấy user_info, còn không thì lấy từ user_id
  ///
  bool get hasUserInfo => fromUserInfo != null || toUserInfo != null;

  // giao dịch nhận điểm
  // bool get isIn => contextType != CoinContextType.deductPoints;

  bool get isOut =>
      contextType == CoinContextType.deductPoints ||
      contextType == CoinContextType.recallPoints ||
      contextType == CoinContextType.qrCodeSubtractPoints ||
      contextType == CoinContextType.redeemGift;

  String displayTitle() {
    if (contextType == CoinContextType.redeemGift) {
      return LocaleKeys.coin_gift_transaction_redeem_gift.tr;
    }

    if (contextType == CoinContextType.redeemGiftCancel) {
      return LocaleKeys.coin_gift_transaction_refund_point.tr;
    }

    var isOrgFunding = fromUserId.toString() == Constants.workspaceId() ||
        toUserId.toString() == Constants.workspaceId();

    var fromUserDisplayName = fromUserInfo?.displayName ?? '';
    var toUserDisplayName = toUserInfo?.displayName ?? '';

    if (isOut) {
      return isOrgFunding
          ? LocaleKeys.coin_trading_detail_transfer_organization.tr
          : fromUserDisplayName;
    } else {
      return isOrgFunding
          ? LocaleKeys.coin_trading_detail_transfer_organization.tr
          : toUserDisplayName;
    }
  }

  String assetIcon() {
    if (!isOut) {
      return Assets
          .PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_FILL_INARROW_CIRCLE_RIGHTDOWN_SVG;
    }

    return Assets
        .PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_FILL_OUTARROW_CIRCLE_LEFTUP_SVG;
  }

  String displayCoinValue() {
    if (!isOut) {
      return '+${CoinFormatter.formatCoin(walletBalanceChanged.toString())}';
    }

    return '-${CoinFormatter.formatCoin(walletBalanceChanged.toString())}';
  }

  String displaySubTitle() {
    final isRedeemGift = contextType == CoinContextType.redeemGift;
    final isRedeemGiftCancel = contextType == CoinContextType.redeemGiftCancel;

    if (isRedeemGift) {
      return contextName ?? '';
    }

    if (isRedeemGiftCancel) {
      return LocaleKeys.coin_gift_transaction_redeem_gift_canceled.tr;
    }

    final isQrTransaction = [
      CoinContextType.qrCodeAddPoints,
      CoinContextType.qrCodeSubtractPoints,
    ].contains(contextType);

    final shouldShowQrMessage = contextName != null;

    if (isQrTransaction && shouldShowQrMessage) {
      return contextName!;
    }

    /*
      Expected:
        - Từ quỹ tổ chức vì {lý_do} , chỉ hiển thị trên 1 dòng, dài thì hiển thị ...
        - Về quỹ tổ chức vì {lý_do} , chỉ hiển thị trên 1 dòng, dài thì hiển thị ...
      
      Logic:
        - Với locale vi: replace `vì`
        - Bỏ viết hoa ký tự đầu tiên ở `reason`: chưa áp dụng
    */

    String title = !isOut
        ? LocaleKeys.coin_trading_history_item_in_reason.tr
        : LocaleKeys.coin_trading_history_item_out_reason.tr;

    String newReason = message ?? '';

    final String becauseStr = LocaleKeys.coin_trading_history_item_because.tr;
    if (newReason.toLowerCase().contains(becauseStr)) {
      newReason = newReason.replaceAll(
          RegExp(
            becauseStr,
            caseSensitive: false,
          ),
          '');
    } else if (newReason.isEmpty) {
      title = title.replaceAll(
          RegExp(
            becauseStr,
            caseSensitive: false,
          ),
          '');
    }

    return '$title ${newReason.trim()}';
  }

  String displayQRCodeName() {
    if (contextName != null) {
      return contextName!;
    }

    return "";
  }

  String displayRawReason() {
    String newReason = message ?? '';

    final String becauseStr = LocaleKeys.coin_trading_history_item_because.tr;
    if (newReason.toLowerCase().contains(becauseStr) || newReason.isEmpty) {
      newReason = newReason.replaceAll(
          RegExp(
            becauseStr,
            caseSensitive: false,
          ),
          '');
    }

    return newReason.trim();
  }

  String displayDateTime() {
    if (createdAt != null) {
      return DateFormat('HH:mm dd/MM/yyyy').format(createdAt!);
    }

    return '';
  }

  String displayCoin() {
    final userCoin = walletBalanceChanged.toString();

    if (!isOut) {
      return '+${CoinFormatter.formatCoin(userCoin)}';
    }

    return '-${CoinFormatter.formatCoin(userCoin)}';
  }

  String displayRawCoin() {
    final userCoin = walletBalanceChanged.toString();

    return CoinFormatter.formatCoin(userCoin);
  }

  String displayMoneySource() {
    return LocaleKeys.coin_trading_detail_transfer_organization.tr;
  }

  bool get isRecallPoints => [
        CoinContextType.recallPoints,
        CoinContextType.qrCodeSubtractPoints,
      ].contains(contextType);

  bool get isQrTransfer => [
        CoinContextType.qrCodeAddPoints,
        CoinContextType.qrCodeSubtractPoints,
      ].contains(contextType);

  String displayTransferReceiveTitle(bool isTransfer) {
    return isTransfer
        ? LocaleKeys.coin_trading_detail_transfer_title.tr
        : LocaleKeys.coin_trading_detail_receive_title.tr;
  }

  String displayUserName() {
    return userInfo?.displayName ?? userEntity?.displayName ?? '';
  }

  String displayUserDepartment() {
    return userInfo?.department ?? userEntity?.department ?? '';
  }

  String displayUserRole() {
    return userInfo?.role ?? userEntity?.role ?? '';
  }

  String displayTransactionType() {
    return isRecallPoints
        ? LocaleKeys.coin_trading_detail_transaction_recall_point.tr
        : LocaleKeys.coin_trading_detail_transaction_is_not_recall_point.tr;
  }

  String displayTransactionStatus() {
    if (contextType == CoinContextType.redeemGift) {
      return exchangeInfo?.status?.localizedValue ?? '';
    }
    return transactionStatus.displayName;
  }

  Color getTransactionStatusColor() {
    if (contextType == CoinContextType.redeemGift) {
      return exchangeInfo?.status?.textColor ?? Colors.white;
    }
    return transactionStatus.textColor;
  }

  int get userId {
    if (isRecallPoints) {
      return fromUserId;
    }

    return toUserId;
  }

  AssigneeEntity? get userInfo {
    if (isRecallPoints) {
      return fromUserInfo;
    }

    return toUserInfo;
  }

  Color displayCoinColor() {
    if (!isOut) {
      return GPColor.greenLight;
    }

    return GPColor.orangeLighter;
  }
}
