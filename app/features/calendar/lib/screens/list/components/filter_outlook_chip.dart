import 'package:flutter/cupertino.dart';
import 'package:gp_core/base/networking/base/feature_flag.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_calendar/screens/create/models/calendar_event_type.dart';

enum FilterOutlookChipItem {
  gapowork,
  outlook,
  google,
  meeting,
  task,
  reminder,
  room,
}

final kDefaultFilterEventAndSource = [
  FilterOutlookChipItem.gapowork,
  if (FeatureFlag.enableFilterOutlook) FilterOutlookChipItem.outlook,
  if (FeatureFlag.enableFilterGoogle) FilterOutlookChipItem.google,
];

extension FilterOutlookChipItemValues on FilterOutlookChipItem {
  String? get iconPath {
    switch (this) {
      case FilterOutlookChipItem.gapowork:
        return 'assets/images/svg/ic24-fill-gapologo-square.svg';
      case FilterOutlookChipItem.outlook:
        return 'assets/images/Outlook-Calendar.svg';
      case FilterOutlookChipItem.google:
        return 'assets/images/svg/Google-Calendar.svg';
      default:
        return null;
    }
  }

  Color? get tagColor {
    switch (this) {
      case FilterOutlookChipItem.meeting:
        return CalendarEventType.meeting.fabBgcolor;
      case FilterOutlookChipItem.task:
        return CalendarEventType.task.fabBgcolor;
      case FilterOutlookChipItem.reminder:
        return CalendarEventType.reminder.fabBgcolor;
      case FilterOutlookChipItem.room:
        return GPColor.indigo;
      default:
        return null;
    }
  }

  String get title {
    switch (this) {
      case FilterOutlookChipItem.gapowork:
        return LocaleKeys
            .calendar_sync_outlook_bottom_sheet_filter_button_gapowork_title.tr;
      case FilterOutlookChipItem.outlook:
        return LocaleKeys
            .calendar_sync_outlook_bottom_sheet_filter_button_outlook_title.tr;
      case FilterOutlookChipItem.google:
        return LocaleKeys
            .calendar_sync_outlook_bottom_sheet_filter_button_google_title.tr;
      case FilterOutlookChipItem.meeting:
        return CalendarEventType.meeting.displayName;
      case FilterOutlookChipItem.task:
        return CalendarEventType.task.displayName;
      case FilterOutlookChipItem.reminder:
        return CalendarEventType.reminder.displayName;
      case FilterOutlookChipItem.room:
        return LocaleKeys.calendar_meeting_room.tr;
    }
  }

  /// Danh sách các "source" (GAPO, GOOGLE, MICROSOFT) muốn lấy, nối nhau bởi dấu ",". Default lấy tất cả"
  String? get source {
    switch (this) {
      case FilterOutlookChipItem.gapowork:
        return 'GAPO';
      case FilterOutlookChipItem.outlook:
        return 'MICROSOFT';
      case FilterOutlookChipItem.google:
        return 'GOOGLE';
      default:
        return null;
    }
  }
}

class FilterOutlookChip extends StatelessWidget {
  final FilterOutlookChipItem item;

  final bool isSelected;
  final VoidCallback onPresed;

  const FilterOutlookChip({
    required this.item,
    required this.isSelected,
    required this.onPresed,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return CupertinoTheme(
      data: CupertinoThemeData(
        textTheme: CupertinoTextThemeData(
          primaryColor: GPColor.contentPrimary,
          textStyle: textStyle(GPTypography.bodyMedium)?.copyWith(
            height: 1,
          ),
        ),
      ),
      child: CupertinoButton(
        minSize: 0,
        padding: EdgeInsets.zero,
        onPressed: onPresed,
        child: Container(
          height: 32,
          decoration: BoxDecoration(
            color: isSelected
                ? GPColor.functionAccentSecondary.withOpacity(0.16)
                : GPColor.bgSecondary,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              width: 2,
              style: BorderStyle.solid,
              color: isSelected
                  ? GPColor.functionAccentPrimary
                  : GPColor.bgSecondary,
            ),
          ),
          alignment: Alignment.center,
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: Row(
            children: [
              if (item.iconPath != null)
                SvgWidget(
                  item.iconPath!,
                  width: 20,
                  height: 20,
                ),
              if (item.tagColor != null)
                Container(
                  width: 8.33,
                  height: 8.33,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: item.tagColor,
                  ),
                ),
              const SizedBox(width: 8),
              Text(
                item.title,
                style: textStyle(GPTypography.bodyMedium)?.copyWith(
                  color: GPColor.contentPrimary,
                  height: 1.2,
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
