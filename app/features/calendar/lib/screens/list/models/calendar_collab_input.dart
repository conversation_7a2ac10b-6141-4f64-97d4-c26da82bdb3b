import 'package:flutter/foundation.dart';
import 'package:gp_core/core.dart';

class CollabInputAdapter {
  final String collabGroupId;
  final bool isAdmin;
  final num screenTopPadding;

  const CollabInputAdapter({
    required this.collabGroupId,
    required this.isAdmin,
    required this.screenTopPadding,
  });
}

class CalendarCollabInput extends CollabInputAdapter with EquatableMixin {
  CalendarCollabInput({
    required super.collabGroupId,
    required super.isAdmin,
    required super.screenTopPadding,
  });

  factory CalendarCollabInput.fromJson(Map<String, dynamic> json) {
    return CalendarCollabInput.fromNativeArguments(json);
  }

  Map<String, dynamic> toJson() {
    return {
      'is_admin': isAdmin,
      'collab_id': collabGroupId,
      'screen_top_padding': screenTopPadding,
    };
  }

  static CalendarCollabInput fromNativeArguments(dynamic arguments) {
    final collabGroupId = ParserHelper.parseString(arguments['collab_id']);
    final isAdmin = ParserHelper.parseBool(arguments['is_admin']);
    final screenTopPadding =
        ParserHelper.parseNum(arguments['screen_top_padding']);

    if (isAdmin != null && collabGroupId != null) {
      return CalendarCollabInput(
        isAdmin: isAdmin,
        collabGroupId: collabGroupId,
        screenTopPadding: screenTopPadding ?? 0,
      );
    }
    final message =
        'passed arguments
collabGroupId: $collabGroupId, isAdmin:$isAdmin';
    logDebug(message);
    throw ErrorDescription(message);
  }

  @override
  List<Object?> get props => [collabGroupId, isAdmin, screenTopPadding];
}
