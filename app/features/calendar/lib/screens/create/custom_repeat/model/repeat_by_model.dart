import 'package:gp_core/core.dart' hide interval;

import '../../models/calendar_event_repeat_option.dart';
import '../../models/calendar_event_schedule_type.dart';

extension RepeatByModelExt on RRuleModel {
  CalendarEventScheduleType? getCest() {
    if (isDaily) {
      return CalendarEventScheduleType.daily;
    } else if (isWeekly) {
      return CalendarEventScheduleType.weekly;
    } else if (isMonthly) {
      return CalendarEventScheduleType.monthly;
    } else if (isYearly) {
      return CalendarEventScheduleType.yearly;
    }

    return null;
  }

  // --------- COMMANDS --------- \

  CalendarEventRepeatOptionEnum? getCeroEByRRule(DateTime dateTime) {
    /*
      Nếu rRule khác những rule cơ bản, lấy theo `getCeroE`
    */

    if (hasCount || hasUntil) {
      return CalendarEventRepeatOptionEnum.custom;
    }
    // Lặp lại 1 ngày 1 lần
    bool isDefaultInterval = int.parse(interval) == 1;

    // Lặp lại 1 tuần 1 lần
    bool isDefaultDailyRule = isDaily && isDefaultInterval;

    // Lặp lại vào [Thứ X] hàng tuần
    bool isDefaultWeeklyRule =
        isWeekly && byDay == dateTime.weekDayStr && isDefaultInterval;

    // Lặp lại vào ngày [X] hàng tháng
    bool isDefaultMonthlyRule =
        isMonthly && byMonthDay == "${dateTime.day}" && isDefaultInterval;

    // Lặp lại vào các ngày trong tuần (từ thứ 2 đến thứ 6)
    bool isDefaultWeeklyWeekdayRule =
        isWeekly && containsWeekday && isDefaultInterval;

    // Lặp lại vào [Thứ X] [tuần thứ Y] hàng tháng
    final weekPosition = dateTime.weekOfMonth;
    bool isDefaultMonthlyByAMonthdayRule = isMonthly &&
        bySetPos == "$weekPosition" &&
        byDay == dateTime.weekDayStr &&
        isDefaultInterval;

    if (!isDefaultDailyRule &&
        !isDefaultWeeklyRule &&
        !isDefaultMonthlyRule &&
        !isDefaultWeeklyWeekdayRule &&
        !isDefaultMonthlyByAMonthdayRule) {
      return CalendarEventRepeatOptionEnum.custom;
    }

    return null;
  }
}
