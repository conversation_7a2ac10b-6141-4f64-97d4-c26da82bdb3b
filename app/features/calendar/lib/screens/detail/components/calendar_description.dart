import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:gp_core/core.dart';
import 'package:gp_markdown_widget/config/const.dart';
import 'package:gp_markdown_widget/markdown_widget.dart';

class CalendarDescription extends StatelessWidget {
  final String description;
  final int trimLines;
  final bool canEdit;
  final VoidCallback onEditPressed;
  final bool isHtmlDescription;

  const CalendarDescription(
      {super.key,
      required this.description,
      required this.onEditPressed,
      this.trimLines = 10,
      this.canEdit = false,
      this.isHtmlDescription = false});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Text(LocaleKeys.calendar_create_label_event_content.tr,
                    style: textStyle(GPTypography.headingSmall))
                .paddingSymmetric(vertical: 16),
            const Spacer(),
            if (canEdit)
              Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: onEditPressed,
                  child: Row(
                    children: [
                      SvgWidget(
                        'assets/images/svg/ic24-fill-pencil.svg',
                        width: 16,
                        height: 16,
                        color: GPColor.functionAccentWorkSecondary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                          LocaleKeys
                              .calendar_create_label_edit_event_content.tr,
                          style: textStyle(GPTypography.headingSmall)?.copyWith(
                              color: GPColor.functionAccentWorkSecondary)),
                    ],
                  ).paddingSymmetric(vertical: 16),
                ),
              ),
          ],
        ),
        Divider(thickness: 1, height: 1, color: GPColor.lineTertiary),
        const SizedBox(height: 12),
        Container(
            alignment: Alignment.topLeft,
            decoration: BoxDecoration(color: GPColor.bgPrimary),
            child: descriptionWidget(context)),
      ],
    );
  }

  /// Format link khi event có bật meet
  String formatGGMeetDescription() {
    String formatedString = description;
    final index = description.indexOf('Google Meet: https://meet.google.com/');
    bool isGGEvent = index >= 0;
    if (isGGEvent) {
      final linkGG = description.substring(index + 13, index + 49);
      final isValidUrl = Uri.tryParse(linkGG)?.hasAbsolutePath ?? false;
      if (isValidUrl) {
        formatedString = description.replaceRange(
            index + 13, index + 49, '<a href="$linkGG">$linkGG</a>');
      }
    }
    return formatedString;
  }

  Widget descriptionWidget(BuildContext context) {
    if (isHtmlDescription) {
      final htmlDescription =
          formatGGMeetDescription().replaceAll('
', '</br>');
      // Blockquote ở outlook có padding margin đơn vị ex chưa support nên chuyển về em
      return HtmlWidget(
        htmlDescription
            .replaceAll('padding-left:1ex', 'padding-left:1em')
            .replaceAll('margin-left:0.8ex', 'margin-left:0.8em'),
        onTapUrl: (url) async {
          Deeplink.onTapUrl(url);
          return true;
        },
      );
    } else {
      return MarkdownWidget(
        data: description,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        lineController: LineController(
          maxLines: trimLines,
          showView: true,
          loadMoreStr: LocaleKeys.task_readMore.tr,
          viewLessStr: LocaleKeys.task_showLess.tr,
        ),
        screenWidth: MediaQuery.of(context).size.width,
        styleConfig: StyleConfig(
          titleConfig: GPTitleConfig.titleConfig,
          ulConfig: GPBulletConfig.ulConfig,
          olConfig: GPBulletConfig.olConfig,
          pConfig: GPPConfig.pConfig(
            linkStyle: textStyle(GPTypography.bodyMedium)?.copyWith(
                // fontWeight: FontWeight.w400,
                // decoration: TextDecoration.underline,
                color: GPColor.blue),
            onTapUrl: (href) => Deeplink.onTapUrl(href),
          ),
          preConfig: GPPreConfig.preConfig,
          tableConfig: GPTableConfig.tableConfig,
          blockQuoteConfig: GPBlockQuoteConfig.blockQuoteConfig,
        ),
      );
    }
  }

  void onTapDescriptionElement(String element) {
    final TextElementType? type = Utils.textElementType(element);
    if (type is TextElementType) {
      switch (type) {
        case TextElementType.hashtag:
          Deeplink.openHashtag(element);
          break;
        case TextElementType.url:
          Deeplink.onTapUrl(element);
          break;
        case TextElementType.phonenumber:
          Deeplink.onTapUrl(element);
          break;
        case TextElementType.tag:
          Deeplink.openUser(element);
          break;
      }
    } else {
      logDebug('Unable to detect element type with input: $element');
    }
  }
}
