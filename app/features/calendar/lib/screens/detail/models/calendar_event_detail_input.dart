import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_calendar/screens/list/models/calendar_collab_input.dart';
import 'package:gp_feat_calendar/screens/list/models/event_list_response.dart';
import 'package:gp_feat_calendar/screens/list/models/room_approval.dart';

import 'flutter_calendar_detail_input.dart';

enum CalendarDetailScenario { native_, noti, calendar }

class CalendarEventDetailInput {
  final CalendarDetailScenario scenario;
  final CalendarCollabInput? calendarCollabInput;
  CalendarEventList? event;
  final String? eventId;
  final String? identity;
  final int? startAt;
  final int? endAt;
  final String? googleEmail;
  final RoomApproval? roomApproval;

  /// để chuyển ws cho noti phê duyệt
  final String? workspaceId;

  CalendarEventDetailInput({
    required this.scenario,
    this.calendarCollabInput,
    this.eventId,
    this.event,
    this.identity,
    this.endAt,
    this.startAt,
    this.googleEmail,
    this.roomApproval,
    this.workspaceId,
  });

  factory CalendarEventDetailInput.fromJson(Map<String, dynamic> json) {
    final s = CalendarDetailScenario.values
        .byName(ParserHelper.parseString(json['scenario']) ?? '');
    return CalendarEventDetailInput(
      scenario: s,
      calendarCollabInput: json['calendarCollabInput'] != null
          ? CalendarCollabInput.fromJson(
              Map<String, dynamic>.from(json['calendarCollabInput']))
          : null,
      event: json['event'] != null
          ? CalendarEventList.fromJson(Map<String, dynamic>.from(json['event']))
          : null,
      eventId: json['eventId'],
      endAt: json['endAt'],
      identity: json['identity'],
      startAt: json['startAt'],
      googleEmail: json['googleEmail'],
      roomApproval: json['roomApproval'] != null
          ? RoomApproval.fromJson(
              Map<String, dynamic>.from(json['roomApproval']))
          : null,
      workspaceId: json['workspaceId'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'scenario': scenario.name,
      'calendarCollabInput': calendarCollabInput?.toJson(),
      'event': event?.toJson(),
      'eventId': eventId,
      'identity': identity,
      'startAt': startAt,
      'endAt': endAt,
      'googleEmail': googleEmail,
      'roomApproval': roomApproval?.toJson(),
      'workspaceId': workspaceId,
    };
  }

  static CalendarEventDetailInput? fromFlutterCalendarDetailInput(
    FlutterCalendarDetailInput e, {
    required CalendarDetailScenario scenario,
    String? workspaceId,
  }) {
    CalendarCollabInput? input;
    RoomApproval? roomApproval;

    switch (e.type) {
      case FlutterCalendarDetailInputDeeplinkType.calendarDetail:
        break;
      case FlutterCalendarDetailInputDeeplinkType.collabMeeting:
        break;
      case FlutterCalendarDetailInputDeeplinkType.collabMeetingDetail:
        input = CalendarCollabInput(
          collabGroupId: e.collabGroupId ?? '',
          isAdmin: false,
          screenTopPadding: 0,
        );
        break;
      case FlutterCalendarDetailInputDeeplinkType.roomApproval:
        roomApproval = RoomApproval(
          eventId: e.eventId,
          roomId: e.roomId,
        );
        break;
    }

    return CalendarEventDetailInput(
      scenario: scenario,
      eventId: e.eventId,
      calendarCollabInput: input,
      endAt: e.endAt,
      identity: e.eventIdentity,
      startAt: e.startAt,
      event: null,
      roomApproval: roomApproval,
      workspaceId: workspaceId,
    );
  }

  static CalendarEventDetailInput fromDynamicInputCalledFromNative(
      dynamic input) {
    final errorSuffix = '
Received Input:
${input.toString()}';
    if (input is Map) {
      final object =
          FlutterCalendarDetailInput.fromJson(Map<String, dynamic>.from(input));
      final result = CalendarEventDetailInput.fromFlutterCalendarDetailInput(
        object,
        scenario: CalendarDetailScenario.native_,
      );
      if (result != null) {
        return result;
      } else {
        throw ErrorDescription(
            'Couldnt parse FlutterCalendarDetailInput into CalendarEventDetailInput, $errorSuffix');
      }
    } else {
      throw UnsupportedError(
          'Unsupported argument
Expect FlutterCalendarDetailInput json object, $errorSuffix');
    }
  }
}
