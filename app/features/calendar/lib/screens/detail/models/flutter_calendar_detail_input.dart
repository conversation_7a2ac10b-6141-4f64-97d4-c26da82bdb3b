import 'package:gp_core/core.dart';
import 'package:gp_core/utils/gp_sentry.dart';

enum FlutterCalendarDetailInputDeeplinkType {
  collabMeeting,
  collabMeetingDetail,
  calendarDetail,
  roomApproval,
}

class FlutterCalendarDetailInput {
  /// Deeplink sample
  /// work.gapo://calendar/{eventId}?start_at=1667802600000&end_at=1667806200000
  /// work.gapo://calendar/62949d7968e124b8a5181b5b?start_at=1667802600000&end_at=1667806200000
  /// gapowork.vn/collab/{collabID}/meet/{meetingID}
  final FlutterCalendarDetailInputDeeplinkType type;
  final String? eventId;
  final String? eventIdentity;
  final String? deeplink;
  final String? collabGroupId;
  final String? roomId;
  final int? startAt;
  final int? endAt;

  FlutterCalendarDetailInput({
    required this.type,
    this.eventId,
    this.deeplink,
    this.collabGroupId,
    this.eventIdentity,
    this.endAt,
    this.startAt,
    this.roomId,
  });

  static FlutterCalendarDetailInput? fromDeeplink(String deeplink) {
    deeplink = deeplink.toLowerCase();
    if (deeplink.endsWith('/')) {
      deeplink = deeplink.substring(0, deeplink.length - 1);
    }
    if (deeplink.contains('work.gapo://')) {
      deeplink =
          deeplink.replaceFirst('work.gapo://', 'https://www.gapowork.com/');
    }
    if (deeplink.contains('work-op://')) {
      deeplink =
          deeplink.replaceFirst('work-op://', 'https://www.gapowork.com/');
    }
    try {
      final uri = Uri.parse(deeplink.toLowerCase());
      final pathSegments = uri.pathSegments;
      if (pathSegments.contains('calendar')) {
        if (pathSegments.contains('room')) {
          final eventId =
              ParserHelper.parseString(uri.queryParameters['event_id']);
          final roomId = pathSegments[pathSegments.length - 1 - 1];
          return FlutterCalendarDetailInput(
            type: FlutterCalendarDetailInputDeeplinkType.roomApproval,
            eventId: eventId,
            roomId: roomId,
            deeplink: deeplink,
          );
        }

        final eventId = pathSegments.last;
        if (eventId.isNotEmpty &&
            pathSegments[pathSegments.length - 1 - 1] == 'calendar') {
          final identity =
              ParserHelper.parseString(uri.queryParameters['identity']);
          final startAt =
              ParserHelper.parseInt(uri.queryParameters['start_at']);
          final endAt = ParserHelper.parseInt(uri.queryParameters['end_at']);
          return FlutterCalendarDetailInput(
            eventId: eventId,
            type: FlutterCalendarDetailInputDeeplinkType.calendarDetail,
            startAt: startAt,
            endAt: endAt,
            deeplink: deeplink,
            eventIdentity: identity,
          );
        }
        return null;
      } else if (pathSegments.contains('collab') &&
          pathSegments.contains('meet')) {
        if (pathSegments.last == 'meet') {
          if (pathSegments.length > 2) {
            final collabGroupId = pathSegments[pathSegments.length - 1 - 1];
            return FlutterCalendarDetailInput(
              type: FlutterCalendarDetailInputDeeplinkType.collabMeeting,
              deeplink: deeplink,
              collabGroupId: collabGroupId,
            );
          }
        } else {
          if (pathSegments.length > 3) {
            final collabGroupId = pathSegments[pathSegments.length - 1 - 1 - 1];
            final identity =
                ParserHelper.parseString(uri.queryParameters['identity']);
            final startAt =
                ParserHelper.parseInt(uri.queryParameters['start_at']);
            final endAt = ParserHelper.parseInt(uri.queryParameters['end_at']);
            return FlutterCalendarDetailInput(
              eventId: pathSegments.last,
              type: FlutterCalendarDetailInputDeeplinkType.collabMeetingDetail,
              deeplink: deeplink,
              collabGroupId: collabGroupId,
              eventIdentity: identity,
              startAt: startAt,
              endAt: endAt,
            );
          }
        }
      }
    } catch (e, trace) {
      GPCoreTracker().appendError(
        'Flutter.calendar.FlutterCalendarDetailInput.error',
        data: {'error': e, 'stacktrace': trace},
      );
      GPCoreTracker().sendLog(
        message: 'Flutter.calendar.FlutterCalendarDetailInput.error',
        trackerType: GPTrackerType.calendar,
      );

      logDebug(e);
      logDebug(trace);
      return null;
    }

    return null;
  }

  /// Ưu tiên dùng deeplink để parse ra object, nếu k được thì mới dùng json
  factory FlutterCalendarDetailInput.fromJson(Map<String, dynamic> json) {
    final deeplink = json['deeplink'] ?? json['url'];
    if (deeplink is String && deeplink.isNotEmpty) {
      final attemp = FlutterCalendarDetailInput.fromDeeplink(deeplink);
      if (attemp != null) {
        return attemp;
      }
    }
    final result = FlutterCalendarDetailInput(
      eventId: json['eventId'] ?? json['id'],
      deeplink: deeplink,
      collabGroupId: json['collabGroupId'],
      type: FlutterCalendarDetailInputDeeplinkType.calendarDetail,
    );
    if (result.eventId?.isNotEmpty == true) {
      return result;
    }
    final message =
        'Missing required paramters: eventId or deeplink or url
Received: $json';
    throw UnsupportedError(message);
  }

  // Map<String, dynamic> toJson() {
  //   return {
  //     'eventId': eventId,
  //     'deeplink': deeplink,
  //     'collabGroupId': collabGroupId,
  //     'url': deeplink,
  //   };
  // }
}
