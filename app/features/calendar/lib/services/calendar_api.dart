import 'package:flutter/foundation.dart';
import 'package:gp_core/base/models/base_models.dart';
import 'package:gp_core/base/networking/base/api.dart';
import 'package:gp_core/configs/constants.dart';
import 'package:gp_core/models/notification/notification_status.dart';
import 'package:gp_feat_calendar/models/calendar_notification_model.dart';
import 'package:gp_feat_calendar/screens/create/models/calendar_event_type_response.dart';
import 'package:gp_feat_calendar/screens/create/models/calendar_todo_item_status.dart';
import 'package:gp_feat_calendar/screens/create/models/create_calendar_event_request.dart';
import 'package:gp_feat_calendar/screens/create/models/default_create_calendar_event_request.dart';
import 'package:gp_feat_calendar/screens/create/models/room_area.dart';
import 'package:gp_feat_calendar/screens/list/models/event_list_request.dart';
import 'package:gp_feat_calendar/screens/list/models/event_list_response.dart';
import 'package:gp_feat_calendar/screens/list/models/event_room.dart';
import 'package:gp_feat_calendar/screens/list/models/room_busy.dart';
import 'package:gp_feat_calendar/screens/list/models/sync_account_model.dart';

import 'package:gp_shared_dep/gp_shared_dep.dart';

class CalendarAPI {
  final ApiService _service = ApiService(Constants.calendarDomain,
      gpDomainChecker: GPDomainChecker.calendar);

  Future<CalendarEventList> createEvent(
      CreateCalendarEventRequest request) async {
    var body = request.toJson();
    body.removeWhere((key, value) => value == null);
    if (body['schedule'] != null) {
      body['schedule'].removeWhere((key, value) => value == null);
      body['schedule'].removeWhere(
          (key, value) => key == "recurrence" && value == "RRULE:");
    }
    final response =
        await _service.postData(endPoint: Constants.calendarEvents, body: body);
    final event = CalendarEventList.fromJson(response.data['data']);
    return event;
  }

  final ___testEventId = '630491d34f05a9e9ee578f84';
  final ___testOneEvent = false;
  Future<CalendarListEventResponse> getEvents(
      {required CalendarListEventRequest request}) async {
    Map<String, dynamic> params = {};

    if (request.eventFromAt != null) {
      params['event_from_at'] = request.eventFromAt;
    }

    if (request.eventFromTo != null) {
      params['event_to_at'] = request.eventFromTo;
    }

    if (request.isGen != null) {
      params['is_gen'] = request.isGen;
    } else {
      params['is_gen'] = 0;
    }

    // testing only
    if (kDebugMode && ___testOneEvent) {
      request.ids = [___testEventId];
    }
    if (request.ids != null && request.ids!.isNotEmpty) {
      params['ids'] = request.ids!.join(",");
    }

    if (request.type?.isNotEmpty == true) {
      params['type'] = request.type;
    }

    if (request.identity != null) {
      params['identity'] = request.identity;
    }

    if (request.ownerIds?.isNotEmpty == true) {
      params['owner_ids'] = request.ownerIds!.join(",");
      params['type'] = "Meeting";
    }

    if (request.attendees != null) {
      params['attendees'] = request.attendees;
    }

    if (request.fields != null) {
      params['fields'] = request.fields!.join(',');
    }

    if (request.source?.isNotEmpty == true) {
      params['source'] = request.source!.join(',');
    }

    if (request.getRoom == 1) {
      params['get_room'] = 1;
    }

    params['return_deleted_event'] = true;

    final response = await _service.getData(endPoint: "/events", query: params);
    CalendarListEventResponse result =
        CalendarListEventResponse.fromJson(response.data);

    // testing only
    if (kDebugMode && ___testOneEvent) {
      final event =
          result.data.firstWhere((element) => element.id == ___testEventId);
      result = CalendarListEventResponse(data: [event]);
    }

    return result;
  }

  Future<CalendarListEventResponse> getCollabEvents(
      {required CalendarListEventRequest request}) async {
    Map<String, dynamic> params = {};

    if (request.eventFromAt != null) {
      params['event_from_at'] = request.eventFromAt;
    }

    if (request.eventFromTo != null) {
      params['event_to_at'] = request.eventFromTo;
    }

    if (request.isGen != null) {
      params['is_gen'] = request.isGen;
    } else {
      params['is_gen'] = 0;
    }

    // testing only
    if (kDebugMode && ___testOneEvent) {
      request.ids = [___testEventId];
    }
    if (request.ids != null && request.ids!.isNotEmpty) {
      params['ids'] = request.ids!.join(",");
    }

    if (request.type?.isNotEmpty == true) {
      params['type'] = request.type;
    }

    if (request.identity != null) {
      params['identity'] = request.identity;
    }

    if (request.ownerIds?.isNotEmpty == true) {
      params['owner_ids'] = request.ownerIds!.join(",");
      params['type'] = "Meeting";
    }

    if (request.attendees != null) {
      params['attendees'] = request.attendees;
    }

    if (request.fields != null) {
      params['fields'] = request.fields!.join(',');
    }

    if (request.source?.isNotEmpty == true) {
      params['source'] = request.source!.join(',');
    }

    if (request.getRoom == 1) {
      params['get_room'] = 1;
    }

    if (request.collabId?.isNotEmpty == true) {
      params['collab_id'] = request.collabId;
    }

    final response = await _service.getData(endPoint: "/events", query: params);
    CalendarListEventResponse result =
        CalendarListEventResponse.fromJson(response.data);
    for (var e in result.data) {
      e.isCollabGroupEventUI = true;
    }

    // testing only
    if (kDebugMode && ___testOneEvent) {
      final event =
          result.data.firstWhere((element) => element.id == ___testEventId);
      result = CalendarListEventResponse(data: [event]);
    }

    return result;
  }

  Future<bool> deleteEvent(
      String eventId,
      //  List<String> identities,
      EditCalendarEventRequestOption editOption) async {
    try {
      final _ = await _service.delete(
        endPoint: "/events/$eventId",
        body: editOption.toJson(),
      );
      return true;
    } catch (e) {
      rethrow;
    }
  }

  Future deleteEvents(
      List<String> eventIds, EditCalendarEventRequestOption editOption) async {
    for (var eventId in eventIds) {
      deleteEvent(eventId, editOption);
    }
  }

  Future<CalendarEventList> editEvent(
      String id, CreateCalendarEventRequest request) async {
    if (request is DefaultCreateCalendarEventRequest) {
      request.attachmentFiles
          ?.removeWhere((element) => element["mimeType"] != null);
    }
    var body = request.toJson();
    body.removeWhere((key, value) => value == null && key != "meeting_type_id");
    if (body['schedule'] != null) {
      body['schedule'].removeWhere((key, value) => value == null);
      body['schedule'].removeWhere(
          (key, value) => key == "recurrence" && value == "RRULE:");
    }
    final response = await _service.patchData(
        endPoint: '${Constants.calendarEvents}/$id', body: body);
    final event = CalendarEventList.fromJson(response.data["data"]);
    return event;
  }

  Future<CalendarEventList> dragEvent({
    required String id,
    required DateTime startTime,
    required bool sendNoti,
    required bool isRecursive,
    required String identity,
    required String targetIdentity,
    // required List value,
    required DateTime endTime,
    // CalendarEventType type = CalendarEventType.task,
    // CalendarEventScheduleType scheduleType = CalendarEventScheduleType.daily,
  }) async {
    Map<String, dynamic> body = {
      'identity': targetIdentity,
      'send_notify': sendNoti,
      // 'type': type.stringValue,
      // 'start_at': startTime.millisecondsSinceEpoch,
      // 'end_at': endTime.millisecondsSinceEpoch,
      'schedule': {
        // 'type': scheduleType.stringValue,
        'from_date': startTime.millisecondsSinceEpoch,
        // 'end_date': endTime.millisecondsSinceEpoch,
        'start_hour': startTime.hour,
        'start_minute': startTime.minute,
        'end_hour': endTime.hour,
        'end_minute': endTime.minute,
        // 'value': value,
      }
    };
    if (isRecursive) {
      body['options'] = {
        'method': EditCalendarEventMethod.individuals.stringValue,
        'identities': [identity],
      };
    }
    final response = await _service.postData(
        endPoint: Constants.dragCalendarEvent.replaceFirst('%', id),
        body: body);
    final event = CalendarEventList.fromJson(response.data["data"]);
    return event;
  }

  Future<CalendarEventList> undoDraggedEvent({required String id}) async {
    final response = await _service.postData(
        endPoint: Constants.undoDraggedCalendarEvent.replaceFirst('%', id),
        body: {});
    final event = CalendarEventList.fromJson(response.data["data"]);
    return event;
  }

  Future<CalendarEventList> markTodoStatus({
    required String id,
    required CalendarTodoStatus newStatus,
    required List<String> originalCompletedIdentities,
    required String identity,
  }) async {
    if (newStatus == CalendarTodoStatus.done) {
      originalCompletedIdentities.add(identity);
    } else {
      originalCompletedIdentities.remove(identity);
    }
    final body = {'identities': originalCompletedIdentities};
    final response = await _service.postData(
        endPoint: Constants.calendarEvents + '/$id/complete-tasks', body: body);
    final event = CalendarEventList.fromJson(response.data["data"]);
    return event;
  }

  Future<ListAPIResponse<CalendarNotificationModel>> getNotifications(
      {String nextLink = "", int limit = 30}) async {
    var params = {"limit": limit.toString()};
    if (nextLink.isNotEmpty) params.addAll(Uri.splitQueryString(nextLink));
    final response = await _service.getData(endPoint: '/notify', query: params);
    final data = response.data;
    ListAPIResponse<CalendarNotificationModel> result =
        ListAPIResponse.fromJson(
      data,
      (jsonData) => CalendarNotificationModel.fromJson(jsonData),
    );
    return result;
  }

  Future<dynamic> markReadANotification(String notiId) async {
    final response =
        await _service.patchData(endPoint: "/notify/marked", body: {
      "status": NotificationStatus.read.value,
      "ids": notiId,
    });
    return response;
  }

  Future<dynamic> signInGoogle(
      {required dynamic data, required bool hasSync}) async {
    final Map<String, dynamic> body = {
      "email": data["email"],
      "avatar": data["avatar"],
      "authorization_code": data["authorization_code"],
      "source": hasSync ? "SYNC" : "MEETING",
    };

    final response =
        await _service.postData(endPoint: Constants.signInGG, body: body);
    return response;
  }

  Future<dynamic> signOutGoogle(String email, {bool? isDeleteEvent}) async {
    final Map<String, dynamic> body = {
      "email": email,
      "remove_gg_events": isDeleteEvent ?? false,
    };
    final response = await _service.postData(
      endPoint: Constants.signOutGG,
      body: body,
    );
    return response;
  }

  Future<dynamic> trackSyncGoogle() async {
    final response = await _service.getData(endPoint: Constants.trackSyncGG);
    return response.data["data"];
  }

  Future<dynamic> eventTrackSync({String? eventId}) async {
    Map<String, String>? query;
    if (eventId != null) query = {"event_id": eventId};
    final response = await _service.getData(
        endPoint: Constants.eventTrackSync, query: query);
    return response.data["data"];
  }

  /// Truyền email thì phải có scope hoặc không truyền gì cả
  Future<dynamic> googleScopeChecking(
      {String? email, List<String>? scopes}) async {
    // BE mới hỗ trợ 1 scope duy nhất
    final String? scope = scopes?.firstOrNull;
    final Map<String, dynamic> query = {};
    if (email != null) query['email'] = email;
    if (scopes != null) query['scope'] = scope;
    if (email != null && scopes == null) query.remove('email');
    final response = await _service.getData(
        endPoint: Constants.ggScopeChecking, query: query);
    return response.data["data"];
  }

  Future googleAccessToken({String? email}) async {
    final response = await _service
        .getData(endPoint: Constants.ggToken, query: {"email": email});
    return response.data["data"];
  }

  Future<CalendarEventList> addAttachmentFiles(
      String id, Map attachmentFileJson) async {
    final body = {'attachments': attachmentFileJson};
    final response = await _service.patchData(
        endPoint: '${Constants.calendarEvents}/$id', body: body);
    final event = CalendarEventList.fromJson(response.data['data']);
    return event;
  }

  Future<dynamic> markReadAllNotification() async {
    final response = await _service.patchData(
        endPoint: "/notify/marked",
        body: {"status": NotificationStatus.read.value});
    return response;
  }

  Future<CalendarEventList> attendeeUpdateEvent(
      String id, Map<String, dynamic> request) async {
    request.removeWhere((key, value) => value.isEmpty);
    final response = await _service.patchData(
        endPoint: '${Constants.calendarEvents}/$id/attendee/update',
        body: request);
    final event = CalendarEventList.fromJson(response.data["data"]);
    return event;
  }

  Future<dynamic> postSyncAuth({
    String code = 'Microsoft',
    String? userId,
    String? email,
    String? avatar,
    String? authorizationCode,
    int? expireAt,
    String? accessToken,
    String? refreshToken,
    bool sendNotifcation = false,
  }) async {
    final Map<String, dynamic> body = {
      'code': code,
      'userId': userId,
      'email': email,
      'avatar': avatar,
      'authorization_code': authorizationCode,
      'expire_at': expireAt,
      'access_token': accessToken,
      'refresh_token': refreshToken,
      'send_notification_success': sendNotifcation,
    };

    final response =
        await _service.postData(endPoint: Constants.syncAuth, body: body);
    return response;
  }

  Future<List<SyncOutlookAccountModel>> getSyncAuth({
    String code = 'Microsoft',
  }) async {
    /// query parameters
    /// fields
    /// Các trường cần lấy nối nhau bởi dấu ",". Mặc định sẽ trả ra: code, email, avatar, access_token"
    final query = {'code': code};
    final response =
        await _service.getData(endPoint: Constants.syncAuth, query: query);
    if (response.data['data'] != null) {
      return List<SyncOutlookAccountModel>.from(
        response.data['data'].map(
          (x) => SyncOutlookAccountModel.fromJson(x),
        ),
      );
    }

    return [];
  }

  /// Dừng đồng bộ Event. Nếu không truyền email thì sẽ dừng
  /// đồng bộ tất cả các email gắn với user này
  Future<dynamic> deleteSyncAuth({
    String code = 'Microsoft',
    required String email,
  }) async {
    final body = {
      'code': code,
      'email': email,
    };
    final response =
        await _service.delete(endPoint: Constants.syncAuth, body: body);
    return response;
  }

  Future<List<EventRoom>> getListRoom(
      {required int from,
      required int to,
      String? locationId,
      String? ignoreEventId,
      String? name}) async {
    Map<String, dynamic> params = {'from_at': from, 'to_at': to};
    if (locationId?.isNotEmpty ?? false) {
      params['location_id'] = locationId;
    }

    if (ignoreEventId?.isNotEmpty ?? false) {
      params['ignore_event_id'] = ignoreEventId;
    }

    if (name?.isNotEmpty ?? false) {
      params['name'] = name;
    }

    final response =
        await _service.getData(endPoint: '/room/blocks', query: params);
    final rooms = List<EventRoom>.from(response.data['data'].map(
      (element) => EventRoom.fromJson(element),
    ));
    return rooms;
  }

  Future<List<EventRoom>> getAllRoom() async {
    final response = await _service.getData(endPoint: '/room');
    final rooms = List<EventRoom>.from(response.data['data'].map(
      (element) => EventRoom.fromJson(element),
    ));
    return rooms;
  }

  Future<List<RoomArea>> getListRoomArea() async {
    final response = await _service.getData(endPoint: '/locations');
    final areas = List<RoomArea>.from(response.data['data'].map(
      (element) => RoomArea.fromJson(element),
    ));
    return areas;
  }

  Future<CalendarEventList> acceptDeniedEvent({
    required String id,
    required bool isAccept,
    EditCalendarEventRequestOption? editOption,
  }) async {
    final Map<String, dynamic> body = {
      'is_accepted': isAccept,
    };

    if (editOption != null) {
      body['options'] = editOption.toJson();
    }

    final response =
        await _service.postData(endPoint: '/events/$id/acceptance', body: body);
    final event = CalendarEventList.fromJson(response.data["data"]);
    return event;
  }

  Future<void> acceptDeniedRoom({
    required String id,
    required bool isApprove,
  }) async {
    final Map<String, dynamic> body = {
      'is_approve': isApprove,
      'approval_id': id,
    };

    final response =
        await _service.postData(endPoint: '/approval/rooms', body: body);
  }

  Future<List<RoomBusy>> getRoomBlock(
      {required String id, required int from, required int to}) async {
    Map<String, dynamic> params = {'from_at': from, 'to_at': to};
    final response =
        await _service.getData(endPoint: '/room/$id/block', query: params);
    final listRoomBlock = List.from(response.data["data"]);
    final roomBlocks = listRoomBlock.map((e) => RoomBusy.fromJson(e)).toList();
    return roomBlocks;
  }

  Future<RoomBusy> getRoomApproval(
      {required String roomId, required String eventId}) async {
    Map<String, dynamic> params = {'event_id': eventId};
    final response =
        await _service.getData(endPoint: '/room/$roomId/block', query: params);
    final listRoomBlock = List.from(response.data["data"]);
    final roomBlocks = listRoomBlock.map((e) => RoomBusy.fromJson(e)).toList();
    return roomBlocks.first;
  }

  Future<Map<String, dynamic>?> getConfigs() async {
    final response = await _service.getData(endPoint: '/configs');
    return response.data;
  }

  Future<CalendarEventTypeResponse> getEventTypes() async {
    try {
      final response = await _service.getData(endPoint: '/meeting-types');
      return CalendarEventTypeResponse.fromJson(response.data);
    } catch (e) {
      // Return empty list if API fails
      return CalendarEventTypeResponse(eventTypes: []);
    }
  }
}
