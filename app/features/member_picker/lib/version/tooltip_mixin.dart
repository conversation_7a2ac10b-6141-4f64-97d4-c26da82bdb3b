import 'package:gp_core/core.dart';

// for version 1.0.6
mixin TooltipMixin {
  void addedTooltipByItem(String key, {String? isAddedByAdminStr}) {
    tooltipControllers.putIfAbsent(
      key,
      () => AssigneeTooltipModel(
        tooltipController: SuperTooltipController(),
        isAddedByAdminStr: isAddedByAdminStr,
        onTap: () async {
          await hideSelectedTooltip();
          await showTooltip(key);
        },
      ),
    );

    tooltipSelectedControllers.putIfAbsent(
      key,
      () => AssigneeTooltipModel(
        tooltipController: SuperTooltipController(),
        isAddedByAdminStr: isAddedByAdminStr,
        onTap: () async {
          await hideTooltip();
          await showSelectedTooltip(key);
        },
      ),
    );
  }

  // ---------- tooltip function cho list selected assignee ---------- \
  final Map<String, AssigneeTooltipModel> tooltipControllers = {};

  String _currentTooltipId = "";

  AssigneeTooltipModel? tooltipById(String id) {
    return tooltipControllers[id];
  }

  SuperTooltipController? get _currentTooltip =>
      tooltipControllers[_currentTooltipId]?.tooltipController;

  Future showTooltip(String id) async {
    if (_currentTooltipId != id) {
      // bấm vào tooltip khác, ẩn tooltip trước đó
      await hideTooltip();
    }

    _currentTooltipId = id;

    // tooltip đang ẩn -> hiện và ngược lại
    if (_currentTooltip?.isVisible == false) {
      await _currentTooltip?.showTooltip();
    } else {
      await hideTooltip();
    }
  }

  Future hideTooltip() async {
    if (_currentTooltipId.isEmpty) return;

    return await _currentTooltip?.hideTooltip();
  }

  // ---------- tooltip function cho list selected assignee ---------- \
  final Map<String, AssigneeTooltipModel> tooltipSelectedControllers = {};

  String _currentTooltipSelectedAssigneeId = "";

  AssigneeTooltipModel? tooltipSelectedByAssigneeId(String assigneeId) {
    return tooltipSelectedControllers[assigneeId];
  }

  SuperTooltipController? get _currentTooltipSelectedAssignee =>
      tooltipSelectedControllers[_currentTooltipSelectedAssigneeId]
          ?.tooltipController;

  Future showSelectedTooltip(String id) async {
    if (_currentTooltipSelectedAssigneeId != id) {
      // bấm vào tooltip khác, ẩn tooltip trước đó
      await hideSelectedTooltip();
    }

    _currentTooltipSelectedAssigneeId = id;

    // tooltip đang ẩn -> hiện và ngược lại
    if (_currentTooltipSelectedAssignee?.isVisible == false) {
      await _currentTooltipSelectedAssignee?.showTooltip();
    } else {
      await hideSelectedTooltip();
    }
  }

  Future hideSelectedTooltip() async {
    if (_currentTooltipSelectedAssigneeId.isEmpty) return;

    return await _currentTooltipSelectedAssignee?.hideTooltip();
  }
}
