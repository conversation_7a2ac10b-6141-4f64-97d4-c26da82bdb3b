import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/models/base_picker_model.dart';

import '../picker_mixin.dart';

const kListWidget = "kListWidget";
const kSelectedListWidget = "kSelectedListWidget";

abstract class BasePickerController<T extends BasePickerModel>
    extends BaseListController<T> with OrgChatConfigTab {
  BasePickerController({GPConnection? gpConnection})
      : super(gpConnection ?? GPConnectionConcrete());

  // ---------- abstract --------- \
  Future fetchData();
  // ---------- \

  final TextEditingController searchTextEditingController =
      TextEditingController();

  final RefreshController refreshController = RefreshController();

  /// local data, using for searching (local)
  final List<T> _localData = [];

  /// đã khởi tạo thành công hay chưa
  bool isInitialized = false;

  // list ids chứa các phần tử selected đượ<PERSON> truyền vào khi init
  final List<String> _selectedIdsInits = [];

  /// list generic chứa các phần tử hiện được selected
  final List<T> selectedItems = [];

  bool get hasSelectedItems => selectedItems.isNotEmpty;

  String get searchStr =>
      TiengViet.parse(searchTextEditingController.text.toLowerCase());

  int currentPage = 1;
  int totalPage = 1;

  @override
  bool get canLoadMore => currentPage <= totalPage;

  void setOnPickModeListener(Function onPickOneCallback) {
    this.onPickOneCallback = onPickOneCallback;
  }

  List<T> get selectedItemsNoDuplicate =>
      listItem.where((e) => e.rxIsSelected.value).toSet().toList();

  void setSelectedItemsByIds(List<String>? ids) {
    if (ids == null || ids.isEmpty) return;

    _selectedIdsInits.addAll(ids.toSet());
  }

  void setSelectedItems(List<T>? data) {
    if (data == null || data.isEmpty) return;

    selectedItems.clear();

    selectedItems.addAll(data.toSet());
  }

  void setSelectedItem(T? item) {
    if (item == null) return;

    if (selectedItems.contains(item)) return;

    selectedItems.add(item);
  }

  void onSelected(T item) {
    if (item.rxIsSelected.value) {
      selectedItems.add(item);
    } else {
      final itemToRemove =
          selectedItems.firstWhereOrNull(((element) => element.id == item.id));
      selectedItems.remove(itemToRemove);
    }

    update([kSelectedListWidget]);
  }

  void onRemoveSelectedItem(T item) {
    selectedItems.remove(
        selectedItems.firstWhereOrNull(((element) => element.id == item.id)));
    listItem
        .firstWhereOrNull(((element) => element.id == item.id))
        ?.rxIsSelected
        .value = false;

    update([kSelectedListWidget]);
  }

  Future init() async {
    if (isInitialized) return;

    searchTextEditingController.addListener(() {
      filter();
    });

    await getListItems();

    isInitialized = true;
  }

  void removeAllSelected() {
    for (var e in listItem) {
      e.rxIsSelected.value = false;
    }

    update([kListWidget]);
  }

  void _updateSelectedItems() {
    for (var element in selectedItems) {
      for (var e in listItem) {
        if (e.id == element.id) {
          e.rxIsSelected.value = true;
          break;
        }
      }
    }
  }

  @override
  Future reload() async {
    final response = await super.reload();

    selectedItems.clear();

    update([kSelectedListWidget]);

    return response;
  }

  @override
  Future getListItems() async {
    if (isLoading.value) return;

    isLoading.value = true;
    try {
      var response = await fetchData();

      handleResponse(response, "");

      if (_selectedIdsInits.isNotEmpty) {
        for (var e in _selectedIdsInits) {
          setSelectedItem(listItem.firstWhereOrNull((p0) => p0.id == e));
        }
        _selectedIdsInits.clear();
      }

      _updateSelectedItems();
    } catch (error, s) {
      logDebug("get list error $error");
      handleError(error, s);
      refreshController.loadFailed();
      refreshController.refreshFailed();
    }

    refreshController.refreshCompleted();
    refreshController.loadComplete();

    _localData.clear();
    _localData.addAll(listItem);

    update([kListWidget]);

    if (selectedItems.isNotEmpty) {
      update([kSelectedListWidget]);
    }
  }

  Future filter() async {
    final String s = searchStr;
    listItem.clear();

    if (s.isEmpty) {
      listItem.addAll(_localData);

      update([kListWidget]);

      return;
    }

    listItem.addAll(_localData.where((e) {
      final String name = TiengViet.parse(e.name.toLowerCase());
      return name.contains(s);
    }).toList());

    update([kListWidget]);
  }

  void handleLinks(ListAPIResponse response) {
    currentPage++;
    totalPage = response.links?.totalPages ?? 0;
  }

  @override
  void handleResponse(List<T> items, String? nextLink) {
    isLoading.value = false;

    if (currentPage == 1) {
      listItem.clear();
    }
    listItem.addAll(items);
  }

  @override
  Future loadMoreItems() async {
    if (!canLoadMore || isLoading.value) {
      refreshController.loadNoData();
      return;
    }

    return await getListItems();
  }
}
