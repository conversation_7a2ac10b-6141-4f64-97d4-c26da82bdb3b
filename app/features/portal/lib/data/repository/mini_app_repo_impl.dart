import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:injectable/injectable.dart';

import '../../domain/repository/mini_app_repo.dart';
import '../data_source/remote/mini-app/mini_app.service.dart';
import '../model/request/mini_app/mini_app_get_data_by_category_params.dart';
import '../model/response/mini_app_response.dart';

@LazySingleton(as: MiniAppRepository, order: DiConstants.kDataRepositoryOrder)
@Named('kMiniAppRepository')
final class MiniAppRepositoryImpl implements MiniAppRepository {
  const MiniAppRepositoryImpl(
    @Named('kMiniAppService') this.miniAppService,
  );

  final MiniAppService miniAppService;

  @override
  Future<ListAPIResponseV2<MiniAppCategoryResponse>> getCategories({
    String? startingAfter,
  }) {
    return miniAppService.getCategories(
      startingAfter: startingAfter,
    );
  }

  @override
  Future<ListAPIResponseV2<MiniAppResponse>> getMiniApps({
        required MiniAppGetDataCategoryParams params,
  }) {
    return miniAppService.getMiniApps(
      categoryId: params.categoryId,
      limit: params.limit,
      nextLink: params.nextLink,
    );
  }
