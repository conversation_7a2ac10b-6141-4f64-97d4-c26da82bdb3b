/*
 * Created Date: Thursday, 13th March 2025, 08:50:26
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Thursday, 17th April 2025 15:21:22
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

// ignore_for_file: public_member_api_docs
// ignore_for_file: use_if_null_to_convert_nulls_to_bools

import 'package:auto_mappr_annotation/auto_mappr_annotation.dart';

import '../../data/data.dart';
import '../../domain/domain.dart';
import 'portal_entity_mapper.auto_mappr.dart';


@AutoMappr(
  [
    // ---------- List ---------- \
    MapType<PortalListResponse, PortalListEntity>(
      fields: [
        Field(
          'id',
          custom: PortalEntityMapper.mapId,
        ),
      ],
    ),
    // ---------- Mini App ---------- \
    MapType<MiniAppResponse, MiniAppEntity>(
      fields: [
        Field(
          'id',
          custom: PortalEntityMapper.mapId,
        ),
      ],
    ),
    MapType<MiniAppCategoryResponse, MiniAppCategoryEntity>(
      fields: [
        Field(
          'id',
          custom: PortalEntityMapper.mapId,
        ),
      ],
    ),
    MapType<MiniAppMemberResponse, MiniAppMemberEntity>(
      fields: [
        Field(
          'id',
          custom: PortalEntityMapper.mapId,
        ),
      ],
    ),
  ],
  includes: [],
)
class PortalEntityMapper extends $PortalEntityMapper {
  const PortalEntityMapper();

  static dynamic mapId(dynamic input) {
    return input.id;
  }
}
