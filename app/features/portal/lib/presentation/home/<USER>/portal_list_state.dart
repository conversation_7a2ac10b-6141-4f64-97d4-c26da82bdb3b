import 'package:gp_feat_portal/domain/domain.dart';
import 'package:gp_shared/presentation/base/bloc/list/base_list_state.dart';

final class PortalListLoadedState extends BaseListDataLoaded<PortalListEntity> {
  const PortalListLoadedState({
    required super.isInitialLoad,
    required super.canNextPage,
    super.data,
    super.nextLink,
    super.page,
  });
}

final class PortalListLoadingState extends BaseListState{
  const PortalListLoadingState({
    required this.seclectedEntites,
  });
  final List<PortalListEntity> seclectedEntites;
