import 'package:gp_core_v2/gp_core_v2.dart';

abstract class MiniAppEvent extends CoreV2BaseEvent {
  const MiniAppEvent();
}

final class MiniAppLoadCategoriesEvent extends MiniAppEvent {
  const MiniAppLoadCategoriesEvent();
}

final class MiniAppSelectCategoryEvent extends MiniAppEvent {
  const MiniAppSelectCategoryEvent({required this.categoryId});

  final String categoryId;
}

final class MiniAppLoadListEvent extends MiniAppEvent {
  const MiniAppLoadListEvent({
    required this.categoryId,
    this.limit = 10,
  });

  final String categoryId;
  final int limit;
