import 'package:gp_feat_ticket/domain/entity/ticket/ticket_node.entity.dart';

import '../../../../../domain/entity/enums/ticket/ticket_form_field_type.dart';
import 'input_text_behavior.dart';

abstract class BaseInputTextParams {
  BaseInputTextParams({
    required this.id,
    required this.formIndex,
    required this.formFieldType,
    this.isReadOnly = false,
    this.permissions,
  });

  final List<InputTextBehavior> inputTextBehaviors = [];

  final int id;
  final TicketFormFieldType formFieldType;

  /// index của field trong form, phục vụ cho việc scroll đến đúng field khi validate
  int formIndex;

  bool isReadOnly;
  WorkflowFormFieldPermissionEntity? permissions;

  void addOnTextChangedListener(InputTextBehavior inputTextBehavior) {
    inputTextBehaviors.add(inputTextBehavior);
  }

  void removeOnTextChangedListener(InputTextBehavior inputTextBehavior) {
    inputTextBehaviors.remove(inputTextBehavior);
  }

  /// toJson chứa value của từng field
  /// <br>
  /// e.g:
  /// "văn bản""
  dynamic toTicketValueJson();

  /// toJson của 1 field cụ thể
  /// <br>
  /// e.g:
  /// {}
  ///   "id"	  :	"1721443835602""
  ///   "value"	:	"văn bản""
  ///   "type"	:	1"
  /// }
  Map<String, dynamic>? toTicketJson() {
    if (id == noId) return null;

    final fieldValue = toTicketValueJson();

    // bỏ parent_id trong input table
    if (formFieldType == TicketFormFieldType.inputTable) {
      for (var element in fieldValue) {
        for (var e in element) {
          if (e.containsKey('parent_id')) {
            e.remove('parent_id');
          }
        }
      }
    }

    if (fieldValue == null) {
      return null;
    }

    return TicketCreateFormValueData(
      id: id,
      formFieldType: formFieldType,
      value: fieldValue,
    ).toJson();
  }

  static const int noId = -1;
}

final class TicketCreateFormValueData {
  final int id;
  final TicketFormFieldType formFieldType;

  /// String, int, json object or json arrays
  final dynamic value;

  final dynamic unitId;

  TicketCreateFormValueData({
    required this.id,
    required this.formFieldType,
    required this.value,
    this.unitId,
  });

  Map<String, dynamic> toJson() {
    final ret = {
      'id': id,
      'type': formFieldType.type,
      'value': value,
    };

    if (unitId != null) {
      if (unitId is String) {
        int? unitIdToInt = int.tryParse(unitId);
        if (unitIdToInt != null) {
          ret.addAll(
            {
              'unit_id': unitIdToInt,
            },
          );
        }
      }
    }

    return ret;
  }
}

extension InputTextBehaviorCollectionExt on List<InputTextBehavior> {
  void onTextChange(int id, String input) {
    for (var behavior in this) {
      behavior.onTextChange(id, input);
    }
  }
}
