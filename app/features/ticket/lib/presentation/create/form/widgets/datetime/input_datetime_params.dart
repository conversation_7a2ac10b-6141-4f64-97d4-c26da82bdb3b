import 'package:gp_core/core.dart';
import 'package:gp_feat_ticket/lib.dart';
import 'package:gp_shared/widgets/datetime_picker/model/model.dart';

const _amPM = 'am/pm';
const _hhMM = 'hh:mm';
const _ddMMYYYY = 'dd/MM/yyyy';

class TicketCreateInputDateTimeParams extends BaseInputTextParams {
  TicketCreateInputDateTimeParams({
    required super.id,
    required super.formIndex,
    required super.formFieldType,
    required this.title,
    required this.isRequired,
    required this.dateTimeFormat,
    super.permissions,
    super.isReadOnly = true,
    this.error,
    this.timeInMS,
    this.subTitle,
  }) {
    mode = fromDateTimeFormat(dateTimeFormat);

    timeInMS ??= _defaultDateTime()?.millisecondsSinceEpoch;
  }

  final String title;
  final bool isRequired;
  final String dateTimeFormat;
  late final GPDateTimePickerMode mode;
  final String? subTitle;

  int? timeInMS;

  final String? error;

  // ---------- fromWorkFlowFormFieldEntity ---------- \
  factory TicketCreateInputDateTimeParams.fromWorkFlowFormFieldEntity(
    WorkFlowFormFieldEntity entity, {
    required int formIndex,
    bool isReadOnly = false,
    WorkflowFormFieldPermissionEntity? permissions,
  }) {
    return TicketCreateInputDateTimeParams(
      id: entity.id,
      formIndex: formIndex,
      formFieldType: entity.type,
      title: entity.title,
      isRequired: entity.isRequired,
      dateTimeFormat: entity.option.timeFormat,
      isReadOnly: isReadOnly,
      permissions: permissions,
      subTitle: entity.isRequired ? '*' : '',
    );
  }

  factory TicketCreateInputDateTimeParams.fromWorkFlowFormFieldEntityWithTitle({
    required WorkFlowFormFieldEntity entity,
    required String title,
    required int formIndex,
    bool isReadOnly = true,
    String? error,
    WorkflowFormFieldPermissionEntity? permissions,
  }) {
    return TicketCreateInputDateTimeParams(
      id: entity.id,
      formIndex: formIndex,
      formFieldType: entity.type,
      title: title,
      isRequired: entity.isRequired,
      dateTimeFormat: entity.option.timeFormat,
      error: error,
      isReadOnly: isReadOnly,
      permissions: permissions,
      subTitle: entity.isRequired ? '*' : '',
    );
  }

  // ---------- fromWorkFlowFieldValuesEntity ---------- \
  factory TicketCreateInputDateTimeParams.fromWorkFlowFieldValuesEntity(
    WorkFlowFieldValuesEntity entity, {
    required int formIndex,
    bool isReadOnly = false,
    WorkflowFormFieldPermissionEntity? permissions,
  }) {
    final WorkFlowFormFieldEntity formFieldEntity = entity.info;

    int? timeInMs;
    if (entity.value != null) {
      if (entity.value is int) {
        timeInMs = entity.value;
      } else if (entity.value is String) {
        timeInMs = int.tryParse(entity.value);
      }
    }

    return TicketCreateInputDateTimeParams(
      id: formFieldEntity.id,
      formIndex: formIndex,
      formFieldType: formFieldEntity.type,
      title: formFieldEntity.title,
      isRequired: formFieldEntity.isRequired,
      dateTimeFormat: formFieldEntity.option.timeFormat,
      isReadOnly: isReadOnly,
      timeInMS: timeInMs,
      permissions: permissions,
      subTitle: formFieldEntity.isRequired ? '*' : '',
    );
  }

  factory TicketCreateInputDateTimeParams.fromWorkFlowFieldValuesEntityWithTitle({
    required WorkFlowFieldValuesEntity entity,
    required String title,
    required int formIndex,
    int? timeInMs,
    bool isReadOnly = true,
    String? error,
    WorkflowFormFieldPermissionEntity? permissions,
  }) {
    final formFieldEntity = entity.info;

    return TicketCreateInputDateTimeParams(
      id: formFieldEntity.id,
      formIndex: formIndex,
      formFieldType: formFieldEntity.type,
      title: title,
      isRequired: formFieldEntity.isRequired,
      dateTimeFormat: formFieldEntity.option.timeFormat,
      error: error,
      isReadOnly: isReadOnly,
      timeInMS: timeInMs,
      permissions: permissions,
      subTitle: formFieldEntity.isRequired ? '*' : '',
    );
  }

  GPDateTimePickerMode fromDateTimeFormat(String timeFormat) {
    // cẩn thận khi so sánh phần datetime dd/mm/yyyy hh:mm
    final newTimeFormat = timeFormat.toLowerCase();
    if (newTimeFormat.contains(_amPM)) {
      return GPDateTimePickerMode.dateAmPm;
    } else if (newTimeFormat.contains(_hhMM)) {
      return GPDateTimePickerMode.dateTime;
    } else {
      return GPDateTimePickerMode.date;
    }
  }

  String? formatDate() {
    if (timeInMS == null) return null;

    DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(timeInMS!);

    String newDateFormat = dateTimeFormat
        .toLowerCase()
        .replaceAll(_ddMMYYYY.toLowerCase(), _ddMMYYYY)
        .trim();

    if (dateTimeFormat.toLowerCase().contains(_hhMM)) {
      // BE trả về dd/mm/yyyy hh:mm
      newDateFormat = newDateFormat
          .replaceAll(_amPM, '')
          .replaceAll(_ddMMYYYY.toLowerCase(), _ddMMYYYY)
          .trim();

      final String dateTimeStr = DateFormat(newDateFormat).format(dateTime);

      if (dateTime.hour < 12) {
        return '$dateTimeStr ${LocaleKeys.ticket_datetime_am_shorten.tr}';
      } else {
        return '$dateTimeStr ${LocaleKeys.ticket_datetime_pm_shorten.tr}';
      }
    } else if (dateTimeFormat.toLowerCase().contains(_amPM)) {
      // BE trả về dd/mm/yyyy hh:mm
      newDateFormat = newDateFormat
          .toLowerCase()
          .replaceAll(_amPM, '')
          .replaceAll(_ddMMYYYY.toLowerCase(), _ddMMYYYY)
          .trim();

      final String dateTimeStr = DateFormat(newDateFormat).format(dateTime);

      if (dateTime.hour < 12) {
        return '$dateTimeStr ${LocaleKeys.ticket_datetime_am.tr}';
      } else {
        return '$dateTimeStr ${LocaleKeys.ticket_datetime_pm.tr}';
      }
    }

    return DateFormat(newDateFormat).format(dateTime);
  }

  String displayDateFormat() {
    return dateTimeFormat.split(' ').first;
  }

  DateTime? parseMs() {
    if (timeInMS != null) {
      return DateTime.fromMillisecondsSinceEpoch(timeInMS!);
    }

    return _defaultDateTime();
  }

  DateTime? _defaultDateTime() {
    // if (isRequired) {
    //   final now = DateTime.now();

    //   if (mode == GPDateTimePickerMode.date) {
    //     return DateTime(now.year, now.month, now.day, 0, 0);
    //   } else if (mode == GPDateTimePickerMode.dateAmPm) {
    //     return DateTime(now.year, now.month, now.day, 0, 0);
    //   } else if (mode == GPDateTimePickerMode.dateTime) {
    //     return DateTime(now.year, now.month, now.day, now.hour, now.minute);
    //   }
    // }

    return null;
  }

  bool get showTimePick => mode == GPDateTimePickerMode.dateTime;
  bool get showAmPmPick => mode == GPDateTimePickerMode.dateAmPm;

  bool get hasError => error != null && error?.isNotEmpty == true;

  @override
  dynamic toTicketValueJson() {
    if (formFieldType == TicketFormFieldType.inputDateRange) return null;
    return timeInMS;
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is TicketCreateInputDateTimeParams &&
        other.title == title &&
        other.isRequired == isRequired &&
        other.dateTimeFormat == dateTimeFormat &&
        other.mode == mode &&
        other.subTitle == subTitle &&
        other.timeInMS == timeInMS &&
        other.error == error &&
        other.id == id &&
        other.formFieldType == formFieldType &&
        other.formIndex == formIndex;
  }

  @override
  int get hashCode {
    return Object.hashAll([
      title,
      isRequired,
      dateTimeFormat,
      mode,
      subTitle,
      timeInMS,
      error,
      id,
      formFieldType,
      formIndex,
    ]);
  }
}
