import 'package:gp_shared/presentation/base/bloc/list/base_list_state.dart';
import 'package:gp_feat_ticket/domain/entity/ticket/ticket_activity.entity.dart';


class TicketActivityListLoadedState extends BaseListDataLoaded<TicketActivityEntity> {
  const TicketActivityListLoadedState({
    required super.isInitialLoad,
    required super.data,
    required super.canNextPage,
    super.page,
    super.nextLink,
  });
