import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/shared_features/comment/input/controller.dart';
import 'package:gp_core/shared_features/comment/items/comment_item.dart';
import 'package:gp_core/shared_features/comment/reply_comment/controller/reply_comment_controller.dart';
import 'package:gp_feat_ticket/domain/entity/entity.dart';

class CommentWrapper extends StatelessWidget {
  const CommentWrapper(
      {super.key,
      required this.scrollController,
      required this.inputCommentController,
      required this.index,
      required this.comments,
      required this.onReplyCommentCallback});

  final AutoScrollController scrollController;
  final InputCommentController inputCommentController;
  final List<TicketCommentEntity> comments;
  final OnReplyCommentCallback onReplyCommentCallback;

  final int index;

  @override
  Widget build(BuildContext context) {
    if (comments.isNotEmpty) {
      return ColoredBox(
        color: Colors.white,
        child: _AutoScrollWrapperWidget(
          scrollController: scrollController,
          index: index,
          child: CommentItem(
            avatarSize: 40,
            comment: comments[index],
            id: comments[index].id,
            controller: inputCommentController,
            onReplyCommentCallback: onReplyCommentCallback,
          ),
        ).paddingSymmetric(horizontal: 16).paddingOnly(bottom: 8),
      );
    } else {
      return const SizedBox();
    }
  }
}

class _AutoScrollWrapperWidget extends StatelessWidget {
  const _AutoScrollWrapperWidget({
    required this.scrollController,
    required this.child,
    required this.index,
  });

  final AutoScrollController scrollController;
  final Widget child;
  final int index;

  @override
  Widget build(BuildContext context) {
    return AutoScrollTag(
      key: ValueKey(index),
      controller: scrollController,
      index: index,
      child: child,
    );
  }
