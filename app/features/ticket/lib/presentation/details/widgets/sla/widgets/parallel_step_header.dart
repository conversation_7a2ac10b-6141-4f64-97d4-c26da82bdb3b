import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';

class ParallelStepHeader extends StatelessWidget {
  const ParallelStepHeader(
      {super.key, required this.rxExpandData, required this.onToggleExpand});

  final ValueNotifier<bool> rxExpandData;
  final Function() onToggleExpand;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          SvgWidget(
            Assets.PACKAGES_GP_ASSETS_IMAGES_SVG_IC16_FILL_FOLDER_TREE_SVG,
            color: GPColor.functionAccentWorkSecondary,
          ),
          const SizedBox(width: 12),
          Expanded(
              child: Text(
            LocaleKeys.ticket_details_sla_parallel.tr,
            style: textStyle(GPTypography.bodyLarge),
          )),
          InkWell(
            onTap: onToggleExpand,
            child: ValueListenableBuilder(
              valueListenable: rxExpandData,
              builder: (context, value, child) {
                return SvgWidget(
                  value
                      ? Assets
                          .PACKAGES_GP_ASSETS_IMAGES_SVG_IC16_FILL_CHEVRON_DOWN_SVG
                      : Assets
                          .PACKAGES_GP_ASSETS_IMAGES_SVG_IC16_FILL_CHEVRON_UP_SVG,
                  color: GPColor.contentSecondary,
                );
              },
            ),
          ),
        ],
      ),
    );
  }
