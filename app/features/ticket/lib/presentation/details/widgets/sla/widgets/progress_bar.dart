import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';

class ProgressBar extends StatelessWidget {
  const ProgressBar({super.key, required this.color, required this.value});

  final Color color;
  final double value;

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: SizedBox(
        height: 6,
        child: LinearProgressIndicator(
          value: value,
          valueColor: AlwaysStoppedAnimation<Color>(color),
          backgroundColor: GPColor.bgTertiary,
        ),
      ),
    );
  }
