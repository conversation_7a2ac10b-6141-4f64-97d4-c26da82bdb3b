import 'package:diffutil_dart/diffutil.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_ticket/domain/entity/enums/ticket/ticket.dart';
import 'package:gp_feat_ticket/domain/entity/ticket/ticket_node.entity.dart';

import '../../../data/model/request/ticket/details/edit/ticket_update_field_values.params.dart';
import '../../../domain/entity/ticket/ticket_list.entity.dart';
import '../../../domain/entity/workflow/workflow_form.entity.dart';
import '../../create/widgets/input/input_behavior.dart';

/// danh sách các id đã được xóa
final ticketEditDeletedIds = [];

mixin TicketDataMixin {
  /// So sánh difference giữa 2 list data
  /// Trả về list object thay đổi
  List<TicketUpdateFieldValuesDataParams> getUpdateData({
    required InputBehavior inputBehavior,
    required TicketEntity ticketEntity,
    List<WorkflowFormFieldPermissionEntity>? permissions,
  }) {
    // ---------- so sánh, trả về data user update ---------- \
    final output = <TicketUpdateFieldValuesDataParams>[];
    // final exceptionUpdateData = <TicketUpdateFieldValuesDataParams>[];

    final toJsonData = inputBehavior.toJson() ?? [];
    final editData = toJsonData.whereType<Map>().toList();

    final oldData = ticketEntity.toValuesJson().toList();

    Map<int, WorkflowFormFieldPermissionEntity> mapPermissions = {};
    Map<int, Map<String, dynamic>> mapOldData = {};
    if (permissions != null) {
      for (var element in permissions) {
        mapPermissions[element.workflowFormFieldId ?? 0] = element;
      }
    }
    for (var element in oldData) {
      mapOldData[element['id']] = element;
    }

    logDebug('oldData  -> $oldData');
    logDebug('editData -> $editData');

    final diffResult = calculateListDiff<Map>(
      oldData,
      editData,
      detectMoves: true,
      equalityChecker: _compareFieldValues,
    );

    final updateResults = diffResult.getUpdatesWithData();

    if (updateResults.isEmpty) {
      return [];
    }

    for (final updateResult in updateResults) {
      updateResult.when(
        insert: (pos, data) {
          // data user đã thay đổi, bao gồm insert + update trong case này
          final inputPermissionHasData = _checkInputFieldHasData(
            mapOldData: mapOldData,
            mapPermissions: mapPermissions,
            id: data['id'],
          );
          if (!inputPermissionHasData) {
            output.add(
              TicketUpdateFieldValuesDataParams(
                id: data['id'],
                value: data['value'],
                unitId: data.containsKey('unit_id') ? data['unit_id'] : null,
              ),
            );
          }
        },
        remove: (pos, data) {
          // doNothing
          logDebug('remove -> $pos, $data');
        },
        change: (pos, p1, p2) {
          // doNothing
          logDebug('change -> $pos, $p1, $p2');
        },
        move: (from, to, data) {
          // doNothing
          logDebug('move -> $from, $to, $data');
        },
      );
    }

    // remove null
    for (var element in output) {
      if (element.value is List) {
        final valueData = element.value as List;
        valueData.removeWhere((e) => e == null);
      }
    }

    return output;
  }

  /// check field quyền input đã có dữ liệu chưa
  bool _checkInputFieldHasData({
    required Map<int, Map<String, dynamic>> mapOldData,
    required Map<int, WorkflowFormFieldPermissionEntity> mapPermissions,
    required int id,
  }) {
    if (!mapOldData.containsKey(id)) {
      return false;
    }

    final oldData = mapOldData[id];

    if (oldData == null) {
      return false;
    }

    if (oldData['type'] == TicketFormFieldType.inputFormula.type) {
      return false;
    }

    final permissionId = mapOldData[id]!['parent_id'] == 0
        ? id
        : mapOldData[id]!['parent_id'] ?? id;
    if (!mapPermissions.containsKey(permissionId)) {
      return false;
    }

    final permission = mapPermissions[permissionId]?.isViewOrInput;
    if (permission != true) {
      return false;
    }

    final value = oldData['value'];

    if (oldData['type'] == TicketFormFieldType.inputTable.type) {
      for (var row in value) {
        for (var element in row) {
          if (element['value'] != null && element['value'] != '') {
            return true;
          }
        }
      }
      return false;
    }

    if (value is String) {
      return value.isNotEmpty;
    }

    if (value is List || value is Map) {
      return value.isNotEmpty;
    }

    if (value != null) {
      return true;
    }

    return false;
  }

  bool _compareFieldValues(Map o1, Map o2) {
    if (o1['id'] != o2['id']) return false;

    final type1 = o1['type'];
    final type2 = o2['type'];

    if (_isNumericField(type1, type2)) {
      return _compareNumericValues(o1['value'], o2['value']);
    }

    if (_isMediaField(type1, type2)) {
      return _compareMediaValues(o1['value'], o2['value']);
    }

    // if (_isTableField(type1, type2)) {
    //   return _compareTableValues(o1['value'], o2['value']);
    // }

    return const DeepCollectionEquality().equals(o1['value'], o2['value']);
  }

  bool _isTableField(int type1, int type2) {
    return (type1 == TicketFormFieldType.inputTable.type &&
        type2 == TicketFormFieldType.inputTable.type);
  }

  bool _isNumericField(int type1, int type2) {
    return (type1 == TicketFormFieldType.inputNumber.type &&
            type2 == TicketFormFieldType.inputNumber.type) ||
        (type1 == TicketFormFieldType.inputCurrency.type &&
            type2 == TicketFormFieldType.inputCurrency.type);
  }

  bool _isMediaField(int type1, int type2) {
    return (type1 == TicketFormFieldType.inputPickAttachments.type &&
            type2 == TicketFormFieldType.inputPickAttachments.type) ||
        (type1 == TicketFormFieldType.inputPickMedias.type &&
            type2 == TicketFormFieldType.inputPickMedias.type);
  }

  bool _compareNumericValues(String value1, String value2) {
    final num1 =
        value1.isEmpty ? 0.0 : double.parse(value1.replaceAll(',', ''));
    final num2 =
        value2.isEmpty ? 0.0 : double.parse(value2.replaceAll(',', ''));
    return num1.toStringNoTrailingZeros() == num2.toStringNoTrailingZeros();
  }

  bool _compareMediaValues(dynamic list1, dynamic list2) {
    final val1 = list1 == null ? [] : list1 as List;
    final val2 = list2 == null ? [] : list2 as List;
    if (val1.length != val2.length) return false;

    for (var i = 0; i < val1.length; i++) {
      if (val1[i]['id'] != val2[i]['id']) return false;
    }

    return true;
  }

  bool _compareTableValues(dynamic data1, dynamic data2) {
    final List? val1 = data1 == null ? null : (data1 as List).first;
    final List? val2 = data2 == null ? null : (data2 as List).first;

    if (val1 == null || val2 == null) return false;

    for (var i = 0; i < val1.length; i++) {
      final isEqual = _compareFieldValues(val1[i], val2[i]);
      if (!isEqual) return false;
    }

    return true;
  }

  /// cập nhật dữ liệu user đã nhập vào `TicketEntity`
  TicketEntity updateTicketEntity({
    required InputBehavior inputBehavior,
    required TicketEntity ticketEntity,
  }) {
    // ---------- lấy dữ liệu user đã thay đổi ---------- \
    final List<TicketUpdateFieldValuesDataParams> updateData = getUpdateData(
      inputBehavior: inputBehavior,
      ticketEntity: ticketEntity,
    );

    if (updateData.isEmpty) return ticketEntity;

    // ---------- cập nhật dữ liệu ---------- \
    final fieldValues =
        ticketEntity.fieldValues ?? <WorkFlowFieldValuesEntity>[];
    for (var element in fieldValues) {
      for (var updateElement in updateData) {
        if (element.fieldId == updateElement.id) {
          element.value = updateElement.value;
        }
      }
    }

    return ticketEntity;
  }
}
