/*
 * Created Date: Monday, 18th November 2024
 * Author: Thang Le
 * -----
 * Last Modified: Monday, 18th November 2024
 * Modified By: Thang Le
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

// ignore_for_file: public_member_api_docs

import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:injectable/injectable.dart';

import '../../../data/model/response/ticket/ticket_activity_response.dart';
import '../../domain.dart';

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class TicketGetActivitiesUseCase extends GPBaseFutureUseCase<
    TicketGetActivitiesInput,
    ListAPIResponseV2<TicketActivityResponse>> {
  TicketGetActivitiesUseCase(
    @Named('kTicketRepository') this._ticketRepository,
  );

  final TicketRepository _ticketRepository;

  @override
  Future<ListAPIResponseV2<TicketActivityResponse>> buildUseCase(
    TicketGetActivitiesInput input,
  ) async {
    return await _ticketRepository.getActivities(
      ticketId: input.ticketId,
    );
  }
}

class TicketGetActivitiesInput extends GPBaseInput {
  const TicketGetActivitiesInput({
    required this.ticketId,
  });

  final String ticketId;
