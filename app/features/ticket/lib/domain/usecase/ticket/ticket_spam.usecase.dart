/*
 * Created Date: Wednesday, 30th October 2024, 11:19:10
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Wednesday, 30th October 2024 11:21:28
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

// ignore_for_file: public_member_api_docs

import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:injectable/injectable.dart';

import '../../../data/data.dart';
import '../../domain.dart';

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class TicketSpamUseCase extends GPBaseFutureUseCase<
    TicketSpamInput, ApiResponseV2<dynamic>> {
  TicketSpamUseCase(
    @Named('kTicketRepository') this._ticketRepository,
  );

  final TicketRepository _ticketRepository;

  @override
  Future<ApiResponseV2<dynamic>> buildUseCase(
    TicketSpamInput input,
  ) async {
    return _ticketRepository.spam(
      ticketId: input.ticketId,
      nodeId: input.nodeId,
      params: input.params,
    );
  }
}

class TicketSpamInput extends GPBaseInput {
  const TicketSpamInput({
    required this.ticketId,
    required this.nodeId,
    required this.params,
  });

  final String ticketId;
  final String nodeId;

  final TicketSpamParams params;
