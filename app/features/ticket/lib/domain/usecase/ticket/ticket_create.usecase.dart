/*
 * Created Date: Wednesday, 2nd October 2024, 15:54:35
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Wednesday, 2nd October 2024 16:00:01
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

// ignore_for_file: public_member_api_docs

import 'package:gp_core_v2/gp_core_v2.dart';
import 'package:injectable/injectable.dart';

import '../../../data/data.dart';
import '../../domain.dart';

@Injectable(order: DiConstants.kDomainUseCaseOrder)
class TicketCreateUseCase extends GPBaseFutureUseCase<TicketCreateInput,
    ApiResponseV2<TicketListResponse>> {
  TicketCreateUseCase(
    @Named('kTicketRepository') this._ticketRepository,
  );

  final TicketRepository _ticketRepository;

  @override
  Future<ApiResponseV2<TicketListResponse>> buildUseCase(
    TicketCreateInput input,
  ) async {
    return _ticketRepository.createTickets(input.params);
  }
}

class TicketCreateInput extends GPBaseInput {
  const TicketCreateInput({
    required this.params,
  });

  final TicketCreateParams params;
