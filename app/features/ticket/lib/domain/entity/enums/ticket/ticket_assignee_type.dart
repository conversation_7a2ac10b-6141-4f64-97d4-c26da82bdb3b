import 'dart:ui';

import 'package:gp_core/core.dart';
import 'package:json_annotation/json_annotation.dart';

enum TicketAssigneeType {
  @JsonValue(1)
  user,
  @JsonValue(2)
  collab,
  @JsonValue(3)
  department,
  @JsonValue(4)
  role,
}

enum TicketNodeAssigneeType {
  @JsonValue(0)
  /// giống specific
  specificOld,
  @JsonValue(1)
  creatorLeader,
  @JsonValue(2)
  specific,
  @JsonValue(3)
  all,
  @JsonValue(4)
  creatorOfTicket,
}

extension TicketAssigneeTypeExtension on TicketAssigneeType {
  Color get bgColor {
    switch (this) {
      case TicketAssigneeType.user:
        return GPColor.bgTertiary;
      case TicketAssigneeType.collab:
        return GPColor.purpleLight;
      case TicketAssigneeType.department:
        return GPColor.blueLighter;
      case TicketAssigneeType.role:
        return GPColor.orangeLighter;
    }
  }

  Color get textColor {
    switch (this) {
      case TicketAssigneeType.user:
        return GPColor.contentPrimary;
      case TicketAssigneeType.collab:
        return GPColor.purpleDark;
      case TicketAssigneeType.department:
        return GPColor.blueDark;
      case TicketAssigneeType.role:
        return GPColor.orangeDark;
    }
  }
