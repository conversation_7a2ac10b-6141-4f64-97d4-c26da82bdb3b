final nodeDataTest = {
    "data": {
        "id": 1729140026020,
        "workspace_id": "523866125265220",
        "ticket_id": 1729147439110,
        "node_id": 1729140026020,
        "assignee_id": 0,
        "status": 2,
        "type": 2,
        "updated_by": 0,
        "actual_processing_duration_sec": 0,
        "actual_first_response_after_sec": 0,
        "absolute_seconds_until_deadline": 0,
        "absolute_seconds_until_response_deadline": 0,
        "response_deadline_ts": 0,
        "started_at": 1729147439252,
        "processed_at": 0,
        "response_at": 0,
        "additional_request_at": 0,
        "on_hold_request_at": 0,
        "additional_request_in": 0,
        "on_hold_request_in": 0,
        "date_off_map": {},
        "sla": null,
        "option": {
            "assignee_type": 0,
            "assigning_method_type": 1,
            "reserve_assignee_type": 1,
            "must_done_all_prev_nodes": false,
            "name": "S1",
            "clauses": null,
            "workflow_form_field_permissions": [
                {
                    "workflow_form_field_id": 1729140025846,
                    "permission": 7,
                    "permission_path": "111"
                }
            ]
        },
        "deadline_ts": 0,
        "created_at": 1729147439156,
        "updated_at": 1729147439255,
        "admins": [
            {
                "created_at": 1729147454149,
                "updated_at": 1729147454149,
                "type": 1,
                "id": "*********",
                "name": "Nguyễn Mạnh Toàn",
                "workspace_id": "523866125265220",
                "info": {
                    "id": "*********",
                    "lang": "",
                    "display_name": "Nguyễn Mạnh Toàn",
                    "avatar": "https://image-1.gapowork.vn/images/70cb3793-b281-469e-a788-ece3cb28f0a0.jpeg",
                    "avatar_thumb_pattern": "",
                    "email": "<EMAIL>",
                    "phone_number": "84985942382",
                    "title": "Technical Leader",
                    "department": "Technical Leader",
                    "company_name": "GAPO",
                    "employee_code": "17000372",
                    "list_departments": [
                        {
                            "tree_id": "523866125265220",
                            "tree_name": "GAPO",
                            "department_ids": [
                                "d6081c57e0184cc39a1b5546503a56e4",
                                "2afc6d3cfdd34116a3914413c82bd5fd",
                                "7dbd93a219f64e728d4ecc296fd4bcf1",
                                "6326f64aa8c94f9f93168388ee14db1c"
                            ],
                            "departments": [
                                "Product Development",
                                "Tech",
                                "Flutter",
                                "Technical Leader"
                            ],
                            "department_id": "6326f64aa8c94f9f93168388ee14db1c",
                            "department": "Technical Leader",
                            "role_id": "32eab162290146d0a0ed8759beb646b9",
                            "title": "Technical Leader"
                        },
                        {
                            "tree_id": "523866125265220",
                            "tree_name": "GAPO",
                            "department_ids": [
                                "56ffc80746c446ab88323eb476e326eb"
                            ],
                            "departments": [
                                "phong ban canlendar"
                            ],
                            "department_id": "56ffc80746c446ab88323eb476e326eb",
                            "department": "phong ban canlendar",
                            "role_id": "37c91335d10e4443a4531c0ad8556b08",
                            "title": "TEST CHUC VU"
                        }
                    ],
                    "status": 1,
                    "login_type": "",
                    "region_id": "",
                    "region": ""
                }
            }
        ],
        "supporters": [
            {
                "created_at": 1729147454149,
                "updated_at": 1729147454149,
                "type": 1,
                "id": "*********",
                "name": "Nguyễn Mạnh Toàn",
                "workspace_id": "523866125265220",
                "info": {
                    "id": "*********",
                    "lang": "",
                    "display_name": "Nguyễn Mạnh Toàn",
                    "avatar": "https://image-1.gapowork.vn/images/70cb3793-b281-469e-a788-ece3cb28f0a0.jpeg",
                    "avatar_thumb_pattern": "",
                    "email": "<EMAIL>",
                    "phone_number": "84985942382",
                    "title": "Technical Leader",
                    "department": "Technical Leader",
                    "company_name": "GAPO",
                    "employee_code": "17000372",
                    "list_departments": [
                        {
                            "tree_id": "523866125265220",
                            "tree_name": "GAPO",
                            "department_ids": [
                                "d6081c57e0184cc39a1b5546503a56e4",
                                "2afc6d3cfdd34116a3914413c82bd5fd",
                                "7dbd93a219f64e728d4ecc296fd4bcf1",
                                "6326f64aa8c94f9f93168388ee14db1c"
                            ],
                            "departments": [
                                "Product Development",
                                "Tech",
                                "Flutter",
                                "Technical Leader"
                            ],
                            "department_id": "6326f64aa8c94f9f93168388ee14db1c",
                            "department": "Technical Leader",
                            "role_id": "32eab162290146d0a0ed8759beb646b9",
                            "title": "Technical Leader"
                        },
                        {
                            "tree_id": "523866125265220",
                            "tree_name": "GAPO",
                            "department_ids": [
                                "56ffc80746c446ab88323eb476e326eb"
                            ],
                            "departments": [
                                "phong ban canlendar"
                            ],
                            "department_id": "56ffc80746c446ab88323eb476e326eb",
                            "department": "phong ban canlendar",
                            "role_id": "37c91335d10e4443a4531c0ad8556b08",
                            "title": "TEST CHUC VU"
                        }
                    ],
                    "status": 1,
                    "login_type": "",
                    "region_id": "",
                    "region": ""
                }
            }
        ],
        "default_followers": [
            {
                "created_at": 1729147454149,
                "updated_at": 1729147454149,
                "type": 1,
                "id": "*********",
                "name": "Nguyễn Mạnh Toàn",
                "workspace_id": "523866125265220",
                "info": {
                    "id": "*********",
                    "lang": "",
                    "display_name": "Nguyễn Mạnh Toàn",
                    "avatar": "https://image-1.gapowork.vn/images/70cb3793-b281-469e-a788-ece3cb28f0a0.jpeg",
                    "avatar_thumb_pattern": "",
                    "email": "<EMAIL>",
                    "phone_number": "84985942382",
                    "title": "Technical Leader",
                    "department": "Technical Leader",
                    "company_name": "GAPO",
                    "employee_code": "17000372",
                    "list_departments": [
                        {
                            "tree_id": "523866125265220",
                            "tree_name": "GAPO",
                            "department_ids": [
                                "d6081c57e0184cc39a1b5546503a56e4",
                                "2afc6d3cfdd34116a3914413c82bd5fd",
                                "7dbd93a219f64e728d4ecc296fd4bcf1",
                                "6326f64aa8c94f9f93168388ee14db1c"
                            ],
                            "departments": [
                                "Product Development",
                                "Tech",
                                "Flutter",
                                "Technical Leader"
                            ],
                            "department_id": "6326f64aa8c94f9f93168388ee14db1c",
                            "department": "Technical Leader",
                            "role_id": "32eab162290146d0a0ed8759beb646b9",
                            "title": "Technical Leader"
                        },
                        {
                            "tree_id": "523866125265220",
                            "tree_name": "GAPO",
                            "department_ids": [
                                "56ffc80746c446ab88323eb476e326eb"
                            ],
                            "departments": [
                                "phong ban canlendar"
                            ],
                            "department_id": "56ffc80746c446ab88323eb476e326eb",
                            "department": "phong ban canlendar",
                            "role_id": "37c91335d10e4443a4531c0ad8556b08",
                            "title": "TEST CHUC VU"
                        }
                    ],
                    "status": 1,
                    "login_type": "",
                    "region_id": "",
                    "region": ""
                }
            }
        ],
        "assignees": [
            {
                "created_at": 1729147454149,
                "updated_at": 1729147454149,
                "type": 1,
                "id": "*********",
                "name": "Nguyễn Mạnh Toàn",
                "workspace_id": "523866125265220",
                "info": {
                    "id": "*********",
                    "lang": "",
                    "display_name": "Nguyễn Mạnh Toàn",
                    "avatar": "https://image-1.gapowork.vn/images/70cb3793-b281-469e-a788-ece3cb28f0a0.jpeg",
                    "avatar_thumb_pattern": "",
                    "email": "<EMAIL>",
                    "phone_number": "84985942382",
                    "title": "Technical Leader",
                    "department": "Technical Leader",
                    "company_name": "GAPO",
                    "employee_code": "17000372",
                    "list_departments": [
                        {
                            "tree_id": "523866125265220",
                            "tree_name": "GAPO",
                            "department_ids": [
                                "d6081c57e0184cc39a1b5546503a56e4",
                                "2afc6d3cfdd34116a3914413c82bd5fd",
                                "7dbd93a219f64e728d4ecc296fd4bcf1",
                                "6326f64aa8c94f9f93168388ee14db1c"
                            ],
                            "departments": [
                                "Product Development",
                                "Tech",
                                "Flutter",
                                "Technical Leader"
                            ],
                            "department_id": "6326f64aa8c94f9f93168388ee14db1c",
                            "department": "Technical Leader",
                            "role_id": "32eab162290146d0a0ed8759beb646b9",
                            "title": "Technical Leader"
                        },
                        {
                            "tree_id": "523866125265220",
                            "tree_name": "GAPO",
                            "department_ids": [
                                "56ffc80746c446ab88323eb476e326eb"
                            ],
                            "departments": [
                                "phong ban canlendar"
                            ],
                            "department_id": "56ffc80746c446ab88323eb476e326eb",
                            "department": "phong ban canlendar",
                            "role_id": "37c91335d10e4443a4531c0ad8556b08",
                            "title": "TEST CHUC VU"
                        }
                    ],
                    "status": 1,
                    "login_type": "",
                    "region_id": "",
                    "region": ""
                }
            },
            {
                "created_at": 1729147454149,
                "updated_at": 1729147454149,
                "type": 1,
                "id": "*********",
                "name": "Huyền Gapo Test",
                "workspace_id": "523866125265220",
                "info": {
                    "id": "*********",
                    "lang": "",
                    "display_name": "Huyền Gapo Test",
                    "avatar": "https://image-3.gapowork.vn/images/1ee513f0-5881-4524-9e08-6fbd7c696cf4/blob.jpeg",
                    "avatar_thumb_pattern": "",
                    "email": "<EMAIL>",
                    "title": "TEST CHUC VU",
                    "department": "1 phòng ban mới",
                    "company_name": "GAPO",
                    "list_departments": [
                        {
                            "tree_id": "523866125265220",
                            "tree_name": "GAPO",
                            "department_ids": [
                                "803de57995b9432eb5f910e725c4d2cf"
                            ],
                            "departments": [
                                "1 phòng ban mới"
                            ],
                            "department_id": "803de57995b9432eb5f910e725c4d2cf",
                            "department": "1 phòng ban mới",
                            "role_id": "37c91335d10e4443a4531c0ad8556b08",
                            "title": "TEST CHUC VU"
                        }
                    ],
                    "status": 1,
                    "login_type": "",
                    "region_id": "",
                    "region": ""
                }
            }
        ],
        "assignee_group": [
            {
                "created_at": 1729147454149,
                "updated_at": 1729147454149,
                "type": 1,
                "id": "*********",
                "name": "Nguyễn Mạnh Toàn",
                "workspace_id": "523866125265220",
                "info": {
                    "id": "*********",
                    "lang": "",
                    "display_name": "Nguyễn Mạnh Toàn",
                    "avatar": "https://image-1.gapowork.vn/images/70cb3793-b281-469e-a788-ece3cb28f0a0.jpeg",
                    "avatar_thumb_pattern": "",
                    "email": "<EMAIL>",
                    "phone_number": "84985942382",
                    "title": "Technical Leader",
                    "department": "Technical Leader",
                    "company_name": "GAPO",
                    "employee_code": "17000372",
                    "list_departments": [
                        {
                            "tree_id": "523866125265220",
                            "tree_name": "GAPO",
                            "department_ids": [
                                "d6081c57e0184cc39a1b5546503a56e4",
                                "2afc6d3cfdd34116a3914413c82bd5fd",
                                "7dbd93a219f64e728d4ecc296fd4bcf1",
                                "6326f64aa8c94f9f93168388ee14db1c"
                            ],
                            "departments": [
                                "Product Development",
                                "Tech",
                                "Flutter",
                                "Technical Leader"
                            ],
                            "department_id": "6326f64aa8c94f9f93168388ee14db1c",
                            "department": "Technical Leader",
                            "role_id": "32eab162290146d0a0ed8759beb646b9",
                            "title": "Technical Leader"
                        },
                        {
                            "tree_id": "523866125265220",
                            "tree_name": "GAPO",
                            "department_ids": [
                                "56ffc80746c446ab88323eb476e326eb"
                            ],
                            "departments": [
                                "phong ban canlendar"
                            ],
                            "department_id": "56ffc80746c446ab88323eb476e326eb",
                            "department": "phong ban canlendar",
                            "role_id": "37c91335d10e4443a4531c0ad8556b08",
                            "title": "TEST CHUC VU"
                        }
                    ],
                    "status": 1,
                    "login_type": "",
                    "region_id": "",
                    "region": ""
                }
            }
        ],
        "tasks": null,
        "assignee": null,
        "ticket_tags": []
    }
