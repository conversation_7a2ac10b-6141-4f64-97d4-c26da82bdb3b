final ticketDetailsTest = {
    "data": {
        "id": 1729147439110,
        "workspace_id": "523866125265220",
        "workflow_id": 1729140025840,
        "code": "TEST_M2CXQN0M",
        "title": "ToanNM test",
        "status": 1,
        "priority": 1,
        "is_private": false,
        "ref_ticket_ids": [],
        "sla": null,
        "workflow": {
            "workspace_id": "523866125265220",
            "workflow_group_id": 952442918,
            "id": 1729140025840,
            "name": "ToanNM test luồng quy trình",
            "color_src": "#1A99F4",
            "icon_src": "CALENDAR",
            "prefix_id": 1717149250950,
            "state": 5,
            "is_published": true,
            "is_private": false,
            "created_by": *********,
            "updated_by": *********,
            "created_at": 1729140025840,
            "updated_at": 1729140025840,
            "workflow_group_name": "ToanNM",
            "form": {
                "workflow_id": 1729140025840,
                "workspace_id": "",
                "fields": [
                    {
                        "workflow_id": 1729140025840,
                        "id": 1729140025846,
                        "parent_id": 0,
                        "client_id": 0,
                        "created_at": 1729140025846,
                        "updated_at": 1729140025846,
                        "type": 1,
                        "version": 0,
                        "is_required": true,
                        "workspace_id": "523866125265220",
                        "title": "Tiêu đề",
                        "hint": "Nhập thông tin",
                        "option": {
                            "is_thousand_separator": false,
                            "keep_decimal_places": 0,
                            "second_title": "",
                            "third_title": "",
                            "unit": "",
                            "time_format": "",
                            "formula": "",
                            "content": "",
                            "formula_args": null,
                            "currency_pool": null,
                            "selection_pool": null,
                            "selections": []
                        }
                    }
                ]
            },
            "advance_setting": {
                "created_by": *********,
                "updated_by": *********,
                "created_at": 1729140026393,
                "updated_at": 1729140026393,
                "workflow_id": 1729140025840,
                "version": 0,
                "is_published": false,
                "workspace_id": "523866125265220",
                "admin_option": {
                    "admin_workflow": false,
                    "admin_current_step": false,
                    "assignee_step": false,
                    "requester": false,
                    "Ref": 5269167518871571069
                },
                "builder_workflow": {
                    "admin_workflow": false,
                    "admin_current_step": false,
                    "assignee_step": false,
                    "requester": false,
                    "Ref": 5269167518871626288,
                    "participants": [
                        {
                            "created_at": 1729140026407,
                            "updated_at": 1729140026407,
                            "type": 1,
                            "id": "5269167518871626288",
                            "workspace_id": "523866125265220",
                            "members": [
                                {
                                    "workspace_id": "523866125265220",
                                    "participant_id": "5269167518871626288",
                                    "user_id": *********,
                                    "created_at": 1729140026407,
                                    "updated_at": 1729140026407,
                                    "info": {
                                        "id": "*********",
                                        "lang": "",
                                        "display_name": "Nguyễn Mạnh Toàn",
                                        "avatar": "https://image-1.gapowork.vn/images/70cb3793-b281-469e-a788-ece3cb28f0a0.jpeg",
                                        "avatar_thumb_pattern": "",
                                        "email": "<EMAIL>",
                                        "phone_number": "84985942382",
                                        "title": "Technical Leader",
                                        "department": "Technical Leader",
                                        "company_name": "GAPO",
                                        "employee_code": "17000372",
                                        "list_departments": [
                                            {
                                                "tree_id": "523866125265220",
                                                "tree_name": "GAPO",
                                                "department_ids": [
                                                    "d6081c57e0184cc39a1b5546503a56e4",
                                                    "2afc6d3cfdd34116a3914413c82bd5fd",
                                                    "7dbd93a219f64e728d4ecc296fd4bcf1",
                                                    "6326f64aa8c94f9f93168388ee14db1c"
                                                ],
                                                "departments": [
                                                    "Product Development",
                                                    "Tech",
                                                    "Flutter",
                                                    "Technical Leader"
                                                ],
                                                "department_id": "6326f64aa8c94f9f93168388ee14db1c",
                                                "department": "Technical Leader",
                                                "role_id": "32eab162290146d0a0ed8759beb646b9",
                                                "title": "Technical Leader"
                                            },
                                            {
                                                "tree_id": "523866125265220",
                                                "tree_name": "GAPO",
                                                "department_ids": [
                                                    "56ffc80746c446ab88323eb476e326eb"
                                                ],
                                                "departments": [
                                                    "phong ban canlendar"
                                                ],
                                                "department_id": "56ffc80746c446ab88323eb476e326eb",
                                                "department": "phong ban canlendar",
                                                "role_id": "37c91335d10e4443a4531c0ad8556b08",
                                                "title": "TEST CHUC VU"
                                            }
                                        ],
                                        "status": 1,
                                        "login_type": "",
                                        "region_id": "",
                                        "region": ""
                                    }
                                }
                            ]
                        }
                    ]
                },
                "allow_cancel": {
                    "is_allowed": false,
                    "option": {
                        "when_open": false,
                        "when_processing": false,
                        "when_done_or_closed": false
                    },
                    "participant_applied": {
                        "admin_workflow": false,
                        "admin_current_step": false,
                        "assignee_step": false,
                        "requester": false,
                        "Ref": 5269167518871603603
                    }
                },
                "allow_delete": {
                    "is_allowed": false,
                    "option": {
                        "when_open": false,
                        "when_processing": false,
                        "when_done_or_closed": false,
                        "when_canceled": false
                    },
                    "participant_applied": {
                        "admin_workflow": false,
                        "admin_current_step": false,
                        "assignee_step": false,
                        "requester": false,
                        "Ref": 5269167518871505055
                    }
                },
                "close_option": {
                    "auto_close_time": 0,
                    "auto_close_type": "minutes"
                },
                "rate_option": {
                    "is_allowed": false,
                    "auto_close_time": 0,
                    "auto_close_type": "minutes"
                },
                "approve_before_on_hold_option": {
                    "is_allowed": false,
                    "participant_applied": {
                        "admin_workflow": false,
                        "admin_current_step": false,
                        "assignee_step": false,
                        "requester": false,
                        "Ref": 5269167518871646869
                    }
                },
                "allow_editing_after_resolving": 0
            }
        },
        "deadline_ts": 0,
        "first_response_at": 0,
        "created_by": *********,
        "updated_by": 0,
        "started_at": 1729147439110,
        "created_at": 1729147439110,
        "updated_at": 1729147439258,
        "user_role": {
            "user_who_can_cancel_ticket": false,
            "user_who_can_delete_ticket": false,
            "user_who_can_accept_decline_on_hold_request": false,
            "requester": true,
            "same_department_with_requester": true,
            "user_role_in_node": {
                "1729140026019": {
                    "assignee_group": false,
                    "assignee": false,
                    "admin": false,
                    "supporter": false,
                    "follower": false,
                    "same_department_with_assignee": false,
                    "user_who_can_cancel_ticket": false,
                    "user_who_can_delete_ticket": false
                },
                "1729140026020": {
                    "assignee_group": true,
                    "assignee": true,
                    "admin": true,
                    "supporter": true,
                    "follower": true,
                    "same_department_with_assignee": false,
                    "user_who_can_cancel_ticket": false,
                    "user_who_can_delete_ticket": false
                },
                "1729140026021": {
                    "assignee_group": false,
                    "assignee": true,
                    "admin": true,
                    "supporter": true,
                    "follower": true,
                    "same_department_with_assignee": false,
                    "user_who_can_cancel_ticket": false,
                    "user_who_can_delete_ticket": false
                }
            }
        },
        "field_values": [
            {
                "workspace_id": "523866125265220",
                "ticket_id": 1729147439110,
                "field_id": 1729140025846,
                "info": {
                    "workflow_id": 1729140025840,
                    "id": 1729140025846,
                    "parent_id": 0,
                    "client_id": 0,
                    "created_at": 1729140025846,
                    "updated_at": 1729140025846,
                    "type": 1,
                    "version": 0,
                    "is_required": true,
                    "workspace_id": "523866125265220",
                    "title": "Tiêu đề",
                    "hint": "Nhập thông tin",
                    "option": {
                        "is_thousand_separator": false,
                        "keep_decimal_places": 0,
                        "second_title": "",
                        "third_title": "",
                        "unit": "",
                        "time_format": "",
                        "formula": "",
                        "content": "",
                        "formula_args": null,
                        "currency_pool": null,
                        "selection_pool": null,
                        "selections": []
                    }
                },
                "value": "văn bản",
                "unit_id": 0
            }
        ],
        "ref_tickets": null,
        "creator": {
            "id": "*********",
            "lang": "",
            "display_name": "Nguyễn Mạnh Toàn",
            "avatar": "https://image-1.gapowork.vn/images/70cb3793-b281-469e-a788-ece3cb28f0a0.jpeg",
            "avatar_thumb_pattern": "",
            "email": "<EMAIL>",
            "phone_number": "84985942382",
            "title": "Technical Leader",
            "department": "Technical Leader",
            "company_name": "GAPO",
            "employee_code": "17000372",
            "list_departments": [
                {
                    "tree_id": "523866125265220",
                    "tree_name": "GAPO",
                    "department_ids": [
                        "d6081c57e0184cc39a1b5546503a56e4",
                        "2afc6d3cfdd34116a3914413c82bd5fd",
                        "7dbd93a219f64e728d4ecc296fd4bcf1",
                        "6326f64aa8c94f9f93168388ee14db1c"
                    ],
                    "departments": [
                        "Product Development",
                        "Tech",
                        "Flutter",
                        "Technical Leader"
                    ],
                    "department_id": "6326f64aa8c94f9f93168388ee14db1c",
                    "department": "Technical Leader",
                    "role_id": "32eab162290146d0a0ed8759beb646b9",
                    "title": "Technical Leader"
                },
                {
                    "tree_id": "523866125265220",
                    "tree_name": "GAPO",
                    "department_ids": [
                        "56ffc80746c446ab88323eb476e326eb"
                    ],
                    "departments": [
                        "phong ban canlendar"
                    ],
                    "department_id": "56ffc80746c446ab88323eb476e326eb",
                    "department": "phong ban canlendar",
                    "role_id": "37c91335d10e4443a4531c0ad8556b08",
                    "title": "TEST CHUC VU"
                }
            ],
            "status": 1,
            "login_type": "",
            "region_id": "",
            "region": ""
        },
        "reopened": 0
    }
