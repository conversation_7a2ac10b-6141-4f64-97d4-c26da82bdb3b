import 'package:gp_feat_ticket/domain/entity/enums/ticket/ticket_assignee_type.dart';

import '../ticket/ticket_assignee.entity.dart';

class WorkflowAdvanceSettingEntity {
  WorkflowAdvanceSettingEntity({
    required this.workflowId,
    required this.workspaceId,
    this.createdBy,
    this.updatedBy,
    this.createAt,
    this.updatedAt,
    this.version,
    this.isPublished,
    this.allowCancel,
    this.allowDelete,
    this.approveBeforeOnHold,
    this.adminOption,
    this.rateOption,
    this.allowEdittingAfterResolving,
    this.dontAllowEditAfterResolveBehindNode,
  });

  final int workflowId;

  final String workspaceId;

  final int? createdBy, updatedBy;

  final DateTime? createAt, updatedAt;

  final int? version;
  final bool? isPublished;

  final WorkflowAdvanceSettingAllowActionEntity? allowCancel;

  final WorkflowAdvanceSettingAllowActionEntity? allowDelete;

  final WorkflowAdvanceSettingAllowActionEntity? approveBeforeOnHold;

  final WorkflowAdvanceSettingAdminOptionEntity? adminOption;

  final WorkflowAdvanceSettingRateOptionEntity? rateOption;

  final int? allowEdittingAfterResolving;

  final bool? dontAllowEditAfterResolveBehindNode;

  bool get isAllowEdittingAfterResolving {
    return allowEdittingAfterResolving == 1;
  }
}

class WorkflowAdvanceSettingAllowActionEntity {
  const WorkflowAdvanceSettingAllowActionEntity({
    required this.isAllowed,
    this.option,
    this.participantApplied,
  });

  final WorkflowAdvanceSettingAllowOptionEntity? option;

  final WorkflowAdvanceSettingAdminOptionEntity? participantApplied;

  final bool isAllowed;

  bool get hasParticipantApplied {
    return (participantApplied != null) &&
        (participantApplied?.adminCurrentStep == true ||
            participantApplied?.adminWorkflow == true ||
            participantApplied?.assigneeStep == true ||
            participantApplied?.requester == true);
  }
}

// ---------- sub response --------- \
class WorkflowAdvanceSettingAdminOptionEntity {
  const WorkflowAdvanceSettingAdminOptionEntity({
    this.adminWorkflow,
    this.adminCurrentStep,
    this.assigneeStep,
    this.requester,
    this.ref,
    this.participants,
  });

  final bool? adminWorkflow;

  final bool? adminCurrentStep;

  final bool? assigneeStep;

  final bool? requester;

  final dynamic ref;

  final List<WorkflowAdvanceSettingAssigneeEntity>? participants;
}

class WorkflowAdvanceSettingAllowOptionEntity {
  const WorkflowAdvanceSettingAllowOptionEntity({
    this.whenOpen,
    this.whenProcessing,
    this.whenDoneOrClosed,
    this.whenCanceled,
  });

  final bool? whenOpen;

  final bool? whenProcessing;

  final bool? whenDoneOrClosed;

  final bool? whenCanceled;
}

final class WorkflowAdvanceSettingAssigneeEntity {
  const WorkflowAdvanceSettingAssigneeEntity({
    required this.id,
    required this.workspaceId,
    this.members,
    this.name,
    this.createdAt,
    this.updatedAt,
    this.type,
  });

  final DateTime? createdAt, updatedAt;

  final String id;

  final String? name;

  final String workspaceId;

  final List<TicketAssigneeEntity>? members;

  final TicketAssigneeType? type;
}

class WorkflowAdvanceSettingRateOptionEntity {
  const WorkflowAdvanceSettingRateOptionEntity({
    this.autoCloseTime,
    this.autoCloseType,
    this.isAllowed,
  });

  final int? autoCloseTime;
  final String? autoCloseType;
  final bool? isAllowed;
}
