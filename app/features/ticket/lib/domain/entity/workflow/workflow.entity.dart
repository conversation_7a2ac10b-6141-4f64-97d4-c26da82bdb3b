/*
 * Created Date: Friday, 5th July 2024, 08:43:53
 * Author: gapo
 * -----
 * Last Modified: Monday, 24th March 2025 11:49:46
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

// ignore_for_file: public_member_api_docs, sort_unnamed_constructors_first

import 'package:gp_core/core.dart';
import 'package:gp_shared/domain/entity/base/base_list.entity.dart';
import 'package:gp_shared/widgets/tree_view/model.dart';

import '../enums/ticket/workflow_state.dart';
import 'workflow.dart';

class WorkFlowWrapperEntity extends BaseListEntity {
  WorkFlowWrapperEntity({
    required this.workflow,
    required this.groupName,
    this.userInfo,
    this.form,
    this.startNode,
  }) : super(id: workflow.id.toString());

  final WorkFlowEntity workflow;

  final String groupName;

  String path = '';

  final WorkFlowUserInfoEntity? userInfo;

  final WorkFlowFormEntity? form;

  final WorkFlowStartNodeEntity? startNode;

  @override
  String toString() {
    return "WorkFlowWrapperEntity: groupName:$groupName 
workflow: ${workflow.name}";
  }
}

class WorkFlowEntity {
  WorkFlowEntity({
    required this.workflowGroupId,
    required this.id,
    required this.name,
    required this.isPublished,
    this.workspaceId,
    this.createdBy,
    this.prefixId,
    this.form,
    this.createdAt,
    this.updatedAt,
    this.description,
    this.colorSrc,
    this.iconSrc,
    this.advanceSetting,
    this.state,
  });

  final String name;

  final String? workspaceId;

  final int id, workflowGroupId;

  final int? prefixId, createdBy;

  final String? description, colorSrc, iconSrc;

  final bool isPublished;

  final DateTime? createdAt, updatedAt;

  final WorkFlowFormEntity? form;

  final WorkflowAdvanceSettingEntity? advanceSetting;

  final WorkflowState? state;
}

extension WorkFlowListExt on List<WorkFlowWrapperEntity> {
  List<GPTreeNode> toGPTreeNodeList() {
    return map((e) => e.toGPTreeNode()).toList();
  }

  void updatePath(List<WorkFlowGroupWrapperEntity> groupEntities) {
    if (groupEntities.isEmpty) return;

    final flatternGroupEntities = groupEntities.flattern();

    for (var element in this) {
      final groupEntity = flatternGroupEntities.firstWhereOrNull(
          (e) => e.workflowGroup.id == element.workflow.workflowGroupId);
      if (groupEntity != null) {
        element.path = groupEntity.path;
      }
    }
  }
}

extension WorkFlowToGPTreeNodeExt on WorkFlowWrapperEntity {
  GPTreeNode toGPTreeNode() {
    return GPTreeNode<WorkFlowWrapperEntity>(
      title: workflow.name,
      data: this,
      path: '',
      children: [],
    )..isSelectedNotifier.value = isSelected.value;
  }
}
