/*
 * Created Date: Thursday, 31st October 2024, 10:46:41
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Thursday, 31st October 2024 10:46:49
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

// ignore_for_file: invalid_annotation_target

import 'package:freezed_annotation/freezed_annotation.dart';

part 'ticket_move_to_onhold_params.freezed.dart';
part 'ticket_move_to_onhold_params.g.dart';

@Freezed(
  toJson: true,
  fromJson: false,
  copyWith: false,
)
abstract class TicketMoveToOnHoldParams with _$TicketMoveToOnHoldParams {
  factory TicketMoveToOnHoldParams({
    @J<PERSON><PERSON>ey(name: 'reason') required String reason,
  }) = _TicketMoveToOnHoldParams;
