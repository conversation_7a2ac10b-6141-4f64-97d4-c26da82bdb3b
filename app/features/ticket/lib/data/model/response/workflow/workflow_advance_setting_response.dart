import 'package:gp_feat_ticket/domain/entity/enums/ticket/ticket_assignee_type.dart';
import 'package:gp_shared/data/model/datetime_converter.dart';
import 'package:json_annotation/json_annotation.dart';

import '../ticket/ticket_assignee_response.dart';

part 'workflow_advance_setting_response.g.dart';

@JsonSerializable(createToJson: true, fieldRename: FieldRename.snake)
@DateTimeEpochConverter()
class WorkflowAdvanceSettingResponse {
  WorkflowAdvanceSettingResponse({
    required this.workflowId,
    required this.workspaceId,
    this.createdBy,
    this.updatedBy,
    this.createAt,
    this.updatedAt,
    this.version,
    this.isPublished,
    this.allowCancel,
    this.allowDelete,
    this.approveBeforeOnHold,
    this.adminOption,
    this.rateOption,
    this.allowEdittingAfterResolving,
    this.dontAllowEditAfterResolveBehindNode,
  });

  @<PERSON><PERSON><PERSON><PERSON>(name: 'workflow_id')
  final int workflowId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'workspace_id')
  final String workspaceId;

  final int? createdBy, updatedBy;

  final DateTime? createAt, updatedAt;

  final int? version;
  final bool? isPublished;

  @JsonKey(name: 'allow_cancel')
  final WorkflowAdvanceSettingAllowActionResponse? allowCancel;

  @JsonKey(name: 'allow_delete')
  final WorkflowAdvanceSettingAllowActionResponse? allowDelete;

  @JsonKey(name: 'approve_before_on_hold_option')
  final WorkflowAdvanceSettingAllowActionResponse? approveBeforeOnHold;

  final WorkflowAdvanceSettingAdminOptionResponse? adminOption;

  final WorkflowAdvanceSettingRateOptionResponse? rateOption;

  final int? allowEdittingAfterResolving;

  @JsonKey(name: 'dont_allow_edit_after_resolve_behind_node')
  final bool? dontAllowEditAfterResolveBehindNode;

  factory WorkflowAdvanceSettingResponse.fromJson(Map<String, dynamic> json) =>
      _$WorkflowAdvanceSettingResponseFromJson(json);

  Map<String, dynamic> toJson() => _$WorkflowAdvanceSettingResponseToJson(this);
}

@JsonSerializable(createToJson: true, fieldRename: FieldRename.snake)
class WorkflowAdvanceSettingAllowActionResponse {
  const WorkflowAdvanceSettingAllowActionResponse({
    required this.isAllowed,
    this.option,
    this.participantApplied,
  });

  @JsonKey(name: 'option')
  final WorkflowAdvanceSettingAllowOptionResponse? option;

  @JsonKey(name: 'participant_applied')
  final WorkflowAdvanceSettingAdminOptionResponse? participantApplied;

  @JsonKey(name: 'is_allowed')
  final bool isAllowed;

  factory WorkflowAdvanceSettingAllowActionResponse.fromJson(
          Map<String, dynamic> json) =>
      _$WorkflowAdvanceSettingAllowActionResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$WorkflowAdvanceSettingAllowActionResponseToJson(this);
}

// ---------- sub response --------- \
@JsonSerializable(createToJson: true, fieldRename: FieldRename.snake)
class WorkflowAdvanceSettingAdminOptionResponse {
  const WorkflowAdvanceSettingAdminOptionResponse({
    this.adminWorkflow,
    this.adminCurrentStep,
    this.assigneeStep,
    this.requester,
    this.ref,
    this.participants,
  });

  @JsonKey(name: 'admin_workflow')
  final bool? adminWorkflow;

  @JsonKey(name: 'admin_current_step')
  final bool? adminCurrentStep;

  @JsonKey(name: 'assignee_step')
  final bool? assigneeStep;

  @JsonKey(name: 'requester')
  final bool? requester;

  @JsonKey(name: 'Ref')
  final dynamic ref;

  final List<WorkflowAdvanceSettingAssigneeResponse>? participants;

  factory WorkflowAdvanceSettingAdminOptionResponse.fromJson(
          Map<String, dynamic> json) =>
      _$WorkflowAdvanceSettingAdminOptionResponseFromJson(json);
      
  Map<String, dynamic> toJson() =>
      _$WorkflowAdvanceSettingAdminOptionResponseToJson(this);
}

@JsonSerializable(createToJson: true, fieldRename: FieldRename.snake)
class WorkflowAdvanceSettingAllowOptionResponse {
  const WorkflowAdvanceSettingAllowOptionResponse({
    this.whenOpen,
    this.whenProcessing,
    this.whenDoneOrClosed,
    this.whenCanceled,
  });

  @JsonKey(name: 'when_open')
  final bool? whenOpen;

  @JsonKey(name: 'when_processing')
  final bool? whenProcessing;

  @JsonKey(name: 'when_done_or_closed')
  final bool? whenDoneOrClosed;

  @JsonKey(name: 'when_canceled')
  final bool? whenCanceled;

  factory WorkflowAdvanceSettingAllowOptionResponse.fromJson(
          Map<String, dynamic> json) =>
      _$WorkflowAdvanceSettingAllowOptionResponseFromJson(json);
      
  Map<String, dynamic> toJson() =>
      _$WorkflowAdvanceSettingAllowOptionResponseToJson(this);
}

@JsonSerializable(createToJson: true, fieldRename: FieldRename.snake)
@DateTimeEpochConverterFromMS()
final class WorkflowAdvanceSettingAssigneeResponse {
  const WorkflowAdvanceSettingAssigneeResponse({
    required this.id,
    required this.workspaceId,
    this.members,
    this.name,
    this.createdAt,
    this.updatedAt,
    this.type,
  });

  final DateTime? createdAt, updatedAt;

  final String id;

  final String? name;

  @JsonKey(name: 'workspace_id')
  final String workspaceId;

  final List<TicketAssigneeResponse>? members;

  final TicketAssigneeType? type;

  factory WorkflowAdvanceSettingAssigneeResponse.fromJson(
          Map<String, dynamic> json) =>
      _$WorkflowAdvanceSettingAssigneeResponseFromJson(json);
      
  Map<String, dynamic> toJson() =>
      _$WorkflowAdvanceSettingAssigneeResponseToJson(this);
}

@JsonSerializable(createToJson: true, fieldRename: FieldRename.snake)
class WorkflowAdvanceSettingRateOptionResponse {
  const WorkflowAdvanceSettingRateOptionResponse({
    this.autoCloseTime,
    this.autoCloseType,
    this.isAllowed,
  });

  final int? autoCloseTime;
  final String? autoCloseType;
  final bool? isAllowed;

  factory WorkflowAdvanceSettingRateOptionResponse.fromJson(
          Map<String, dynamic> json) =>
      _$WorkflowAdvanceSettingRateOptionResponseFromJson(json);

  Map<String, dynamic> toJson() =>
      _$WorkflowAdvanceSettingRateOptionResponseToJson(this);
}
