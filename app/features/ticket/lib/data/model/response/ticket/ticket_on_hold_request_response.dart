import 'package:gp_shared/data/model/datetime_converter.dart';
import 'package:json_annotation/json_annotation.dart';

part 'ticket_on_hold_request_response.g.dart';

@JsonSerializable(createToJson: true, fieldRename: FieldRename.snake)
@DateTimeEpochConverterFromMS()
final class TicketOnHoldRequestResponse {
  const TicketOnHoldRequestResponse({
    required this.id,
    required this.nodeId,
    required this.ticketId,
    required this.workspaceId,
    required this.requesterId,
    required this.reason,
    this.createdAt,
    this.updatedAt,
  });

  @J<PERSON><PERSON><PERSON>(name: 'workspace_id')
  final String workspaceId;

  @Json<PERSON>ey(name: 'ticket_id')
  final int ticketId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'node_id')
  final int nodeId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'id')
  final int id;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'requester_id')
  final int requesterId;

  final String reason;

  final DateTime? createdAt, updatedAt;

  factory TicketOnHoldRequestResponse.fromJson(Map<String, dynamic> json) =>
      _$TicketOnHoldRequestResponseFromJson(json);

  Map<String, dynamic> toJson() => _$TicketOnHoldRequestResponseToJson(this);
}

/*
  {
    "data": [
        {
            "workspace_id": "523866125265220",
            "ticket_id": 1730360582124,
            "node_id": 1730357286799,
            "id": 1730361193018,
            "requester_id": 105883395,
            "reason": "bao luuuw",
            "images_urls": null,
            "attached_files": null,
            "approver_ids": null,
            "status": 1,
            "created_at": 1730361193018,
            "updated_at": 1730361193018,
            "requester": null
        }
    ]
}
