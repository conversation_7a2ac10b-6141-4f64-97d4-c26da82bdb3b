import 'package:gp_feat_ticket/data/data.dart';
import 'package:gp_feat_ticket/domain/entity/enums/ticket/ticket_node_status.dart';
import 'package:gp_feat_ticket/domain/entity/enums/ticket/ticket_node_type.dart';
import 'package:gp_shared/data/model/datetime_converter.dart';
import 'package:json_annotation/json_annotation.dart';

import '../../../../domain/entity/enums/ticket/ticket_status.dart';

part 'ticket_flowchart_response.g.dart';

@JsonSerializable(createToJson: true, fieldRename: FieldRename.snake)
@DateTimeEpochConverterFromMS()
final class TicketFlowChartResponse {
  const TicketFlowChartResponse({
    required this.workspaceId,
    required this.ticketStatus,
    required this.nodes,
    required this.edges,
    this.ticketId,
    this.ticketStartAt,
    this.ticketActualProcessingDurationSec,
    this.ticketActualFirstResponseAfterSec,
    this.ticketAbsoluteSecondsUntilDeadline,
    this.ticketAbsoluteSecondsUntilResponseDeadline,
    this.ticketDeadlineTs,
    this.ticketResponseDeadlineTs,
    this.ticketFirstResponseAt,
    this.slaAgg,
  });

  final dynamic ticketId;

  @JsonKey(name: 'workspace_id')
  final String workspaceId;

  @JsonKey(name: 'ticket_status')
  final TicketStatus ticketStatus;

  @JsonKey(name: 'ticket_started_at')
  final DateTime? ticketStartAt;

  @JsonKey(name: 'ticket_actual_processing_duration_sec')
  final int? ticketActualProcessingDurationSec;

  @JsonKey(name: 'ticket_actual_first_response_after_sec')
  final int? ticketActualFirstResponseAfterSec;

  @JsonKey(name: 'ticket_absolute_seconds_until_deadline')
  final int? ticketAbsoluteSecondsUntilDeadline;

  @JsonKey(name: 'ticket_absolute_seconds_until_response_deadline')
  final int? ticketAbsoluteSecondsUntilResponseDeadline;

  @JsonKey(name: 'ticket_deadline_ts')
  final int? ticketDeadlineTs;

  @JsonKey(name: 'ticket_response_deadline_ts')
  final int? ticketResponseDeadlineTs;

  @JsonKey(name: 'ticket_first_response_at')
  final int? ticketFirstResponseAt;

  final List<TicketFlowchartNodeResponse> nodes;

  final List<TicketFlowchartEdgeResponse> edges;

  factory TicketFlowChartResponse.fromJson(Map<String, dynamic> json) =>
      _$TicketFlowChartResponseFromJson(json);

  Map<String, dynamic> toJson() => _$TicketFlowChartResponseToJson(this);

  @JsonKey(name: 'sla_agg')
  final TicketSLAAggResponse? slaAgg;
}

@JsonSerializable(createToJson: true, fieldRename: FieldRename.snake)
@DateTimeEpochConverterFromMS()
final class TicketFlowchartNodeResponse {
  const TicketFlowchartNodeResponse({
    required this.workspaceId,
    required this.status,
    required this.createdAt,
    required this.updatedAt,
    this.id,
    this.ticketId,
    this.nodeId,
    this.assigneeId,
    this.type,
    this.updatedBy,
    this.actualProcessingDurationSec,
    this.actualFirstResponseAfterSec,
    this.absoluteSecondsUntilDeadline,
    this.absoluteSecondsUntilResponseDeadline,
    this.responseDeadlineTs,
    this.startedAt,
    this.processedAt,
    this.responseAt,
    this.additionalRequestAt,
    this.onHoldRequestAt,
    this.additionalRequestIn,
    this.deadlineTs,
    this.dateOffMap,
    this.sla,
    this.admins,
    this.supporters,
    this.defaultFollowers,
    this.assignees,
    this.assignee,
    this.option,
    this.noFirstResponseYet,
  });

  final dynamic id;

  @JsonKey(name: 'workspace_id')
  final String workspaceId;

  @JsonKey(name: 'ticket_id')
  final dynamic ticketId;

  @JsonKey(name: 'node_id')
  final dynamic nodeId;

  @JsonKey(name: 'assignee_id')
  final dynamic assigneeId;

  final TicketNodeStatus status;

  final TicketNodeType? type;

  final int? updatedBy;

  @JsonKey(name: 'actual_processing_duration_sec')
  final int? actualProcessingDurationSec;

  @JsonKey(name: 'actual_first_response_after_sec')
  final int? actualFirstResponseAfterSec;

  @JsonKey(name: 'absolute_seconds_until_deadline')
  final int? absoluteSecondsUntilDeadline;

  @JsonKey(name: 'absolute_seconds_until_response_deadline')
  final int? absoluteSecondsUntilResponseDeadline;

  @JsonKey(name: 'response_deadline_ts')
  final int? responseDeadlineTs;

  @JsonKey(name: 'started_at')
  final int? startedAt;

  @JsonKey(name: 'processed_at')
  final int? processedAt;

  @JsonKey(name: 'response_at')
  final int? responseAt;

  @JsonKey(name: 'additional_request_at')
  final int? additionalRequestAt;

  @JsonKey(name: 'on_hold_request_at')
  final int? onHoldRequestAt;

  @JsonKey(name: 'additional_request_in')
  final int? additionalRequestIn;

  @JsonKey(name: 'deadline_ts')
  final int? deadlineTs;

  @JsonKey(name: 'no_first_response_yet')
  final bool? noFirstResponseYet;

  final DateTime? createdAt, updatedAt;

  // TODO: đang thấy trả về map, chưa rõ là cái gì
  final dynamic dateOffMap;

  final TicketSLAResponse? sla;

  final List<TicketAssigneeResponse>? admins,
      supporters,
      defaultFollowers,
      assignees;

  final TicketAssigneeResponse? assignee;

  final TicketNodeOptionResponse? option;

  factory TicketFlowchartNodeResponse.fromJson(Map<String, dynamic> json) =>
      _$TicketFlowchartNodeResponseFromJson(json);

  Map<String, dynamic> toJson() => _$TicketFlowchartNodeResponseToJson(this);
}

@JsonSerializable(createToJson: true, fieldRename: FieldRename.snake)
@DateTimeEpochConverterFromMS()
final class TicketFlowchartEdgeResponse {
  const TicketFlowchartEdgeResponse({
    required this.workspaceId,
    required this.createdAt,
    required this.updatedAt,
    this.ticketId,
    this.currentNodeId,
    this.nextNodeId,
  });

  @JsonKey(name: 'workspace_id')
  final String workspaceId;

  final dynamic ticketId;

  @JsonKey(name: 'current_node_id')
  final dynamic currentNodeId;

  @JsonKey(name: 'next_node_id')
  final dynamic nextNodeId;

  final DateTime? createdAt, updatedAt;

  factory TicketFlowchartEdgeResponse.fromJson(Map<String, dynamic> json) =>
      _$TicketFlowchartEdgeResponseFromJson(json);

  Map<String, dynamic> toJson() => _$TicketFlowchartEdgeResponseToJson(this);
}

@JsonSerializable(createToJson: true, fieldRename: FieldRename.snake)
final class TicketSLAAggResponse {
  const TicketSLAAggResponse({
    this.shiftAgg,
    this.sla,
  });

  final TicketShiftAggResponse? shiftAgg;
  final TicketSLAResponse? sla;

  factory TicketSLAAggResponse.fromJson(Map<String, dynamic> json) =>
      _$TicketSLAAggResponseFromJson(json);

  Map<String, dynamic> toJson() => _$TicketSLAAggResponseToJson(this);
}

@JsonSerializable(createToJson: true, fieldRename: FieldRename.snake)
final class TicketShiftAggResponse {
  const TicketShiftAggResponse({
    this.dateOffs,
    this.weeklySettings,
    this.shift,
  });
  final TicketShiftDetailResponse? shift;
  final dynamic dateOffs;
  final dynamic weeklySettings;

  factory TicketShiftAggResponse.fromJson(Map<String, dynamic> json) =>
      _$TicketShiftAggResponseFromJson(json);

  Map<String, dynamic> toJson() => _$TicketShiftAggResponseToJson(this);
