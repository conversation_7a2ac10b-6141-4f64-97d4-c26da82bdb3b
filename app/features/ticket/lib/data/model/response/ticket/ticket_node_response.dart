import 'package:gp_core/models/assignee.dart';
import 'package:gp_feat_ticket/data/model/response/workflow/workflow_tag_response.dart';
import 'package:gp_feat_ticket/domain/entity/enums/ticket/ticket_assignee_type.dart';
import 'package:gp_feat_ticket/domain/entity/enums/ticket/ticket_node_status.dart';
import 'package:gp_feat_ticket/domain/entity/enums/ticket/ticket_node_type.dart';
import 'package:gp_shared/data/model/datetime_converter.dart';
import 'package:json_annotation/json_annotation.dart';

import 'ticket_assignee_response.dart';

part 'ticket_node_response.g.dart';

@JsonSerializable(createToJson: true, fieldRename: FieldRename.snake)
@DateTimeEpochConverterFromMS()
final class TicketNodeResponse {
  const TicketNodeResponse({
    required this.id,
    required this.workspaceId,
    required this.ticketId,
    required this.nodeId,
    required this.assigneeId,
    required this.status,
    required this.type,
    required this.updatedBy,
    this.actualProcessingDurationSec,
    this.absoluteSecondsUntilDeadline,
    this.actualFirstResponseAfterSec,
    this.absoluteSecondsUntilResponseDeadline,
    this.responseDeadlineTs,
    this.startedAt,
    this.processedAt,
    this.responseAt,
    this.additionalRequestAt,
    this.onHoldRequestAt,
    this.additionalRequestIn,
    this.onHoldRequestIn,
    this.deadlineTS,
    required this.createdAt,
    required this.updatedAt,
    this.dateOffMap,
    this.ticketTags,
    this.admins,
    this.supporters,
    this.defaultFollowers,
    this.assignees,
    this.assignee,
    this.sla,
    this.assigneeGroup,
    this.tasks,
    required this.option,
    this.noFirstResponseYet,
  });

  final int id;

  @JsonKey(name: 'workspace_id')
  final String workspaceId;

  final dynamic ticketId, nodeId;

  @JsonKey(name: 'assignee_id')
  final int assigneeId;

  final TicketNodeStatus status;

  final TicketNodeType type;

  final int updatedBy;

  @JsonKey(name: 'actual_processing_duration_sec')
  final int? actualProcessingDurationSec;

  @JsonKey(name: 'absolute_seconds_until_deadline')
  final int? absoluteSecondsUntilDeadline;

  @JsonKey(name: 'actual_first_response_after_sec')
  final int? actualFirstResponseAfterSec;

  @JsonKey(name: 'absolute_seconds_until_response_deadline')
  final int? absoluteSecondsUntilResponseDeadline;

  @JsonKey(name: 'response_deadline_ts')
  final int? responseDeadlineTs;

  @JsonKey(name: 'no_first_response_yet')
  final bool? noFirstResponseYet;

  final DateTime? startedAt, processedAt, responseAt;

  final DateTime? additionalRequestAt,
      onHoldRequestAt,
      additionalRequestIn,
      onHoldRequestIn,
      deadlineTS;

  final DateTime? createdAt, updatedAt;

  final dynamic dateOffMap;

  final List<TicketNodeTagResponse>? ticketTags;

  final List<TicketAssigneeResponse>? admins,
      supporters,
      defaultFollowers,
      assignees;

  final Assignee? assignee;

  // TODO: handle later
  final dynamic sla;

  final List<TicketAssigneeResponse>? assigneeGroup;

  final dynamic tasks;

  final TicketNodeOptionResponse option;

  factory TicketNodeResponse.fromJson(Map<String, dynamic> json) =>
      _$TicketNodeResponseFromJson(json);

  Map<String, dynamic> toJson() => _$TicketNodeResponseToJson(this);
}

@JsonSerializable(createToJson: true, fieldRename: FieldRename.snake)
@DateTimeEpochConverterFromMS()
final class TicketNodeOptionResponse {
  const TicketNodeOptionResponse({
    this.name,
    this.assigneeType,
    this.workflowFormFieldPermissions,
  });

  final String? name;
  final TicketNodeAssigneeType? assigneeType;
  final List<WorkflowFormFieldPermissionsResponse>? workflowFormFieldPermissions;

  factory TicketNodeOptionResponse.fromJson(Map<String, dynamic> json) =>
      _$TicketNodeOptionResponseFromJson(json);

  Map<String, dynamic> toJson() => _$TicketNodeOptionResponseToJson(this);
}

@JsonSerializable(createToJson: true, fieldRename: FieldRename.snake)
final class WorkflowFormFieldPermissionsResponse {
  const WorkflowFormFieldPermissionsResponse({
    this.workflowFormFieldId,
    this.permission,
    this.permissionPath,
  });

  final int? workflowFormFieldId;
  final int? permission;
  final String? permissionPath;

  factory WorkflowFormFieldPermissionsResponse.fromJson(Map<String, dynamic> json) =>
      _$WorkflowFormFieldPermissionsResponseFromJson(json);

  Map<String, dynamic> toJson() => _$WorkflowFormFieldPermissionsResponseToJson(this);
}

@JsonSerializable(createToJson: true, fieldRename: FieldRename.snake)
@DateTimeEpochConverterFromMS()
final class TicketNodeTagResponse {
  const TicketNodeTagResponse({
    this.tagId,
    this.ticketId,
    this.nodeId,
    required this.tag,
    this.createdAt,
    this.updatedAt,
  });

  final int? tagId;
  final int? ticketId;
  final int? nodeId;
  final WorkflowTagResponse tag;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  factory TicketNodeTagResponse.fromJson(Map<String, dynamic> json) =>
      _$TicketNodeTagResponseFromJson(json);

  Map<String, dynamic> toJson() => _$TicketNodeTagResponseToJson(this);
