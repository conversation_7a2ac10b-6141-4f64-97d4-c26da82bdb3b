/*
 * Created Date: Friday, 5th April 2024, 11:34:12
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Monday, 8th April 2024 14:06:36
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

// ignore_for_file: public_member_api_docs

import 'package:auto_mappr_annotation/auto_mappr_annotation.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/models/bot/bot_response.dart';
import 'package:gp_shared/domain/entity/entity.dart';
import 'package:gp_shared/mapper/entity/assignee_entity_mapper.auto_mappr.dart';


@AutoMappr(
  [
    MapType<Assignee, GPUserEntity>(
      fields: [
        Field(
          'id',
          custom: AssigneeEntityMapper.mapIntIdToString,
        ),
        Field(
          'name',
          from: 'displayName',
        ),
      ],
    ),
    MapType<AssigneeEntity, GPUserEntity>(
      fields: [
        Field(
          'id',
          custom: AssigneeEntityMapper.mapAssigneeEntityId,
        ),
      ],
    ),
    MapType<GPUserEntity, AssigneeEntity>(
      fields: [
        Field(
          'id',
          custom: AssigneeEntityMapper.mapGPUserEntityId,
        ),
        Field(
          'displayName',
          custom: AssigneeEntityMapper.mapGPUserEntityDisplayName,
        ),
      ],
    ),
    // --------- model to entity --------- \
    MapType<Assignee, AssigneeEntity>(),
    MapType<Work, WorkEntity>(),
    MapType<Info, InfoEntity>(),
    MapType<Conversation, ConversationEntity>(),
    MapType<OrganizationDepartment, OrganizationDepartmentEntity>(),
    MapType<OrganizationRole, OrganizationRoleEntity>(),
    MapType<ChatBotModel, ChatBotEntity>(),
    MapType<SelectInviteesOptions, SelectMemberEntity>(
      fields: [
        Field('assigneeEntities', from: 'selectedMembers'),
        Field('conversationEntities', from: 'selectedThreads'),
        Field('departmentEntities', from: 'selectedDepartments'),
        Field('roleEntities', from: 'selectedRoles'),
        Field('chatBotEntities', from: 'selectedBots'),
      ],
    ),
    //  --------- entity to model  --------- \
    MapType<AssigneeEntity, Assignee>(),
    MapType<WorkEntity, Work>(),
    MapType<InfoEntity, Info>(),
    MapType<ConversationEntity, Conversation>(),
    MapType<OrganizationRoleEntity, OrganizationRole>(),
    MapType<OrganizationDepartmentEntity, OrganizationDepartment>(),
    MapType<ChatBotEntity, ChatBotModel>(
      fields: [
        Field(
          'id',
          custom: AssigneeEntityMapper.mapChatBotEntityId,
        ),
        Field(
          'name',
          custom: AssigneeEntityMapper.mapChatBotEntityName,
        ),
      ],
    ),
  ],
)
class AssigneeEntityMapper extends $AssigneeEntityMapper {
  const AssigneeEntityMapper();

  static String mapIntIdToString(Assignee input) {
    return input.id.toString();
  }

  static String? mapAssigneeEntityId(AssigneeEntity input) {
    return ParserHelper.parseString(input.id);
  }

  static int mapGPUserEntityId(GPUserEntity input) {
    return ParserHelper.parseInt(input.id) ?? -1;
  }

  static String mapGPUserEntityDisplayName(GPUserEntity input) {
    return input.displayName ?? '';
  }

  static String mapChatBotEntityId(ChatBotEntity input) {
    return input.id;
  }

  static String mapChatBotEntityName(ChatBotEntity input) {
    return input.name;
  }
}
