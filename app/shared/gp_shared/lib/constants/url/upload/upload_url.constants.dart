/*
 * Created Date: 4/01/2024 14:23:55
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Saturday, 24th August 2024 16:13:25
 * Modified By: ToanNM
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

// ignore_for_file: public_member_api_docs

import 'package:gp_core/configs/constants.dart';
import 'package:gp_core/core.dart';

/// GapoUploadUrl
final class UploadUrlConstants {
  static const String kUploadAPI = '/media/v1.0';

  // ---------- Upload file: https://gapowork.redoc.ly/tag/Upload-Service_image ---------- \
  static const String kFiles = '/files';
  static const String kVideos = '/videos';
  static const String kImages = '/images';
  static const String kAudios = '/audios';

  // ---------- local storage ---------- \
  static const String kUploadStorageFolder = '/GapoWork_uploads';

  // ---------- keys ---------- \
  static const kMaxChunkSize = 4 * 1 << 20; //100 * 1024;
  static const kMetaDataTitle = 'title';
  static const kMetaDataFileType = 'file_type';
  static const kMetaDataFileSize = 'file_size';
  static const kContentDisposition = 'Content-Disposition';
  static const kContentType = 'Content-Type';

  // ---------- TUS ---------- \
  static String get kTusServerUrl {
    if (Constants.isStaging) {
      return 'https://tusd-staging.gapowork.vn/files/';
    } else if (Constants.isProduction) {
    } else if (Constants.isOnPremise) {}

    return 'https://tusd-staging.gapowork.vn/files/';
  }
}
