/*
 * Created Date: 4/01/2024 09:40:42
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Tuesday, 9th April 2024 10:00:18
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

// ignore_for_file: public_member_api_docs

/// GapoDriveUrl
final class DriveUrlConstants {
  static const String kDriveAPI = '/file-management/v1.0/storage';
  static const String kDriveAccessAPI = '/file-management/v1.0/access';
  // --------- home: https://gapowork.redoc.ly/tag/File-Management-Service_Home --------- \

  static const String kPublic = '/public';

  static const String kPersonal = '/personal';

  static const String kShareWithMe = '/share-with-me';

  static const String kTrash = '/trash';

  // --------- file: https://gapowork.redoc.ly/tag/File-Management-Service_Tap-tin --------- \
  static const String kFiles = '/files';

  // --------- file: https://gapowork.redoc.ly/tag/File-Management-Service_Thu-muc --------- \
  static const String kFolder = '/folder';

  // --------- url: https://gapowork.redoc.ly/tag/File-Management-Service_URL-(Shortcut) --------- \
  static const String kUrl = '/url';

  // --------- file/folder/url actions --------- \
  static const String kSearch = '/search';
  static const String kInfo = '/info';
  static const String kLog = '/log';
  static const String kShareLink = '/share-link';
  static const String kMove = '/move';
  static const String kFavorite = '/favorite';
  static const String kRename = '/rename';
  static const String kRecover = '/recover';
  static const String kDelete = '/delete';
  static const String kDirectoryTree = '/directory-tree';
  static const String kUploadInfo = '/upload-info';
  static const String kDownload = '/download';

  // --------- Access: https://gapowork.redoc.ly/tag/File-Management-Service_Access --------- \
  static const String kAccess = '/access';
  static const String kShare = '/share';
  static const String kShared = '/shared';
  static const String kRevoke = '/revoke';

  // --------- Backend response code / message ---------
  static const String kBEDeleteAllSuccessMessage = 'Done';
  static const String kBESharedSuccessMessage = 'Item shared';
}
