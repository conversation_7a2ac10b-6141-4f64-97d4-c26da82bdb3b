import 'dart:io';

import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/utils/secure_download/gp_secure_download.dart';
import 'package:gp_shared/domain/entity/attachment/gp_attachment_file.entity.dart';
import 'package:gp_shared/widgets/attachment/local_video_thumb_widget.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';

import '../../data/model/attachment/gp_attachment_file_type.dart';
import 'model.dart';

class GPAttachmentFilePreview extends StatelessWidget {
  final GPAttachmentFileEntity item;
  final GpAttachmentFilePreviewSize previewSize;

  const GPAttachmentFilePreview({
    required this.item,
    required this.previewSize,
  });

  @override
  Widget build(BuildContext context) {
    if (item.uploadType == null) {
      return const SizedBox();
    }

    Widget? overlayIcon;

    final hasAbsolutePathThumb = item.hasAbsolutePathThumb;

    if (item.uploadType == GPAttachmentFileUploadType.video) {
      overlayIcon = hasAbsolutePathThumb
          ? SvgWidget(Assets.PACKAGES_GP_ASSETS_IMAGES_IC24_FILL_PLAY_PNG,
              color: GPColor.functionAlwaysLightPrimary)
          : null;
    }

    return Stack(
      alignment: Alignment.center,
      children: [
        _PreviewIcon(item: item, previewSize: previewSize),
        Positioned.fill(
          child: ValueListenableBuilder(
            valueListenable: item.rxIsLoading,
            builder: (context, value, child) {
              if (!value) return const SizedBox();

              return StreamBuilder(
                stream: item.localEntity?.fileEntity.rxProgress,
                builder: (context, snapshot) {
                  if (snapshot.hasData) {
                    return _buildLoadingOverlay(
                      snapshot.data?.progress ?? 0,
                    );
                  }

                  return const SizedBox();
                },
              );
            },
          ),
        ),
        if (overlayIcon != null)
          Positioned.fill(child: _buildIconOverlay(overlayIcon))
      ],
    );
  }

  Widget _buildLoadingOverlay(double progress) {
    return Stack(
      alignment: AlignmentDirectional.bottomCenter,
      children: [
        Container(
          decoration: BoxDecoration(
            color: Color(0x99FFFFFF),
            borderRadius: BorderRadius.circular(8),
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(1, 0, 1, 0.35),
          child: ClipRRect(
            clipBehavior: Clip.hardEdge,
            borderRadius: const BorderRadius.only(
              bottomRight: Radius.circular(200.0),
              bottomLeft: Radius.circular(200.0),
            ),
            child: LinearPercentIndicator(
              lineHeight: 4,
              percent: progress / 100,
              progressColor: GPColor.functionAccentWorkSecondary,
              backgroundColor: Color(0x99000000),
              padding: const EdgeInsets.all(0),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildIconOverlay(Widget icon) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
      ),
      alignment: Alignment.center,
      child: icon,
    );
  }
}

class _PreviewIcon extends StatelessWidget {
  const _PreviewIcon({
    required this.item,
    required this.previewSize,
  });

  final GPAttachmentFileEntity item;

  final GpAttachmentFilePreviewSize previewSize;

  @override
  Widget build(BuildContext context) {
    final hasLocalFile = item.localEntity != null;
    final hasAbsolutePathThumb = item.hasAbsolutePathThumb;

    // Widget wrapperPreviewIcon(Widget iconWidget) {
    //   return Container(
    //     decoration: BoxDecoration(
    //         borderRadius: BorderRadius.circular(8),
    //         border: Border.all(color: GPColor.lineTertiary)),
    //     child: iconWidget,
    //   );
    // }

    Widget previewLocalAssetImage() {
      return _AttachmentFilePreviewLocalAssetImage(
        previewSize: previewSize,
        imagePath: FileExtensionType.video.iconPath,
        padding: const EdgeInsets.all(0),
        hasBoxDecoration: false,
      );
    }

    Widget previewNetworkImage({
      bool isVideo = false,
    }) {
      return _AttachmentFilePreviewNetworkImage(
        previewSize: previewSize,
        url: item.thumbStr,
        isVideo: isVideo,
      );
    }

    switch (item.uploadType!) {
      case GPAttachmentFileUploadType.audio:
      case GPAttachmentFileUploadType.file:
        if (kVideoFileExtensions.contains(item.fileType)) {
          return hasAbsolutePathThumb
              ? previewNetworkImage()
              : previewLocalAssetImage();
        } else {
          return _AttachmentFilePreviewLocalAssetImage(
            previewSize: previewSize,
            imagePath: fileExtensionType(fileName: item.fileName).iconPath,
            padding: const EdgeInsets.all(0),
            hasBoxDecoration: false,
          );
        }
      case GPAttachmentFileUploadType.image:
        return hasLocalFile
            ? _AttachmentFilePreviewLocalImage(
                file: item.localEntity!.file,
                previewSize: previewSize,
              )
            : hasAbsolutePathThumb
                ? previewNetworkImage()
                : const SizedBox();
      case GPAttachmentFileUploadType.video:
        return hasLocalFile
            ? LocalThumbWidget(
                xFile: XFile(
                  item.localEntity!.file.path,
                ),
                thumbSize: previewSize.width,
                borderRadius: 8,
              )
            : previewNetworkImage(isVideo: true);
      // return hasAbsolutePathThumb
      //     ? previewNetworkImage()
      //     : LocalThumbWidget(
      //         xFile: XFile(
      //           item.localEntity!.file.path,
      //         ),
      //       );
      // previewLocalAssetImage()
    }
  }
}

class GPAttachmentFilePreviewHorizontal extends StatelessWidget {
  const GPAttachmentFilePreviewHorizontal({
    required this.item,
    required this.previewSize,
    this.onRemove,
    super.key,
  });

  final GPAttachmentFileEntity item;

  final GpAttachmentFilePreviewSize previewSize;

  final Function()? onRemove;

  String get fileName {
    if (item.localEntity != null) {
      return basename(item.localEntity!.file.path);
    }

    return item.fileName ?? item.name ?? '';
  }

  @override
  Widget build(BuildContext context) {
    final dateTimeStr = item.dateTimeStr;

    return Row(
      children: [
        ValueListenableBuilder(
          valueListenable: item.rxIsLoading,
          builder: (context, value, child) {
            final previewIcon = _PreviewIcon(
              item: item,
              previewSize: previewSize,
            );
            if (!value) return previewIcon;

            return StreamBuilder(
              stream: item.localEntity?.fileEntity.rxProgress,
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  final progress = snapshot.data?.progress ?? 0;

                  if (progress == 100) {
                    return previewIcon;
                  }

                  return _CircleCloseProgressView(
                    progress: progress,
                    onClose: onRemove,
                  );
                }

                return previewIcon;
              },
            );
          },
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                fileName,
                style: textStyle(GPTypography.headingSmall),
              ),
              const SizedBox(height: 4),
              Row(
                children: [
                  Text(
                    item.fileSizeStr,
                    style: textStyle(GPTypography.bodySmall),
                  ),
                  if (dateTimeStr.isNotEmpty) ...{
                    Text(' • '),
                    Text(
                      dateTimeStr,
                      style: textStyle(GPTypography.bodySmall),
                    ),
                  }
                ],
              )
            ],
          ),
        )
      ],
    ).paddingSymmetric(vertical: 12);
  }
}

class _AttachmentFilePreviewNetworkImage extends StatelessWidget {
  final String url;
  final bool isVideo;
  final GpAttachmentFilePreviewSize previewSize;

  const _AttachmentFilePreviewNetworkImage({
    required this.url,
    required this.previewSize,
    this.isVideo = false,
  });

  @override
  Widget build(BuildContext context) {
    PaintingBinding.instance.imageCache.clear();
    if (url.isEmpty) return const SizedBox();

    final iconSize = previewSize.width;

    final headers = GpSecureDownload.headers(url);

    return ClipRRect(
      key: UniqueKey(),
      borderRadius: BorderRadius.circular(8),
      child: Image.network(
        url,
        fit: BoxFit.cover,
        width: iconSize,
        height: iconSize,
        errorBuilder: (context, error, stackTrace) {
          logDebug('DEBUG: gp_attachment_file_preview: error loading image: $error, \nheaders: $headers');

          return Container(
            width: iconSize,
            height: iconSize,
            decoration: BoxDecoration(
                border: Border.all(color: GPColor.lineTertiary),
                borderRadius: BorderRadius.circular(8)),
            child: isVideo
                ? _AttachmentFilePreviewLocalAssetImage(
                    previewSize: previewSize,
                    imagePath: FileExtensionType.video.iconPath,
                    padding: const EdgeInsets.all(14),
                  )
                : null,
          );
        },
        headers: headers,
      ),
    );

    // return GPNetworkImage(
    //   borderRadius: BorderRadius.circular(8),
    //   fit: BoxFit.cover,
    //   url: url,
    //   placeholder: Container(color: GPColor.gray),
    //   width: _filePreviewIconSize,
    //   height: _filePreviewIconSize,
    // );
  }
}

class _AttachmentFilePreviewLocalAssetImage extends StatelessWidget {
  final GpAttachmentFilePreviewSize previewSize;
  final String imagePath;
  final EdgeInsetsGeometry? padding;
  final bool hasBoxDecoration;

  const _AttachmentFilePreviewLocalAssetImage({
    required this.previewSize,
    required this.imagePath,
    this.hasBoxDecoration = true,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final iconSize = previewSize.width;

    return Container(
      padding: padding,
      width: iconSize,
      height: iconSize,
      decoration: hasBoxDecoration
          ? BoxDecoration(
              borderRadius: const BorderRadius.all(Radius.circular(8)),
              border: Border.all(color: GPColor.lineTertiary),
            )
          : null,
      child: SvgWidget(imagePath),
    );
  }
}

class _AttachmentFilePreviewLocalImage extends StatelessWidget {
  const _AttachmentFilePreviewLocalImage({
    required this.file,
    required this.previewSize,
  });

  final File file;
  final GpAttachmentFilePreviewSize previewSize;

  @override
  Widget build(BuildContext context) {
    final iconSize = previewSize.width;

    return ClipRRect(
      key: UniqueKey(),
      borderRadius: BorderRadius.circular(8),
      child: Image.file(
        file,
        fit: BoxFit.cover,
        width: iconSize,
        height: iconSize,
        errorBuilder: (context, error, stackTrace) {
          return Container(
            width: iconSize,
            height: iconSize,
            decoration: BoxDecoration(
                border: Border.all(color: GPColor.lineTertiary),
                borderRadius: BorderRadius.circular(8)),
            child: null,
          );
        },
      ),
    );
  }
}

class _CircleCloseProgressView extends StatelessWidget {
  final double progress;

  const _CircleCloseProgressView({
    required this.progress,
    this.onClose,
  });
  final Function()? onClose;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onClose,
      customBorder: CircleBorder(),
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          color: GPColor.indigo,
          borderRadius: BorderRadius.circular(16),
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            CircularProgressIndicator(
              color:
                  GPColor.contentInversePrimary.withAlpha((0.5 * 255).toInt()),
              strokeWidth: 4,
              value: progress,
            ).paddingAll(2),
            const Icon(
              Icons.close,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}
