/*
 * Created Date: 1/02/2024 11:29:52
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Friday, 8th March 2024 16:02:31
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'package:gp_core_v2/base/bloc/bloc.dart';
import 'package:gp_core_v2/base/widgets/base_list_widget.dart';

import '../../../../domain/entity/base/base_list.entity.dart';
import 'update_usecase_enum.dart';

class BaseListState extends CoreV2BaseState {
  const BaseListState();
}

class BaseListDataLoaded<T> extends BaseListState {
  const BaseListDataLoaded({
    required this.isInitialLoad,
    required this.canNextPage,
    this.data,
    this.nextLink,
    this.page,
  });

  final bool isInitialLoad;
  final List<T>? data;
  final bool canNextPage;
  final String? nextLink;
  final int? page;
}

class BaseListHasError extends BaseListState {
  const BaseListHasError({
    this.errror,
    this.stackTrace,
  });

  final Object? errror;
  final StackTrace? stackTrace;
}

final class BaseListModeChanged extends BaseListState {
  BaseListModeChanged({required this.mode});

  final ListViewMode mode;
}

final class BaseListItemOnCheckedChanged<T> extends BaseListState {
  BaseListItemOnCheckedChanged({
    required this.entitiesByPosition,
    this.headerSelected,
  });

  final Map<int, T> entitiesByPosition;
  final bool? headerSelected;
}

final class BaseEntitiesChanged<T extends BaseListEntity> extends BaseListState {
  const BaseEntitiesChanged({
    required this.entities,
    required this.updateCase,
    required this.effectiveFolderId,
    this.entitiesByPosition,
  });

  final List<T> entities;
  final BaseListChangedCase updateCase;
  final Map<int, T>? entitiesByPosition;

  ///
  /// Folder cần update lại [entities] theo [updateCase] tương ứng
  /// E.g:
  /// - Có 3 màn hình 1 (folder_id = 1), 2(1), 3(3)
  /// - Nếu pass [effectiveFolderId] = FolderEntity(1)
  /// => update lại màn hình 1 và 2
  ///
  final String effectiveFolderId;
}

final class BaseDeleteAllFileState extends BaseListState {
  const BaseDeleteAllFileState(this.deleteCase);

  final DriveDeleteAllFileStateCase deleteCase;
}

enum DriveDeleteAllFileStateCase {
  needReload,
  deletedAll,
  error,
