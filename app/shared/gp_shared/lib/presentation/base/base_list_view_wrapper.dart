import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core_v2/base/widgets/base_list_view_listener.dart';
import 'package:gp_core_v2/base/widgets/base_list_widget.dart';

import '../../domain/entity/base/base_list.entity.dart';
import 'base.dart';

/// Wrapper list
class GPBaseListViewWrapperV2 extends StatefulWidget {
  const GPBaseListViewWrapperV2({
    required this.params,
    required this.builder,
    super.key,
  });

  final GPBaseListViewParamsV2 params;

  final GPBaseListViewV2 Function(
    GPBaseListViewParamsV2 params,
  ) builder;

  @override
  State<GPBaseListViewWrapperV2> createState() =>
      _GPBaseListViewWrapperV2State();
}

class _GPBaseListViewWrapperV2State extends State<GPBaseListViewWrapperV2> {
  @override
  void initState() {
    super.initState();

    widget.params.listBloc
      ..initInputData(widget.params.inputData)
      ..loadData();
  }

  @override
  Widget build(BuildContext context) {
    return widget.builder(
      widget.params,
    );
  }
}

final class GPBaseListViewParamsV2 {
  GPBaseListViewParamsV2({
    required this.listBloc,
    this.onRefresh,
    this.inputData,
    this.needLoadDataWhenInitial = true,
  });

  GPBaseListBlocV2 listBloc;
  dynamic inputData;
  final Function? onRefresh;
  BaseListWidgetControllerV2? listController;

  final bool needLoadDataWhenInitial;
}

/// Abstract ListView, xử lý toàn bộ các phần buildItem ở đây,
// ignore: must_be_immutable
abstract class GPBaseListViewV2<T extends BaseListEntity>
    extends BaseListPage<T>
    with BaseListViewMixin<T>
    implements _BaseEntitiesChangedListener<T> {
  GPBaseListViewV2({
    required this.params,
    super.shrinkWrap = false,
    super.customBuilder,
    super.inputData,
    super.dismissKeyboardOnScroll,
    super.key,
  }) {
    super.listController.setOnBaseListViewListenerV2(this);
    params.listController = super.listController;
  }

  final GPBaseListViewParamsV2 params;

  @override
  Future onLoadData(
    BuildContext context, {
    dynamic inputData,
    bool isRefreshData = true,
  }) async {
    if (isRefreshData && params.onRefresh != null) {
      params.onRefresh?.call();
    } else {
      params.listBloc.loadData(
        isInitialLoad: isRefreshData,
        inputData: inputData,
      );
    }
  }

  bool isBlocRegistered(BuildContext context) {
    try {
      BlocProvider.of<GPBaseListBlocV2>(context);
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Widget build(BuildContext context) {
    final childWidget = BlocListener<GPBaseListBlocV2, BaseListState>(
      bloc: params.listBloc,
      listener: (context, state) {
        if (state is BaseEntitiesChanged<T>) {
          final entities = state.entities;

          switch (state.updateCase) {
            case BaseListChangedCase.created:
              if (state.entitiesByPosition != null) {
                handleEntitiesCreatedByPosition(state.entitiesByPosition!);
              } else {
                handleEntitiesCreated(entities);
              }
              break;
            case BaseListChangedCase.edited:
              handleEntitiesEdited(entities);
              break;
            case BaseListChangedCase.deleted:
              handleEntitiesDeleted(entities);
              break;
            case BaseListChangedCase.recovery:
              assert(state.entitiesByPosition != null);

              if (state.entitiesByPosition != null) {
                handleEntitiesCreatedByPosition(state.entitiesByPosition!);
              }
              break;
          }
        }
      },
      child: super.build(context),
      // ),
    );

    if (!isBlocRegistered(context)) {
      return BlocProvider<GPBaseListBlocV2>(
          create: (context) => params.listBloc, child: childWidget);
    }

    return childWidget;
  }

  // ---------- handle entities changed, override if needed ---------- \
  @override
  void handleEntitiesCreated(List<T> entities) {
    listController.appendData(
      entities,
      needHighlightAddedItems: true,
      reverse: true,
    );

    params.listBloc.add(
      BaseListUpdateDataEvent(data: listController.data),
    );
  }

  @override
  void handleEntitiesCreatedByPosition(
    Map<int, T> addEntitiesByPosition,
  ) {
    listController.appendDataByPosition(
      addEntitiesByPosition,
      needHighlightAddedItems: true,
    );

    params.listBloc.add(
      BaseListUpdateDataEvent(data: listController.data),
    );
  }

  @override
  void handleEntitiesEdited(List<T> entities) {
    for (var updated in entities) {
      final updateEntity =
          listController.data.firstWhereOrNull((e) => e.id == updated.id);

      if (updateEntity != null) {
        final currentIndex = listController.data.indexOf(updateEntity);

        if (currentIndex != -1) {
          listController.data[currentIndex] = updated;
          listController.highlight(currentIndex, currentIndex);
        }
      }
    }
  }

  @override
  void handleEntitiesDeleted(List<T> entities) {
    for (var element in entities) {
      listController.data.removeWhere((rElement) => rElement.id == element.id);
    }

    listController.notifyDataSetChanged();

    params.listBloc.add(
      BaseListUpdateDataEvent(data: listController.data),
    );
  }
}

/// listener for scaleup
abstract class _BaseEntitiesChangedListener<T extends BaseListEntity> {
  void handleEntitiesCreated(List<T> entities);

  void handleEntitiesCreatedByPosition(Map<int, T> addEntitiesByPosition);

  void handleEntitiesEdited(List<T> entities);

  void handleEntitiesDeleted(List<T> entities);
}
