import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/utils/gp_sentry.dart';
import 'package:gp_feat_calendar/routes/router_name.dart';

import 'app_behavior/assignee_picker_behavior.dart';
import 'app_behavior/calendar_behavior.dart';
import 'app_behavior/general_behavior.dart';
import 'app_behavior/rtf_editor_behavior.dart';
import 'app_behavior/task_behavior.dart';
import 'app_behavior/ticket_behavior.dart';

/*
updated 07/06/2024
Flow:
+-----------------------------------------------------+-----+-------------------------------------------------------------------------------+
|                         ios                         |     |                                    flutter                                    |
+-----------------------------------------------------+-----+-------------------------------------------------------------------------------+
| 1. makeEngine: khởi tạo engine                      | <-> | Init app, call method "flutterInit" sau khi init xong (updated at 07/06/2024) |
+-----------------------------------------------------+-----+-------------------------------------------------------------------------------+
| 2. setAppInfo: truyền token cho phía Flutter        | ->  | Nhận callback kèm params: receive token info                                  |
+-----------------------------------------------------+-----+-------------------------------------------------------------------------------+
| 3. sendMoreInfo: mở màn hình tương ứng phía Flutter | ->  | Nhận entryPoint, kèm params: open entryPoint tương ứng                        |
+-----------------------------------------------------+-----+-------------------------------------------------------------------------------+
*/

// const _maxRetry = 1;

class LoadingScreen extends StatefulWidget {
  const LoadingScreen({super.key});

  @override
  State<LoadingScreen> createState() => _LoadingScreenState();
}

class _LoadingScreenState extends State<LoadingScreen>
    with
        GapoAppGeneralBehavior,
        GapoAppTaskBehavior,
        GapoAppCalendarBehavior,
        GapoAppRtfEditorBehavior,
        GapoAppAssigneePickerBehavior,
        GapoAppTicketBehavior,
        _DebugMixin {
  final MethodChannel _channel = Deeplink.channel;
  String routeName = "";
  String nextRoute = "";

  Timer? timer;

  /// retry time for all callback
  int currentRetry = 0;

  void appendMessage(String message, {Map<String, dynamic>? data}) {
    if (Platform.isIOS) {
      GPCoreTracker().appendMessage(message, data: data);
    }
  }

  void _setupChannel() {
    nextRoute = Get.parameters["next"] ?? "";
    routeName = nextRoute;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _channel.setMethodCallHandler((call) async {
        logDebug(
            'receive global method call
method: ${call.method}
args: ${call.arguments}');

        appendMessage(
          'receive global method call',
          data: {
            'method': call.method,
            'args': call.arguments.toString(),
            'route': {
              'current_route': Get.currentRoute,
              'next_route': routeName,
            }
          },
        );

        _onMethodCallHandler(call, routeName);
      });
    });
  }

  Future _onMethodCallHandler(MethodCall call, String currentRoute) async {
    bool result = false;

    try {
      appendDebugMessage(
          '* callMethod: ${call.method} 
currentRoute:$currentRoute - ${Get.currentRoute}');

      if (call.method == DeepLinkMethod.setLanguage) {
        result = await onSetLanguage(call);
      } else if (call.method == DeepLinkMethod.updateTokenInfo) {
        result = await onUpdateTokenInfo(call, currentRoute);
      } else if (call.method == DeepLinkMethod.updateUserInfo) {
        result = await onUpdateUserInfo(call);
      } else if (call.method == "setAppInfo") {
        result = await onSetAppInfo(call, currentRoute);
      } else if (call.method == "setTaskArguments") {
        result = await onSetTaskArguments(call);
      } else if (call.method == "setTicketArguments") {
        result = await onSetTicketArguments(call);
      } else if (call.method == "setProject") {
        result = await onSetProject(call, currentRoute);
      } else if (call.method == "setProjectArguments") {
        result = await onSetProjectArguments(call, currentRoute);
      } else if (call.method == DeepLinkMethod.setEvent) {
        result = await onSetEvent(call);
      } else if (call.method == "setAlbum") {
        return _handleError('not implemented ${call.method}');
      } else if (call.method == "pop") {
        result = await onPop(call);
      } else if (call.method == "refreshEventList") {
        if (!isSetAppInfo) return false;

        result = await onRefreshEventList(call);
      } else if (call.method == DeepLinkMethod.refreshOutlookSyncIfNeeded) {
        result = await onRefreshOutlookSyncIfNeeded(call);
      } else if (call.method == DeepLinkMethod.logout) {
        result = await onLogout(call);
      } else if (call.method == DeepLinkMethod.initializeCalendarCollab) {
        result = await onInitializeCalendarCollab(call);
      } else if (call.method == DeepLinkMethod.scrollToTop) {
        result = await onScrollToTop(call);
      } else if (call.method == DeepLinkMethod.setPlatformNavigatorParams) {
        result = await onSetPlatformNavigatorParams(call);
      } else if (call.method == DeepLinkMethod.syncSuccessfully) {
        result = await onSyncSuccessfullys(call);
      } else if (call.method == DeepLinkMethod.initializeTaskCollab) {
        result = await onInitializeTaskCollab(call, currentRoute);
      } else if (call.method == DeepLinkMethod.refreshTaskCollab) {
        result = await onRefreshTaskCollab(call);
      } else if (call.method == DeepLinkMethod.initTaskListData) {
        result = await onInitTaskListData(call);
      } else if (call.method == DeepLinkMethod.initializeAssigneePicker) {
        result = await onInitializeAssigneePicker(call);
      } else if (call.method == DeepLinkMethod.initializeRtfEditor) {
        result = await onInitializeRtfEditor(call);
      } else if (call.method == DeepLinkMethod.updateSwipeBackGestureIfNeeded) {
        result = await onUpdateSwipeBackGestureIfNeeded(call);
      } else if (call.method == DeepLinkMethod.createTaskFromMessage) {
        result = await onCreateTaskFromMessage(call);
      } else {
        // `throw exception` native sẽ không nhận được callback
        return _handleError('not implemented ${call.method}');
      }
    } catch (e, s) {
      GPCoreTracker().appendError(
        'Flutter.app._setupChannel',
        data: {'ex': e, 'stackTrace': s},
      );
      GPCoreTracker().sendLog(
        message: 'Flutter.app._setupChannel',
      );
      appendDebugMessage('* has error: $e, 
$s');

      // `throw exception` native sẽ không nhận được callback
      Get.offAndToNamed(RouterName.emptyScreen);
      return _handleError("Got an error ${e.toString()} from Flutter");
    }

    appendDebugMessage('========= ${call.method}: $result');
  }

  /// Không sử dụng `throw exception` vì native sẽ không nhận được callback
  Map<String, dynamic> _handleError(
    String message, {
    String? code,
  }) {
    logDebug(
        "DEBUG: _handleError methodChannel with code: $code, message: $message");

    SentryEvent(
      message: SentryMessage('Loading Screen $message - $code'),
      logger: "Flutter:main",
      level: SentryLevel.error,
      fingerprint: [message],
    );

    return {
      "code": code ?? "",
      "message": message,
    };
  }

  @override
  void onAppendError(String error) {
    appendDebugMessage('* onAppendError -> $error');
  }

  @override
  void initState() {
    super.initState();

    _setupChannel();

    initLogFile().then((v) {
      try {
        // delay 1 khoảng thời gian để native có thể nhận được callback
        // nếu không delay sẽ xảy ra tình trạng trắng màn hình ở trên 1 vài thiết bị ios 15 trở lên.
        final delayMs = (routeName == CalendarRouterName.calendar ||
                routeName ==
                    RouterName.routeWithoutAnimation(
                        CalendarRouterName.calendar))
            ? 150
            : 500;

        Future.delayed(Duration(milliseconds: delayMs)).then((v) {
          Deeplink.flutterInit();
        });
      } catch (e) {
        appendMessage('flutterInit error -> $e');
        appendDebugMessage('*** flutterInit error -> $e');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: GPBackButton(),
        elevation: 0,
      ),
      body: Center(
        child: CircularProgressIndicator.adaptive(),
      ),
    );
  }
}

mixin _DebugMixin {
  File? logFile;
  Future<File?> initLogFile() async {
    if (kDebugTool == false) return null;

    final String logPath =
        await Utils.getDownloadFileName('debug_gapo_flutter.txt');

    File file = File(logPath);

    if (!file.existsSync()) {
      file.createSync(recursive: true);
    }

    logFile = file;
    appendDebugMessage('----------
${DateTime.now().toString()}');

    return logFile;
  }

  Future appendDebugMessage(String message) async {
    if (kDebugTool == false) return null;
    if (logFile == null) return null;

    await logFile?.writeAsString('$message 

', mode: FileMode.append);
  }
}
