/*
 * Created Date: 1/12/2023 14:39:37
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Tuesday, 10th June 2025 09:00:45
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gp_core/base/networking/base/feature_flag.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/navigator/router_mapping.dart';
import 'package:gp_core/navigator/tag_management.dart';
import 'package:gp_core/routes/pages.dart';
import 'package:gp_dio_log/gp_dio_log.dart';
import 'package:gp_feat_calendar/routes/pages.dart';
import 'package:gp_feat_calendar/routes/router_name.dart';
import 'package:gp_feat_member_picker/routes/pages.dart';
import 'package:gp_feat_task/routes/pages.dart';
import 'package:gp_feat_time_keeping/routes/pages.dart';
import 'package:timezone/data/latest.dart' as tz;

import '../main.dart';
import '../pages/pages.dart';
import '../utils/router_mapping.dart';
import 'controller.dart';
import 'widgets/dynamic_selector_page.dart';

// --------- FOR DEBUG ONLY --------- \
void main(List<String> args) async {
  await initApp();

  runApp(const GapoDebugPage());
}

Future initApp() async {
  WidgetsFlutterBinding.ensureInitialized();

  SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle(
    statusBarColor: GetPlatform.isAndroid
        ? GPColor.workPrimary
        : GPColor.bgPrimary, // status bar color
    statusBarIconBrightness: Brightness.dark,
    statusBarBrightness: Brightness.light,
    systemNavigationBarColor: GPColor.bgPrimary,
    systemNavigationBarIconBrightness: Brightness.dark,
  ));

  await GetStorage.init();
}

class GapoDebugPage extends StatelessWidget {
  const GapoDebugPage({
    this.runApplicationCallback,
    super.key,
  });

  final Function(Widget page)? runApplicationCallback;

  Environment defaultEnvironment(DebugPageController controller) =>
      Environment.values
          .byName(controller.defaultEnvironment() ?? Environment.staging.name);

  Locale defaultLocale(DebugPageController controller) =>
      appLocales.firstWhereOrNull(
          (e) => e.languageCode == controller.defaultLocale()) ??
      appLocales.first;

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      home: GestureDetector(
        onTap: () {
          FocusScope.of(context).requestFocus(FocusNode());
        },
        child: Container(
          padding: EdgeInsets.only(bottom: GetPlatform.isAndroid ? 24 : 0),
          color: GPColor.bgPrimary,
          child: SafeArea(
            top: false,
            child: GetBuilder(
              global: false,
              init: DebugPageController(),
              builder: (controller) {
                return Scaffold(
                  backgroundColor: GPColor.bgPrimary,
                  appBar: AppBar(
                    centerTitle: true,
                    backgroundColor: GPColor.workPrimary,
                    title: Text(
                      "Gapo Debug Page",
                      style: textStyle(GPTypography.headingLarge)
                          ?.mergeColor(GPColor.bgPrimary),
                    ),
                  ),
                  body: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        DebugAppDynamicSelector<GetPage<dynamic>>(
                          title: "Route:",
                          data: controller.pages,
                          currentSelected: controller.defaultRoute(),
                          itemTextBuilder: (item) => item.name,
                          onChanged: (value) {
                            controller.updateRoute(value);
                          },
                        ),
                        DebugAppDynamicSelector<Environment>(
                          title: "Env:",
                          data: Environment.values,
                          currentSelected: defaultEnvironment(controller),
                          itemTextBuilder: (item) => item.name,
                          onChanged: (value) {
                            controller.updateEnv(value);
                          },
                        ),
                        DebugAppDynamicSelector<Locale>(
                          title: "Locale:",
                          data: appLocales,
                          currentSelected: defaultLocale(controller),
                          itemTextBuilder: (item) => item.languageCode,
                          onChanged: (value) {
                            controller.updateLocale(value);
                          },
                        ),
                        const SizedBox(height: 12),
                        GPSearchBar(
                          isShowPrefixIcon: true,
                          colorBG: GPColor.linePrimary,
                          hintText:
                              "userId fixed, default is Signed-in account",
                          prefixAssetPath:
                              "assets/images/ic24-line15-person.png",
                          textEditingController: controller.inputUserId,
                          showClearBtn: true,
                        ),
                        const SizedBox(height: 12),
                        GPSearchBar(
                          isShowPrefixIcon: true,
                          colorBG: GPColor.linePrimary,
                          hintText: "jwt token fixed",
                          prefixAssetPath:
                              "assets/images/svg/ic24-fill-person-lockopen.svg",
                          textEditingController: controller.inputToken,
                          showClearBtn: true,
                        ),
                        const SizedBox(height: 12),
                        const _DotLineWidget(),
                        const SizedBox(height: 12),
                        GPSearchBar(
                          isShowPrefixIcon: true,
                          colorBG: GPColor.linePrimary,
                          hintText: "user name for sign in",
                          prefixAssetPath:
                              "assets/images/ic24-line15-person-checkmark.svg",
                          textEditingController: controller.inputAccountName,
                          showClearBtn: true,
                        ),
                        const SizedBox(height: 12),
                        GPSearchBar(
                          isShowPrefixIcon: true,
                          colorBG: GPColor.linePrimary,
                          hintText: "user password for sign in",
                          prefixAssetPath:
                              "assets/images/ic24-line15-shield.svg",
                          textEditingController:
                              controller.inputAccountPassword,
                          showClearBtn: true,
                          obscureText: true,
                        ),
                        const SizedBox(height: 12),
                        Text(
                          "If Jwt Token or (username and password) is empty\nAccount default will be:\<EMAIL>",
                          style: textStyle(GPTypography.bodyMedium),
                        )
                      ],
                    ).paddingAll(12),
                  ),
                  bottomNavigationBar: GPWorkButton(
                    title: "Run application",
                    onTap: () {
                      controller.setAppDebugData();
                      runGapoDebugApp(controller.debugAppModel);
                    },
                  ).paddingSymmetric(horizontal: 12),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  void runGapoDebugApp(DebugAppModel model) async {
    Future<void> initApp() async {
      FeatureFlag.enableHttpLog = true;

      Get.lazyPut(() => TagController());

      WidgetsFlutterBinding.ensureInitialized();

      env = model.debugAppConfig?.env ?? Environment.staging;

      Map<String, dynamic> tokenJson = {
        "userId": model.debugAppConfig?.userId,
        "workspaceId": "***************",
        "language": model.debugAppConfig?.locale?.languageCode,
      };

      if (model.needToLogin) {
        // login with an account
        Constants.updateTokenInfo(TokenInfo.fromJson(tokenJson));

        try {
          await TokenManager.initToken(
            email: model.debugAppConfig?.accountName,
            password: model.debugAppConfig?.accountPassword,
          );
        } catch (ex) {
          logDebug("initToken -> $ex");
        }
      } else if (model.hasAccessToken) {
        // use accessToken
        tokenJson.addAll({
          "accessToken": model.debugAppConfig?.accessToken,
        });
        final TokenInfo tokenInfo = TokenInfo.fromJson(tokenJson);
        Constants.updateTokenInfo(tokenInfo);

        await TokenManager.saveTokenInfo(tokenInfo);
        await TokenManager.saveLastRefreshToken("");
      } else {
        // login with default account
        Constants.updateTokenInfo(TokenInfo.fromJson(tokenJson));

        try {
          await TokenManager.initToken(
            email: model.loginEmailStr,
            password: model.loginPasswordStr,
          );
        } catch (ex) {
          logDebug("initToken -> $ex");
        }
      }
      // statusBar + system navigation
      SystemChrome.setSystemUIOverlayStyle(systemUiOverlayStyle);
      LogPoolManager.getInstance()?.maxCount = 1000;

      // Pages.pages.addAll(AlbumPages.pages);
      Pages.pages.addAll(TaskPages.pages);
      Pages.pages.addAll(CalendarPages.pages);
      Pages.pages.addAll(MemberPickerPages.pages);
      Pages.pages.addAll(TimeKeepingPages.pages);
      Pages.pages.addAll(AppPages.pages);

      RouterMapping.instance.routerEntryPointMapping.addAll(routerMap);

      Constants.updateThemeByFlavor();
    }

    await initApp();
    tz.initializeTimeZones();

    final page = LoadingApp(
      language: Constants.language(),
      routeName: model.route?.name ?? CalendarRouterName.calendar,
    );

    if (runApplicationCallback != null) {
      runApplicationCallback?.call(page);
    } else {
      runApp(page);
    }
  }
}

class _DotLineWidget extends StatelessWidget {
  const _DotLineWidget();

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        const Spacer(),
        Flexible(child: Divider(color: GPColor.linePrimary, thickness: 1.5)),
        Expanded(
            flex: 6,
            child: Text(
              "Use Token or Gapo 's account",
              textAlign: TextAlign.center,
              style: textStyle(GPTypography.bodyMedium)
                  ?.mergeColor(GPColor.contentSecondary),
            )),
        Flexible(child: Divider(color: GPColor.linePrimary, thickness: 1.5)),
        const Spacer(),
      ],
    );
  }
}
