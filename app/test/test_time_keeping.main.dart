// ignore_for_file: non_constant_identifier_names, depend_on_referenced_packages, unused_element

/*
 * Created Date: Wednesday, 15th May 2024, 15:52:09
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Thursday, 16th May 2024 09:00:32
 * Modified By: <PERSON><PERSON><PERSON>
 * -----
 * Copyright (c) 2021 - 2024 GAPO
 */

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:gp_core/core.dart';
import 'package:gp_feat_time_keeping/model/response/office_response.dart';

const _tagDebug = 'DEBUG_TIME_KEEPING';

void main() async {
  _distanceFromLocation();

  runApp(const SizedBox());
}

Map<String, dynamic> _prepareInput() {
  const ips = [
    '**************',
  ];

  final locations = <Map<String, dynamic>>[
    ..._1291608818,

    // ..._402400439,
    // ..._1103519930,
    // ..._669509139,
    // ..._446421544,

    // ..._276349049,
    // ..._1259756649,
    // ..._486850579,
  ];

  locations.sort((a, b) {
    final aDateTime = DateFormat('dd/MM/yyyy hh:mm:ss').parse(a['time']);
    final bDateTime = DateFormat('dd/MM/yyyy hh:mm:ss').parse(b['time']);

    return aDateTime.millisecondsSinceEpoch
        .compareTo(bDateTime.millisecondsSinceEpoch);
  });

  return {'locations': locations, 'ips': ips};
}

List<_DistanceOffice> _distanceFromLocation() {
  final Map<String, dynamic> inputData = _prepareInput();
  final List<Map<String, dynamic>> locations = inputData['locations'];
  final List<String> ips = inputData['ips'];

  final ret = <_DistanceOffice>[];

  final listResponse = _offices.map((e) => OfficeResponse.fromJson(e)).toList();

  for (var office in listResponse) {
    if (office.hasPosition) {
      for (var location in locations) {
        final double? lat = location['lat'];
        final double? long = location['long'];

        final double accuracy = (location['accuracy'] ?? 0.0).roundToDouble();

        if (lat != null && long != null) {
          double distanceInMeters = Geolocator.distanceBetween(
            lat,
            long,
            office.position!.lat!,
            office.position!.long!,
          ).roundToDouble();

          double distanceInMetersWithAccuracy = distanceInMeters;

          bool isInCheckInRadius = false;

          if (distanceInMetersWithAccuracy > accuracy) {
            distanceInMetersWithAccuracy -= accuracy;
          } else {
            // accuracy lớn hơn khoảng cách từ user tới văn phòng
            isInCheckInRadius = true;
            distanceInMetersWithAccuracy = 0;
          }

          ret.add(
            _DistanceOffice(
              input: location,
              office: office,
              distanceInMeters: distanceInMeters,
              distanceInMetersWithAccuracy: distanceInMetersWithAccuracy,
              accuracy: accuracy,
              isInCheckInRadius: !isInCheckInRadius
                  ? distanceInMeters <=
                          (office.position?.radius ?? 999999999) ||
                      distanceInMetersWithAccuracy <=
                          (office.position?.radius ?? 999999999)
                  : isInCheckInRadius,
              isContainsIps: office.ips is List?
                  ? office.ips?.any((element) => ips.contains(element)) ?? false
                  : false,
            ),
          );
        }
      }
    }
  }

  ret.sort((a, b) =>
      a.distanceInMeters.toInt().compareTo(b.distanceInMeters.toInt()));

  final result = ret.take(1 * locations.length).toList();

  for (var element in result) {
    log(
      '$element',
      // name: element.input['user_id'].toString(),
      name: '${element.distanceInMeters.toInt().toString()} m',
    );
  }

  return result;
}

class _DistanceOffice {
  final OfficeResponse office;
  final double distanceInMeters;
  final double distanceInMetersWithAccuracy;
  final double accuracy;
  final bool isInCheckInRadius;
  final bool isContainsIps;
  final Map<String, dynamic> input;

  _DistanceOffice({
    required this.office,
    required this.distanceInMeters,
    required this.distanceInMetersWithAccuracy,
    required this.accuracy,
    required this.isInCheckInRadius,
    required this.isContainsIps,
    required this.input,
  });

  bool get isCheckInSuccess => isInCheckInRadius || isContainsIps;

  @override
  String toString() {
    return '''
'''
        '''1. input:                    $input
'''
        '''2. distance in meters:       ${distanceInMeters.toInt()} meter 
'''
        '''3. distance with accuracy:   ${distanceInMetersWithAccuracy.toInt()} meter, accuracy: $accuracy 
'''
        '''4. Office name:              ${office.name} 
'''
        '''5. Office position:          ${office.position?.lat}, ${office.position?.long}
'''
        '''6. Office isInCheckInRadius: $isInCheckInRadius
'''
        '''7. isContainsIps:            $isContainsIps, officeIps: ${office.ips}''';
  }
}

/// **Mai Thị Thúy Thoa, ****************
var _1291608818 = [
  {
    'user_id': 1291608818,
    'lat': 10.765555524950262,
    'long': 106.70143013809172,
    'accuracy': 186,
    'time': '27/05/2024 11:28:48',
  },
  {
    'user_id': 1291608818,
    'lat': 10.7659912109375,
    'long': 106.7007917461048,
    'accuracy': 205.99317490545363,
    'time': '27/05/2024 11:01:32',
  },
  {
    'user_id': 1291608818,
    'lat': 10.765884399414062,
    'long': 106.70096618785304,
    'accuracy': 214,
    'time': '27/05/2024 11:01:01',
  },
  {
    'user_id': 1291608818,
    'lat': 10.76677289567126,
    'long': 106.69910767489785,
    'accuracy': 220,
    'time': '27/05/2024 10:01:51',
  },
  {
    'user_id': 1291608818,
    'lat': 10.766082763671877,
    'long': 106.7004112426134,
    'accuracy': 178,
    'time': '27/05/2024 09:14:20',
  },
];

/// **Nguyễn Thái Phúc, ip ****************
var _276349049 = [
  {
    'user_id': 276349049,
    'lat': 10.76516237681354,
    'long': 106.70248126370294,
    'time': '23/05/2024 07:51:06',
    'success': true,
  },
  {
    'user_id': 276349049,
    'lat': 10.766995037884826,
    'long': 106.69881713669676,
    'time': '24/05/2024 09:29:12',
  },
  {
    'user_id': 276349049,
    'lat': 10.765566935898319,
    'long': 106.70135800418196,
    'time': '24/05/2024 09:28:40',
  },
  {
    'user_id': 276349049,
    'lat': 10.765356611218415,
    'long': 106.70176595899117,
    'time': '24/05/2024 09:28:23',
  },
  {
    'user_id': 276349049,
    'lat': 10.766305480926754,
    'long': 106.70003210425209,
    'time': '24/05/2024 08:56:32',
  }
];

/// **Lê Hoàng Triều, ip **************, iPhone 15 Pro Max**
var _402400439 = [
  {
    'user_id': 402400439,
    'lat': 10.76592254638672,
    'long': 106.7008927581777,
    'time': '27/05/2024 13:22:45',
    'accuracy': 195.94357318552113,
  },
];

/// **Lê Thị Thúy, ip ***************
var _446421544 = [
  {
    'user_id': 446421544,
    'lat': 10.765510559082031,
    'long': 106.70150403603824,
    'time': '27/05/2024 10:08:40',
    'accuracy': 176,
  },
  {
    'user_id': 446421544,
    'lat': 10.765289306640623,
    'long': 106.70184205792955,
    'time': '27/05/2024 09:43:16',
    'accuracy': 140.69945373524016,
  },
  {
    'user_id': 446421544,
    'lat': 10.766090393066406,
    'long': 106.70043314951953,
    'time': '27/05/2024 09:26:17',
    'accuracy': 176,
  },
  {
    'user_id': 446421544,
    'lat': 10.766159057617188,
    'long': 106.7003064784496,
    'time': '27/05/2024 09:07:24',
    'accuracy': 176,
  },
  {
    'user_id': 446421544,
    'lat': 10.766159700492947,
    'long': 106.7003064784496,
    'time': '27/05/2024 09:07:22',
    'accuracy': 176,
  },
];

/// **Nguyễn Trần Li Li, ip ***************
var _669509139 = [
  {
    'user_id': 669509139,
    'lat': 10.766342163085938,
    'long': 106.70006459483632,
    'time': '27/05/2024 08:27:42',
    'accuracy': 208.6403906573951,
  },
  {
    'user_id': 669509139,
    'lat': 10.764634954031663,
    'long': 106.70280287129631,
    'time': '27/05/2024 08:27:15',
    'accuracy': 35,
  },
];

/// **Nguyễn Minh Hiền, ip **************, iPhone 15 Plus**
var _1103519930 = [
  {
    'user_id': 1103519930,
    'lat': 10.766265869140623,
    'long': 106.70026934434016,
    'time': '27/05/2024 08:57:08',
    'accuracy': 233.97821706391684,
  },
  {
    'user_id': 1103519930,
    'lat': 10.765403747558594,
    'long': 106.70191082716198,
    'time': '27/05/2024 08:54:09',
    'accuracy': 113.94580461784496,
  },
  {
    'user_id': 1103519930,
    'lat': 10.765426635742188,
    'long': 106.70186872525022,
    'time': '27/05/2024 08:53:46',
    'accuracy': 113.9457616208588,
  },
  {
    'user_id': 1103519930,
    'lat': 10.766204833984377,
    'long': 106.70041099007582,
    'time': '27/05/2024 08:53:02',
    'accuracy': 247,
  },
  {
    'user_id': 1103519930,
    'lat': 10.765731811523438,
    'long': 106.70126679800732,
    'time': '27/05/2024 08:52:38',
    'accuracy': 212.62210493826817,
  },
  {
    'user_id': 1103519930,
    'lat': 10.766151428222656,
    'long': 106.7004599220176,
    'time': '27/05/2024 08:51:45',
    'accuracy': 247,
  },
  {
    'user_id': 1103519930,
    'lat': 10.765480041503906,
    'long': 106.70178330513266,
    'time': '27/05/2024 08:50:20',
    'accuracy': 155.246436883256,
  },
  {
    'user_id': 1103519930,
    'lat': 10.76569366455078,
    'long': 106.70135355841776,
    'time': '27/05/2024 08:44:15',
    'accuracy': 177.13336442801753,
  },
  {
    'user_id': 1103519930,
    'lat': 10.766647338867188,
    'long': 106.69954831378472,
    'time': '27/05/2024 08:39:16',
    'accuracy': 148,
  },
  {
    'user_id': 1103519930,
    'lat': 10.765983581542969,
    'long': 106.7007893993058,
    'time': '27/05/2024 08:37:07',
    'accuracy': 230.09330501569,
  },
  {
    'user_id': 1103519930,
    'lat': 10.765373229980469,
    'long': 106.70200093345846,
    'time': '27/05/2024 08:35:02',
    'accuracy': 115.81687919371292,
  },
  {
    'user_id': 1103519930,
    'lat': 10.765960693359377,
    'long': 106.70082681948033,
    'time': '27/05/2024 08:34:17',
    'accuracy': 225.9039493144083,
  },
  {
    'user_id': 1103519930,
    'lat': 10.766250610351562,
    'long': 106.70028115043868,
    'time': '27/05/2024 08:34:06',
    'accuracy': 247,
  },
  {
    'user_id': 1103519930,
    'lat': 10.766098022460938,
    'long': 106.70057911613144,
    'time': '27/05/2024 08:28:02',
    'accuracy': 205.64366900178476,
  },
  {
    'user_id': 1103519930,
    'lat': 10.766448974609377,
    'long': 106.69984463633736,
    'time': '27/05/2024 08:25:01',
    'accuracy': 195.0000011041399,
  },
];

/// **Nguyễn Anh Thư, ip **************, iPhone 13 Pro**
var _1333286337 = [
  {
    'user_id': 1333286337,
    'lat': 10.432109832763672,
    'long': 12.997162818908691,
    'time': '27/05/2024 09:31:12',
    'accuracy': 189.7751691672148,
  },
];

/// **Trịnh Thị Thiên Nga, ip **************, iPhone XS Max**
var _1259756649 = [
  {
    'user_id': 1259756649,
    'lat': 10.765274047851562,
    'long': 106.7021577007341,
    'accuracy': 102.6563104550257,
    'time': '27/05/2024 07:51:41',
  },
  {
    'user_id': 1259756649,
    'lat': 10.766048136803226,
    'long': 106.70077080025465,
    'accuracy': 97.19307184395024,
    'time': '24/05/2024 07:51:35',
  },
  {
    'user_id': 1259756649,
    'lat': 10.766541763439827,
    'long': 106.69971972326269,
    'time': '22/05/2024 10:58:34',
  },
  {
    'user_id': 1259756649,
    'lat': 10.7665556909487,
    'long': 106.69968992823884,
    'time': '24/05/2024 10:58:33',
  },
  {
    'user_id': 1259756649,
    'lat': 10.767166551664786,
    'long': 106.69869254020023,
    'time': '22/05/2024 08:00:24',
    'success': true,
  },
  {
    'user_id': 1259756649,
    'lat': 10.765211111204248,
    'long': 106.70230644245744,
    'time': '21/05/2024 17:37:07',
    'success': true,
  },
  {
    'user_id': 1259756649,
    'lat': 10.766473076421914,
    'long': 106.6999771299384,
    'time': '21/05/2024 17:36:12',
  },
  {
    'user_id': 1259756649,
    'lat': 10.765646697959296,
    'long': 106.7015050454903,
    'time': '21/05/2024 17:35:46',
  },
  {
    'user_id': 1259756649,
    'lat': 10.76710569425145,
    'long': 106.69873151777993,
    'time': '20/05/2024 17:35:18',
    'success': true,
  },
];

/// **Lê Anh Thái, ip ****************
var _486850579 = [
  {
    'user_id': 486850579,
    'lat': 10.76600724646729,
    'long': 106.7006618114777,
    'time': '23/05/2024 16:01:17',
  },
  {
    'user_id': 486850579,
    'lat': 10.767033951988353,
    'long': 106.69882107956117,
    'time': '16/05/2024 10:35:56',
  },
  {
    'user_id': 486850579,
    'lat': 10.767138765480583,
    'long': 106.69861512326379,
    'time': '16/05/2024 10:35:57',
    'success': true,
  },
  {
    'user_id': 486850579,
    'lat': 10.766893698819443,
    'long': 106.69904819292458,
    'time': '16/05/2024 09:09:02',
  },
  {
    'user_id': 486850579,
    'lat': 10.766506919968798,
    'long': 106.69986068371429,
    'time': '13/05/2024 11:35:16',
  },
];

/// **DGW offices**
var _offices = [
  {
    "_id": "6522ce602f5c99c6b603b9f5",
    "check_type": "1",
    "created_at": "1669368305",
    "ips": [],
    "is_deleted": "true",
    "name": "DGW_Văn Phòng Nguyễn Thái Bình",
    "position": {
      "lat": 10.767463985000063,
      "long": 106.69791105900003,
      "mode": 1,
      "name": "195-197 Nguyễn Thái Bình, Nguyễn Thái Bình, Quận 1, Hồ Chí Minh",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1715764578",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "6522ce602f5c99c6b603b9fa",
    "check_type": "2",
    "created_at": "1669608794",
    "ips": ["**************"],
    "is_deleted": false,
    "name": "DCare_Hồ Xuân Hương",
    "position": {
      "lat": 10.7759708,
      "long": 106.6869826,
      "mode": 1,
      "name": "65 Hồ Xuân Hương, Phường 6, Quận 3, Hồ Chí Minh",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1704290507",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "6522ce602f5c99c6b603b9fb",
    "check_type": "2",
    "created_at": "1669608904",
    "ips": ["*************"],
    "is_deleted": false,
    "name": "DCare_ESC",
    "position": {
      "lat": 10.770090421000077,
      "long": 106.70160565300006,
      "mode": 1,
      "name": "68 Nam Kỳ Khởi Nghĩa, Nguyễn Thái Bình, Quận 1, Hồ Chí Minh",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1704290530",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "6522ce602f5c99c6b603b9fc",
    "check_type": "2",
    "created_at": "1669609439",
    "ips": ["**************", "*************"],
    "is_deleted": false,
    "name": "DGW_Kho Quận 12_QL1A",
    "position": {
      "lat": 10.826625052675713,
      "long": 106.60599589347841,
      "mode": 1,
      "name":
          "Công ty TNHH May Trí Đạt, 11A Hẻm 2977 Quốc Lộ 1A, Tân Thới Nhất, Quận 12, Hồ Chí Minh",
      "radius": 200
    },
    "require_upload_image": false,
    "updated_at": "1703825106",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "6522ce602f5c99c6b603b9fd",
    "check_type": "1",
    "created_at": "1669609482",
    "ips": ["************", "**************", "************"],
    "is_deleted": false,
    "name": "DGW_Kho Quận 12",
    "position": {
      "lat": 10.8246103,
      "long": 106.6082318,
      "mode": 1,
      "name":
          "6 Đường Tân Thới Nhất 8, Tân Thới Nhất, Quận 12, Thành phố Hồ Chí Minh",
      "radius": 200
    },
    "require_upload_image": false,
    "updated_at": "1703833219",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "6522ce602f5c99c6b603b9fe",
    "check_type": "2",
    "created_at": "1669609520",
    "ips": ["*************"],
    "is_deleted": false,
    "name": "MiStore_Tôn Thất Tùng",
    "position": {
      "lat": 10.7701586,
      "long": 106.6882704,
      "mode": 1,
      "name": "7 Tôn Thất Tùng, Phạm Ngũ Lão, Quận 1, Thành phố Hồ Chí Minh",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1704290632",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "6522ce602f5c99c6b603b9ff",
    "check_type": "2",
    "created_at": "1669610197",
    "ips": ["**************"],
    "is_deleted": false,
    "name": "Dcare_HN",
    "position": {
      "lat": 21.020138926000072,
      "long": 105.82202027100004,
      "mode": 2,
      "name":
          "106A ngõ 34 Hoàng Cầu, Phường Ô Chợ Dừa, Quận Đống Đa, Thành phố Hà Nội",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1704290742",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "6522ce602f5c99c6b603ba00",
    "check_type": "2",
    "created_at": "1669610241",
    "ips": ["*************"],
    "is_deleted": false,
    "name": "DGW_Văn Phòng Hà Nội",
    "position": {
      "lat": 21.02339343700004,
      "long": 105.80974429000008,
      "mode": 1,
      "name":
          "TNR Tower - Vinhomes Nguyễn Chí Thanh, 54A Nguyễn Chí Thanh, Láng Thượng, Đống Đa, Hà Nội",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1704290768",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "6522ce602f5c99c6b603ba01",
    "check_type": "2",
    "created_at": "1669610293",
    "ips": ["*************"],
    "is_deleted": false,
    "name": "DGW_Văn Phòng Đà Nẵng",
    "position": {
      "lat": 16.06999474959582,
      "long": 108.19160641641889,
      "mode": 1,
      "name": "36 Hà Huy Tập, Thanh Khê, Đà Nẵng",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1713846284",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "6522ce602f5c99c6b603ba02",
    "check_type": "2",
    "created_at": "1669611479",
    "ips": ["**************"],
    "is_deleted": false,
    "name": "DGW_Kho Đông Anh HN",
    "position": {
      "lat": 21.1277759,
      "long": 105.7903015,
      "mode": 1,
      "name": "TRUNG TÂM LOGISTICS HNT ĐÔNG ANH, Kim Nỗ, Đông Anh, Hà Nội",
      "radius": 300
    },
    "require_upload_image": false,
    "updated_at": "1704290837",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "6522ce602f5c99c6b603bcd0",
    "check_type": "2",
    "created_at": "1676359755",
    "ips": ["************"],
    "is_deleted": false,
    "name": "DGW_Kho Quận 10",
    "position": {
      "lat": 10.781085,
      "long": 106.6656176,
      "mode": 1,
      "name": "92 Tam Đảo, Cư xá Bắc Hải, Phường 15, Quận 10, Hồ Chí Minh",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1704290860",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "6522ce602f5c99c6b603bcd1",
    "check_type": "1",
    "created_at": "1676359805",
    "ips": "null",
    "is_deleted": "true",
    "name": "MiStore_Crescent Mall",
    "position": {
      "lat": 10.728694344000075,
      "long": 106.71850147100008,
      "mode": 1,
      "name":
          "Miniso, TTTM Crescent Mall, 101 Tôn Dật Tiên, Tân Phú, Quận 7, Hồ Chí Minh",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1704254231",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "6522ce602f5c99c6b603bcd2",
    "check_type": "1",
    "created_at": "1676359895",
    "ips": [],
    "is_deleted": "true",
    "name": "Mizone_Vạn Hạnh",
    "position": {
      "lat": 10.7705861,
      "long": 106.6694828,
      "mode": 1,
      "name":
          "Van Hanh Mall, 11 Đường Sư Vạn Hạnh, Phường 10 (Quận 10), Quận 10, Thành phố Hồ Chí Minh",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1704254244",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "6522ce602f5c99c6b603bcd3",
    "check_type": "2",
    "created_at": "1676359992",
    "ips": ["*************", "************"],
    "is_deleted": false,
    "name": "DGW_Kho Bình Dương",
    "position": {
      "lat": 10.9218856,
      "long": 106.7772143,
      "mode": 1,
      "name": "ECP Tan Dong Hiep B, phường Tân Đông Hiệp, Dĩ An, Bình Dương",
      "radius": 300
    },
    "require_upload_image": false,
    "updated_at": "1704290890",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "6522ce602f5c99c6b603bcd4",
    "check_type": "1",
    "created_at": "1676360110",
    "ips": "null",
    "is_deleted": "true",
    "name": "SPS 203 Nguyễn Thái Sơn",
    "position": {
      "lat": 10.8249696,
      "long": 106.6872836,
      "mode": 1,
      "name":
          "203 Nguyễn Thái Sơn, Phường 7, quận Gò Vấp, Thành phố Hồ Chí Minh",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1704254305",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "6522ce602f5c99c6b603bcd5",
    "check_type": "1",
    "created_at": "1676360159",
    "ips": "null",
    "is_deleted": "true",
    "name": "SPS 29 Âu Cơ",
    "position": {
      "lat": 10.769862640000042,
      "long": 106.65127924000006,
      "mode": 1,
      "name": "29 Âu Cơ, Phường 14, Quận 11, Hồ Chí Minh",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1704254308",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "6522ce602f5c99c6b603bcd6",
    "check_type": "1",
    "created_at": "1676360183",
    "ips": "null",
    "is_deleted": "true",
    "name": "SPS 694 Tỉnh Lộ 10",
    "position": {
      "lat": 10.7583517,
      "long": 106.6127252,
      "mode": 1,
      "name": "694 Tỉnh lộ 10, Bình Trị Đông, Bình Tân, Thành phố Hồ Chí Minh",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1704254311",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "6522ce602f5c99c6b603bcd7",
    "check_type": "1",
    "created_at": "1676360404",
    "ips": "null",
    "is_deleted": "true",
    "name": "SPS 400 Quốc Lộ 50",
    "position": {
      "lat": 10.7304143,
      "long": 106.6561781,
      "mode": 1,
      "name": "3 QL50, Bình Hưng, Bình Chánh, Thành phố Hồ Chí Minh",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1704254315",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "6522ce602f5c99c6b603bd0c",
    "check_type": "1",
    "created_at": "1677807058",
    "ips": [],
    "is_deleted": false,
    "name": "VP_Whirlpool",
    "position": {
      "lat": 10.755775907294549,
      "long": 106.67372167110445,
      "mode": 1,
      "name": "370 Đường Nguyễn Trãi, phường 3, Quận 5, Hồ Chí Minh",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1700043173",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "6522ce602f5c99c6b603bea1",
    "check_type": "2",
    "created_at": "1683170616",
    "ips": ["*************"],
    "is_deleted": false,
    "name": "Xiaomi_ESC_ĐN",
    "position": {
      "lat": 16.06055447425959,
      "long": 108.2141018642091,
      "mode": 1,
      "name": "100 Nguyễn Văn Linh, Nam Dương, Hải Châu, Đà Nẵng",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1713843086",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "656c84498ad2e5af07a93158",
    "check_type": "2",
    "created_at": "1701610569",
    "ips": ["*************"],
    "is_deleted": false,
    "name": "Dcare ESC Hải Phòng",
    "position": {
      "lat": 20.84124724000003,
      "long": 106.66737612300005,
      "mode": 2,
      "name":
          "293 Trần Nguyên Hãn, Phường Nghĩa Xá, Quận Lê Chân, Thành phố Hải Phòng",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1704291086",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "656c85f38ad2e5af07a9315a",
    "check_type": "2",
    "created_at": "1701610995",
    "ips": ["**************"],
    "is_deleted": false,
    "name": "Dcare ESC Đà Lạt",
    "position": {
      "lat": 11.94272703289218,
      "long": 108.43153953552248,
      "mode": 2,
      "name": "3 Đường Hai Bà Trưng, Đà Lạt, Lâm Đồng",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1704291112",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "65951728033b5fb195c15e25",
    "check_type": "1",
    "created_at": "1704269608",
    "ips": [],
    "is_deleted": false,
    "name": "VMN CN Cống Quỳnh",
    "position": {
      "lat": 10.765772925000022,
      "long": 106.68162173200005,
      "mode": 2,
      "name": "520 Nguyễn Thị Minh Khai, Phường 2, Quận 3, Hồ Chí Minh",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1704269608",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "659517a6033b5fb195c15e29",
    "check_type": "1",
    "created_at": "1704269734",
    "ips": [],
    "is_deleted": false,
    "name": "VMN CN Lý Thường Kiệt",
    "position": {
      "lat": 10.76410071500004,
      "long": 106.66000024900006,
      "mode": 2,
      "name": "146 Lý Thường Kiệt, Phường 14, Quận 10, Hồ Chí Minh",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1704269734",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "6595184d033b5fb195c15e2b",
    "check_type": "1",
    "created_at": "1704269901",
    "ips": [],
    "is_deleted": false,
    "name": "VMN CN Đinh Tiên Hoàng",
    "position": {
      "lat": 10.797297,
      "long": 106.696399,
      "mode": 2,
      "name": "110K Lê Văn Duyệt, Phường 1, Bình Thạnh, Hồ Chí Minh 70000",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1704269901",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "6595187b033b5fb195c15e2f",
    "check_type": "1",
    "created_at": "1704269947",
    "ips": [],
    "is_deleted": false,
    "name": "VMN CN Nguyễn Văn Nghi",
    "position": {
      "lat": 10.82312961200006,
      "long": 106.68794909900004,
      "mode": 2,
      "name": "104 Nguyễn Văn Nghi, Phường 5, Gò Vấp, Hồ Chí Minh",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1704269947",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "659518c8033b5fb195c15e33",
    "check_type": "1",
    "created_at": "1704270024",
    "ips": [],
    "is_deleted": false,
    "name": "VMN CN Phan Đăng Lưu",
    "position": {
      "lat": 10.801744124584811,
      "long": 106.68279826641084,
      "mode": 2,
      "name":
          "TienNgay.vn, 138 Phan Đăng Lưu, Phường 3, Phú Nhuận, Hồ Chí Minh",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1704270024",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "65951983033b5fb195c15e37",
    "check_type": "1",
    "created_at": "1704270211",
    "ips": [],
    "is_deleted": false,
    "name": "VMN CN Nguyễn Gia Trí",
    "position": {
      "lat": 10.806694536000066,
      "long": 106.71622029300005,
      "mode": 2,
      "name": "186 Nguyễn Gia Trí, Phường 25, Bình Thạnh, Hồ Chí Minh",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1704270211",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "659519b2033b5fb195c15e3d",
    "check_type": "1",
    "created_at": "1704270258",
    "ips": [],
    "is_deleted": false,
    "name": "VMN CN Khánh Hội",
    "position": {
      "lat": 10.759182904000056,
      "long": 106.69897659400004,
      "mode": 2,
      "name":
          "Cầm đồ Viet Money, 234B Khánh Hội, Phường 6, Quận 4, Hồ Chí Minh",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1704270258",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "659519d8033b5fb195c15e3f",
    "check_type": "1",
    "created_at": "1704270296",
    "ips": [],
    "is_deleted": false,
    "name": "VMN CN Nguyễn Thị Thập",
    "position": {
      "lat": 10.738290162000055,
      "long": 106.71841108700005,
      "mode": 2,
      "name": "168C Nguyễn Thị Thập, Bình Thuận, Quận 7, Hồ Chí Minh",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1704270296",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "65951a92033b5fb195c15e43",
    "check_type": "1",
    "created_at": "1704270482",
    "ips": [],
    "is_deleted": false,
    "name": "VMN CN Quốc Lộ 50",
    "position": {
      "lat": 10.73377935600007,
      "long": 106.65599245400006,
      "mode": 2,
      "name": "296 5 Quốc Lộ 50, Phường 6, Quận 8, Hồ Chí Minh",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1704270482",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "65951ade033b5fb195c15ec8",
    "check_type": "1",
    "created_at": "1704270558",
    "ips": [],
    "is_deleted": false,
    "name": "VMN CN Tân Phú",
    "position": {
      "lat": 10.79579835900006,
      "long": 106.62613762200004,
      "mode": 2,
      "name": "Cầm đồ Viet Money, 79A Gò Dầu, Tân Quý, Tân Phú, Hồ Chí Minh",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1704270558",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "65951afc033b5fb195c15eca",
    "check_type": "1",
    "created_at": "1704270588",
    "ips": [],
    "is_deleted": false,
    "name": "VMN CN Hoàng Hoa Thám",
    "position": {
      "lat": 10.801066391000063,
      "long": 106.64729941700006,
      "mode": 2,
      "name":
          "Cầm đồ Viet Money, 89 Hoàng Hoa Thám, Phường 13, Tân Bình, Hồ Chí Minh",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1704270588",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "65951b31033b5fb195c15ecc",
    "check_type": "1",
    "created_at": "1704270641",
    "ips": [],
    "is_deleted": false,
    "name": "VMN CN Lạc Long Quân",
    "position": {
      "lat": 10.76653896700003,
      "long": 106.64215277100004,
      "mode": 2,
      "name": "327 Lạc Long Quân, Phường 3, Quận 11, HCM",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1704270641",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "65951b75033b5fb195c15ed0",
    "check_type": "1",
    "created_at": "1704270709",
    "ips": [],
    "is_deleted": false,
    "name": "VMN CN Đỗ Xuân Hợp",
    "position": {
      "lat": 10.83083868400007,
      "long": 106.76781586100009,
      "mode": 2,
      "name": "112 Đỗ Xuân Hợp, Phước Long A, Thủ Đức, Hồ Chí Minh",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1704270709",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "65951c6b033b5fb195c15eda",
    "check_type": "1",
    "created_at": "1704270955",
    "ips": [],
    "is_deleted": false,
    "name": "VMN CN Thủ Dầu Một",
    "position": {
      "lat": 10.972686218611637,
      "long": 106.67117893695833,
      "mode": 2,
      "name": "261 Đại Lộ Bình Dương, Chánh Nghĩa, Thủ Dầu Một, Bình Dương",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1704270955",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "65951ce0033b5fb195c15ee0",
    "check_type": "1",
    "created_at": "1704271072",
    "ips": [],
    "is_deleted": false,
    "name": "VMN CN Nguyễn Ảnh Thủ",
    "position": {
      "lat": 10.876832679000074,
      "long": 106.64307752300004,
      "mode": 2,
      "name": "38A Nguyễn Ảnh Thủ, Hiệp Thành, Quận 12, Hồ Chí Minh",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1704271072",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "65952eff033b5fb195c15f6b",
    "check_type": "1",
    "created_at": "1704275711",
    "ips": [],
    "is_deleted": false,
    "name": "VMN ĐĐ 30/04 Đồng Nai",
    "position": {
      "lat": 10.948639105498827,
      "long": 106.82018101215364,
      "mode": 2,
      "name": "349 30 Tháng 4, Thanh Bình, Biên Hòa, Đồng Nai",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1704275711",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  },
  {
    "_id": "6631fbb78ad5dd20d83fdbef",
    "check_type": "2",
    "created_at": "1714551735",
    "ips": ["**************", "*************"],
    "is_deleted": false,
    "name": "DGW_Văn Phòng ETOWN",
    "position": {
      "lat": 10.764582239004772,
      "long": 106.70294165611269,
      "mode": 2,
      "name": "Etown Central, 11 Đoàn Văn Bơ, P. 12, Q. 4, Hồ Chí Minh",
      "radius": 100
    },
    "require_upload_image": false,
    "updated_at": "1714551735",
    "updater_id": "266054877",
    "workspace_id": "582995832907655"
  }
];
