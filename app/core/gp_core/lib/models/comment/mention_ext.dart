import 'comment.dart';

mixin MentionExt {
  // string hiển thị trong comment, sử dụng tại client, dùng để compare khi xoá 1 mention
  String? displayName;
}

extension CommentExt on Comment {
  String getText() {
    return text ?? "";
  }

  void updateIfNeeded(String textDisplay) {
    if (mentions != null && mentions!.isNotEmpty) {
      final String newText = getText();
      if (newText != textDisplay) {
        int offset = textDisplay.indexOf(newText);

        for (var mention in mentions!) {
          mention.offset -= offset;
          mention.length = mention.offset + (mention.displayName ?? "").length;
        }
      }
    }
  }
}

String getCommentStr(String? input) {
  return (input ?? "")
      .trim()
      .split(RegExp(r'(?:
?
|
)'))
      .where((s) => s.trim().isNotEmpty)
      .join('
');
}
