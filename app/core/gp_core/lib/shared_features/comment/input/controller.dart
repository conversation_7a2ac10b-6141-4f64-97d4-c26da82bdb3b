import 'dart:async';

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/models/comment/comment.dart';

import 'social/social.dart';
import 'social/util/social_span.dart';
import 'sub_controller.dart';
import 'vars.dart';

export 'vars.dart';

class OnCommentCallback {
  // callback tạo mới comment từ task
  final Comment Function() onNewCommentLocal;

  // callback khi comment có media
  final OnMediasCallback? onMediasCallback;

  // callback khi edit comment
  final Future Function(String? id, Comment comment) onEditComment;

  // callback khi tạo comment
  final Future Function(bool hasMedias, Comment comment) onCreateComment;

  // call sau cùng, khi toàn bộ function đã chạy xong
  final Function() onFinally;

  OnCommentCallback({
    required this.onNewCommentLocal,
    this.onMediasCallback,
    required this.onEditComment,
    required this.onCreateComment,
    required this.onFinally,
  });
}

class OnMediasCallback {
  Function(Comment, bool isResendComment, bool isEditingComment)
      onCommentHasMedias;

  Function() onCommentUploadMediasError;

  OnMediasCallback({
    required this.onCommentHasMedias,
    required this.onCommentUploadMediasError,
  });
}

class OnCommentItemCallback {
  final double Function() getScrollOffset;

  final Future Function(Comment comment) afterClickDeleteComment;

  OnCommentItemCallback({
    required this.getScrollOffset,
    required this.afterClickDeleteComment,
  });
}

enum CreateCommentMode {
  create,
  edit,
  reply,
  delete,
}

class InputCommentController extends GetxController
    with InputCommentVars, SubControllerVars {
  /// hiển thị mentionList hay không, trigger khi regex match [SocialSpan.socialDetectPrefix]
  RxBool showMentionList = false.obs;

  bool inputHasMentionReg = false;

  /// mention hiển thị dạng overlay,
  /// [overLayBottom] là khoảng cách từ ô input comment + keyboard tới mention list
  RxDouble overLayBottom = 50.0.obs;

  RxString commentStr = "".obs;
  RxString commentStrSplitted = "".obs;

  // has text or image... or videos...
  bool get hasComment =>
      (commentStrSplitted.value.isNotEmpty || isChoosingMedias);

  bool get canComment => hasComment && isSendingComment.value == false;

  RxBool isKeyboardVisible = false.obs;
  RxBool isEmojiVisible = false.obs;

  // show editComment layout or not
  final RxBool isEditCommentVisible = false.obs;

  bool get isEditingComment => isEditCommentVisible.value;

  // comment for editting;
  late Comment eComment;

  CreateCommentMode createCommentMode = CreateCommentMode.create;

  String? idParent;

  /// show editCommentMedia layout or not,
  /// view start listening isChoosingMedias.value after creating [CommentMediasController]
  final RxBool isEditCommentMediaVisible = false.obs;

  RxBool isSendingComment = false.obs;

  bool isPickingMedia = false;

  bool get isChoosingMedias =>
      isEditCommentMediaVisible.value &&
      commentMediasController?.hasMedia == true;

  RxBool hasMedias = false.obs;

  double get overlayMaxHeight => isChoosingMedias ? 150 : 240;

  late StreamSubscription keyboardSubscription;

  final KeyboardVisibilityController keyboardVisibilityController =
      KeyboardVisibilityController();

  double scrollCurrentPositionPx = 0.0;

  final OnCommentItemCallback onCommentItemCallback;
  final OnCommentCallback onCommentCallback;

  InputCommentController({
    required this.onCommentItemCallback,
    required this.onCommentCallback,
  }) {
    // gọi trực tiếp trong constructor, tránh trường onInit của `InputCommentController` call sau allocate in memory
    initSubController();
  }

  @override
  void onInit() {
    textEditingController.addListener(() {
      commentStr.value = textEditingController.text;

      textEditingController.handleOnTextChanged(_onTextChanged);
    });

    commentStr.listen((p0) {
      commentStrSplitted.value = getCommentStr(p0);
    });

    KeyboardVisibilityController().onChange.listen((bool isKeyboardVisible) {
      this.isKeyboardVisible.value = isKeyboardVisible;

      if (isKeyboardVisible && isEmojiVisible.value) {
        isEmojiVisible.value = false;
      }
    });

    focusNode.addListener(() {
      if (!isEmojiVisible.value && keyboardVisibilityController.isVisible) {
        showMentionList.value = focusNode.hasFocus;
      }
    });

    keyboardSubscription =
        keyboardVisibilityController.onChange.listen((bool visible) {
      if (!visible) {
        unfocus();
        showMentionList.value = false;
      }
    });

    super.onInit();
  }

  @override
  void onClose() {
    keyboardSubscription.cancel();
    super.onClose();
  }

  void setMembersCanMention(List<Assignee> members) {
    mentionController.setAssignees(members);
  }

  Future<void> sendComment({Comment? comment}) async {
    if (!hasComment) return;

    if (!isSendingComment.value) {
      isSendingComment.value = true;
      // ignore: no_leading_underscores_for_local_identifiers
      Comment _comment;
      // comment sử dụng cho trường hợp gửi lại comment lỗi trước đó
      if (comment != null) {
        _comment = comment;
      } else {
        // _comment = Get.find<TaskCreateController>().newCommentLocal(
        //     commentStr.value,
        //     commentMediaLocals:
        //         commentMediasController?.commentMedias.toList());

        _comment = onCommentCallback.onNewCommentLocal();
      }

      // uploadMedias nếu cần
      List<Medias> commentMedias = [];

      final bool hasMedias = isChoosingMedias || _comment.hasMediaLocals;

      final String commentTextStr = textEditingController.text;

      if (hasMedias) {
        onCommentCallback.onMediasCallback?.onCommentHasMedias
            .call(_comment, hasMedias, isEditingComment);

        isEditCommentVisible.value = false;

        if (commentMediasController != null) {
          commentMedias = await commentMediasController!
              .uploadMedias(commentMediaLocals: _comment.commentMediaLocals);

          if (commentMedias.isEmpty) {
            // hiển thị view error khi upload lại ảnh
            // _isEditCommentVisible.value = true;
            isSendingComment.value = false;

            onCommentCallback.onMediasCallback?.onCommentUploadMediasError
                .call();
            return;
          }
        }

        _comment.medias = commentMedias;
      }
      // create or edit comment
      if (commentStr.value.isNotEmpty || commentMedias.isNotEmpty) {
        switch (createCommentMode) {
          case CreateCommentMode.edit:
            String? id = eComment.id;
            eComment = _comment;
            eComment.id = id;

            _comment.parentId = idParent;

            onCommentCallback.onEditComment(id, _comment);
            break;
          case CreateCommentMode.create:
          case CreateCommentMode.reply:
            _comment.updateIfNeeded(commentTextStr);

            await onCommentCallback.onCreateComment(hasMedias, _comment);
            break;
          case CreateCommentMode.delete:
            break;
        }

        exitEdittingComment();
        textEditingController.clear();
      }
      //reset idParent when send comment
      idParent = null;

      isSendingComment.value = false;

      onCommentCallback.onFinally();

      hideEmojiKeyboard();
      unfocus();
    }
  }

  void editComment(Comment comment) async {
    idParent = comment.parentId;
    eComment = comment;
    createCommentMode = CreateCommentMode.edit;
    textEditingController.mentions.clear();

    final String commentText = comment.getText();
    textEditingController.text = "$commentText ";

    // update medias
    if (comment.hasMedias) {
      initCommentMediasController();

      commentMediasController?.addMedias(comment.medias!);
    }

    List<Mentions> mentions = comment.mentions ?? [];
    if (mentions.isNotEmpty) {
      for (var mention in mentions) {
        final String newValue =
            commentText.substring(mention.offset, mention.length);

        mention.displayName = newValue;
      }

      textEditingController.mentions
        ..clear()
        ..addAll(mentions);
    }

    _preparedEditting();

    textEditingController.moveCursorToEnd();

    await Future.delayed(const Duration(milliseconds: 100));
    requestFocus();

    enterEdittingComment();
  }

  void replyComment(Comment comment) {
    if (comment.commentAs == null) return;

    createCommentMode = CreateCommentMode.reply;
    idParent = comment.parentId ?? comment.id;

    bool needAddMention = comment.commentAs!.authorId != Constants.userId();

    textEditingController.text = "";
    textEditingController.mentions.clear();

    if (needAddMention && comment.user != null) {
      String mentionDisplayName = comment.user!.displayName;

      const int offset = 0;
      final int length = mentionDisplayName.length;

      textEditingController.text = mentionDisplayName;

      textEditingController.replaceRange(
        mentionDisplayName,
        TextRange(start: offset, end: length),
        assignee: Assignee(
            id: int.parse(comment.user!.userId),
            displayName: mentionDisplayName),
      );
    } else {
      clearText();
    }

    _preparedEditting();

    exitEdittingComment();
  }

  void hideKeyboards() {
    hideEmojiKeyboard();
    unfocus();
  }

  void onEmojiTapped() async {
    unfocus();
    showMentionList.value = false;
    if (isEmojiVisible.value) {
      isEmojiVisible.value = false;
      await SystemChannels.textInput.invokeMethod('TextInput.show');
      requestFocus();
    } else if (isKeyboardVisible.value) {
      isEmojiVisible.value = true;
      await SystemChannels.textInput.invokeMethod('TextInput.hide');
    } else if (!isEmojiVisible.value && !isKeyboardVisible.value) {
      isEmojiVisible.value = true;
    }
  }

  void hideEmojiKeyboard() {
    isEmojiVisible.value = false;
  }

  void hideMentions() {
    FocusManager.instance.primaryFocus?.unfocus();
    showMentionList.value = false;
    unfocus();
  }

  void enterEdittingComment() {
    isEditCommentVisible.value = true;
  }

  void exitEdittingComment() {
    isEditCommentVisible.value = false;

    isEditCommentMediaVisible.value = false;

    clearMedias();

    showMentionList.value = false;

    createCommentMode = CreateCommentMode.create;

    hideKeyboards();
  }

  void onMentionTapped(Assignee assignee) {
    // '@Toàn', tap chọn Nguyễn Mạnh Toàn, hiển thị 'Nguyễn Mạnh Toàn'
    textEditingController.replaceRange(
      assignee.displayName,
      TextRange(start: lastDetection.start, end: lastDetection.end),
      assignee: assignee,
    );
  }

  void _onTextChanged(LengthMap? lengthMap) {
    if (lengthMap != null) {
      lastDetection = lengthMap;
      if (focusNode.hasFocus) {
        inputHasMentionReg = true;
        showMentionList.value = true;

        mentionController.searchStr.value =
            lengthMap.str.replaceAll(SocialSpan.socialDetectPrefix, "");
      }
    } else {
      inputHasMentionReg = false;
      showMentionList.value = false;
      mentionController.clear();
    }
  }

  Future _preparedEditting() async {
    requestFocus();

    await 300.milliseconds.delay();

    showMentionList.value = false;
  }

  // --------- COMMENT MEDIAS --------- \
  void clearMedias() {
    commentMediasController?.clearMedias();
  }

  Future pickMedia({bool allowMultiple = true}) async {
    Future delayPickMediaTimer() async {
      await 500.milliseconds.delay();
      isPickingMedia = false;
    }

    hideKeyboards();

    Popup.instance
        .showBottomSheet(FilePickerBottomSheet(onSelected: (fileType) async {
      var result =
          await GPPicker.instance.pick(fileType, allowMultiple: allowMultiple);
      SystemChrome.setSystemUIOverlayStyle(systemUiOverlayStyle);

      if (isPickingMedia) return;

      isPickingMedia = true;

      delayPickMediaTimer();

      unfocus();
      showMentionList.value = false;
      initCommentMediasController();

      // hide keyboard bên ios, nếu không đi từ native bị lỗi: bàn phím ko hiển thị được
      bool hasFocus = focusNode.hasFocus;
      if (hasFocus && GetPlatform.isIOS) {
        hideKeyboards();
      }

      if ((result?.filePickerResult?.files.length ?? 0) > 1) {
        Popup.instance.showAlert(
            title: LocaleKeys.task_mediaPicker_errorLimitTitle.tr,
            message: LocaleKeys.task_mediaPicker_errorLimitContent.tr);
        return;
      }

      // case chụp ảnh
      if (result?.gpXFiles != null) {
        final xfiles = result?.gpXFiles?.first.xFile;

        final size = await xfiles?.length();
        final bytes = await xfiles?.readAsBytes();

        final filePickerResult = FilePickerResult([
          PlatformFile(
              path: xfiles?.path ?? "",
              name: xfiles?.name ?? "",
              size: size ?? 0,
              bytes: bytes ?? Uint8List(0))
        ]);

        commentMediasController?.handlePickedFiles(
            GPFilePickerResult(filePickerResult: filePickerResult));
      } else {
        commentMediasController?.handlePickedFiles(result);
      }

      // hiển thị lại bàn phím
      if (hasFocus && GetPlatform.isIOS) {
        requestFocus();
      }
    }));
  }
}
