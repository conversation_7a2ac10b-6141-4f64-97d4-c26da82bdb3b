import 'package:gp_core/core.dart';
import 'package:gp_core/shared_features/custom_repeat/frequency.dart';

import 'iteration.dart';

enum RepeatEveryByModel {
  day,
  week,
  month,
  year,
}

extension RepeatEveryByModelExt on RepeatEveryByModel {
  String get value {
    switch (this) {
      case RepeatEveryByModel.day:
        return "day";
      case RepeatEveryByModel.week:
        return "week";
      case RepeatEveryByModel.month:
        return "month";
      case RepeatEveryByModel.year:
        return "year";
    }
  }

  String get displayName {
    switch (this) {
      case RepeatEveryByModel.day:
        return LocaleKeys.calendar_repeat_by_day.tr;
      case RepeatEveryByModel.week:
        return LocaleKeys.calendar_repeat_by_week.tr;
      case RepeatEveryByModel.month:
        return LocaleKeys.calendar_repeat_by_month.tr;
      case RepeatEveryByModel.year:
        return LocaleKeys.calendar_repeat_by_year.tr;
    }
  }

  String get rRuleString {
    switch (this) {
      case RepeatEveryByModel.day:
        return "DAILY";
      case RepeatEveryByModel.week:
        return "WEEKLY";
      case RepeatEveryByModel.month:
        return "MONTHLY";
      case RepeatEveryByModel.year:
        return "YEARLY";
    }
  }
}

/*
  Định fork từ https://github.com/JonasWanke/rrule
  nhưng project hơi big, và có quá nhiều thứ không cần thiết
*/

class RRuleModel {
  late String freq;
  late String interval;
  String? byDay;
  String? bySetPos;
  String? byMonth;
  String? byMonthDay;

  String? count;
  String? until;

  DateTime? startDate;
  DateTime? overOnADate;

  String rRule;

  int get intervalNumber => int.parse(interval);

  bool get hasByDay => byDay != null && byDay != "";
  bool get hasBySetPos => bySetPos != null && bySetPos != "";
  bool get hasByMonthDay => byMonthDay != null && byMonthDay != "";
  bool get hasCount => count != null && count != "";
  bool get hasUntil => until != null && until != "";

  bool get isYearly => freq == "YEARLY";
  bool get isMonthly => freq == "MONTHLY";
  bool get isWeekly => freq == "WEEKLY";
  bool get isDaily => freq == "DAILY";

  int get monthDayNumber => int.parse(byMonthDay ?? "0");
  int get monthNumber => int.parse(byMonth ?? "0");

  Frequency get frequency {
    if (isYearly) {
      return Frequency.yearly;
    } else if (isMonthly) {
      return Frequency.monthly;
    } else if (isWeekly) {
      return Frequency.weekly;
    } else {
      return Frequency.daily;
    }
  }

  Iterable<DateTime> getInstances({
    required DateTime start,
    DateTime? after,
    bool includeAfter = false,
    DateTime? before,
    bool includeBefore = false,
  }) {
    return getRecurrenceRuleInstances(
      this,
      start: startDate ?? DateTime.now(),
      after: after,
      includeAfter: includeAfter,
      before: before,
      includeBefore: includeBefore,
    );
  }

  String get _byDayStr => (byDay ?? "");
  bool get containsWeekday =>
      _byDayStr.contains("MO") &&
      _byDayStr.contains("TU") &&
      _byDayStr.contains("WE") &&
      _byDayStr.contains("TH") &&
      _byDayStr.contains("FR") &&
      !_byDayStr.contains("SA") &&
      !_byDayStr.contains("SU");

  List get jsDeekDay {
    if (!hasByDay) return [];
    return byDay!
        .split(",")
        .map((e) => e.toRepeatByWeekEnum().jsWeekDay)
        .toList();
  }

  List get weekDays {
    if (!hasByDay) return [];
    return byDay!
        .split(",")
        .map((e) => e.toRepeatByWeekEnum().weekDay)
        .toList();
  }

  RRuleModel(this.rRule, {this.startDate}) {
    _init();

    updateRRuleStr(startDate);
  }

  void _init() {
    List<String> splitteds = rRule.split(";");
    freq = splitteds
            .firstWhereOrNull((e) => e.startsWith("FREQ="))
            ?.replaceAll("FREQ=", "") ??
        '';

    if (splitteds.isEmpty) return;

    interval = splitteds
            .firstWhereOrNull((e) => e.startsWith("INTERVAL="))
            ?.replaceAll("INTERVAL=", "") ??
        "1";

    byDay = splitteds
        .firstWhereOrNull((e) => e.startsWith("BYDAY="))
        ?.replaceAll("BYDAY=", "");

    byMonth = splitteds
        .firstWhereOrNull((e) => e.startsWith("BYMONTH="))
        ?.replaceAll("BYMONTH=", "");

    byMonthDay = splitteds
        .firstWhereOrNull((e) => e.startsWith("BYMONTHDAY="))
        ?.replaceAll("BYMONTHDAY=", "");

    bySetPos = splitteds
        .firstWhereOrNull((e) => e.startsWith("BYSETPOS="))
        ?.replaceAll("BYSETPOS=", "");

    count = splitteds
        .firstWhereOrNull((e) => e.startsWith("COUNT="))
        ?.replaceAll("COUNT=", "");

    /*
      Logic BE: có case trả về đồng thời cả UNTIL= COUNT=
    */
    if (!hasCount) {
      until = splitteds
          .firstWhereOrNull((e) => e.startsWith("UNTIL="))
          ?.replaceAll("UNTIL=", "");
    } else {
      until = "";
      final String? fullUntil =
          splitteds.firstWhereOrNull((e) => e.startsWith("UNTIL="));
      rRule = rRule.replaceAll(fullUntil ?? "", "");
    }

    if (hasUntil) {
      overOnADate = DateTimeUtils.toIsoDateTime(until!);
    } else {
      // overOnADate = startDate?.add(const Duration(days: 30));
    }
  }

  void updateRRuleStr(DateTime? startDate) {
    this.startDate = startDate;
    if (isWeekly && hasByDay && startDate != null) {
      /*
        Nếu user chọn lặp lại 1 ngày trong tuần: uu tiên thời gian theo startDate:
        - Cập nhật lại weekDay nếu khác startDate.
        - VD: startDate là t4, user chọn lặp lại vào CN -> update lại byday=we
      */
      int selectedLength = byDay!.split(",").length;
      if (selectedLength == 1) {
        List<String> splitteds = rRule.split(";");
        String? oldByDay = splitteds
            .firstWhereOrNull((e) => e.startsWith("BYDAY="))
            ?.replaceAll("BYDAY=", "");
        byDay = startDate.weekDayStr;

        rRule = rRule.replaceAll("BYDAY=$oldByDay", "BYDAY=$byDay");
      }
    }
  }

  void updateUntil(DateTime endDateTime) {
    /*
      App đang ưu tiên rRule, nếu không có rRule sẽ lấy data lặp từ schedule
      -> có trường hợp người tham gia event, xoá event,
      lúc này data từ rRule sẽ không có until, chỉ có endDate trong schedule
    */

    List<String> splitteds = rRule.split(";");

    if (hasCount) {
      String? count = splitteds.firstWhereOrNull((e) => e.startsWith("COUNT="));
      if (count != "") {
        rRule = rRule.replaceAll(";$count", "");
      }
    }

    until = endDateTime.isoStr;
    if (hasUntil) {
      String? untils =
          splitteds.firstWhereOrNull((e) => e.startsWith("UNTIL="));
      if (untils != "") {
        rRule = rRule.replaceAll(";$untils", "");
      }
    }

    rRule += ";UNTIL=$until";
  }

  // CalendarEventScheduleType? getCest() {
  //   if (isDaily) {
  //     return CalendarEventScheduleType.daily;
  //   } else if (isWeekly) {
  //     return CalendarEventScheduleType.weekly;
  //   } else if (isMonthly) {
  //     return CalendarEventScheduleType.monthly;
  //   } else if (isYearly) {
  //     return CalendarEventScheduleType.yearly;
  //   }

  //   return null;
  // }

  // --------- COMMANDS --------- \

  // CalendarEventRepeatOptionEnum? getCeroEByRRule(DateTime dateTime) {
  //   /*
  //     Nếu rRule khác những rule cơ bản, lấy theo `getCeroE`
  //   */

  //   if (hasCount || hasUntil) {
  //     return CalendarEventRepeatOptionEnum.custom;
  //   }
  //   // Lặp lại 1 ngày 1 lần
  //   bool isDefaultInterval = int.parse(interval) == 1;

  //   // Lặp lại 1 tuần 1 lần
  //   bool isDefaultDailyRule = isDaily && isDefaultInterval;

  //   // Lặp lại vào [Thứ X] hàng tuần
  //   bool isDefaultWeeklyRule =
  //       isWeekly && byDay == dateTime.weekDayStr && isDefaultInterval;

  //   // Lặp lại vào ngày [X] hàng tháng
  //   bool isDefaultMonthlyRule =
  //       isMonthly && byMonthDay == "${dateTime.day}" && isDefaultInterval;

  //   // Lặp lại vào các ngày trong tuần (từ thứ 2 đến thứ 6)
  //   bool isDefaultWeeklyWeekdayRule =
  //       isWeekly && _containsWeekday && isDefaultInterval;

  //   // Lặp lại vào [Thứ X] [tuần thứ Y] hàng tháng
  //   final weekPosition = dateTime.weekOfMonth;
  //   bool isDefaultMonthlyByAMonthdayRule = isMonthly &&
  //       bySetPos == "$weekPosition" &&
  //       byDay == dateTime.weekDayStr &&
  //       isDefaultInterval;

  //   if (!isDefaultDailyRule &&
  //       !isDefaultWeeklyRule &&
  //       !isDefaultMonthlyRule &&
  //       !isDefaultWeeklyWeekdayRule &&
  //       !isDefaultMonthlyByAMonthdayRule) {
  //     return CalendarEventRepeatOptionEnum.custom;
  //   }

  //   return null;
  // }

  String getDisplayName(DateTime startDate,
      {Duration? defaultAddedDuration, bool hideStartTime = false}) {
    defaultAddedDuration ??= DateTime.now().timeZoneOffset;
    /*
      Lấy ra string từ 1 recurrence rule có sẵn.
      VD: FREQ=DAILY;INTERVAL=1;COUNT=1 -> Lặp lại hàng ngày; kết thúc sau 1 lần
    */
    String repeatEveryStr = "";
    String overStr = "";
    String startStr = "";

    if (isDaily) {
      repeatEveryStr = interval == "1"
          ? LocaleKeys.calendar_repeat_display_daily_once.tr
          : LocaleKeys.calendar_repeat_display_daily.tr.format([interval]);
    } else if (isWeekly) {
      repeatEveryStr = _displayWeekly(startDate);
    } else if (isMonthly) {
      repeatEveryStr = _displayMonthly(startDate);
    } else if (isYearly) {
      repeatEveryStr = _displayYearly(startDate);
    }

    final dayStartStr =
        DateFormat(hideStartTime ? 'dd/MM/yyyy' : 'hh:mm a dd/MM/yyyy')
            .format(startDate);

    startStr = " ${LocaleKeys.task_datetimePicker_from.tr} $dayStartStr";
    // kết thúc
    if (hasUntil) {
      final String dayStr = DateFormat('dd/MM/yyyy').format(
          DateTimeUtils.toIsoDateTime(until ?? "").add(defaultAddedDuration));

      overStr =
          " ${LocaleKeys.calendar_repeat_display_end_at.tr.format([dayStr])}";
    } else if (hasCount) {
      overStr = " ${LocaleKeys.calendar_repeat_display_after.tr.format(
        [count!],
      )}";
    }

    return "$repeatEveryStr$startStr$overStr";
  }

  List<dynamic> getScheduleValue() {
    /*
      Hàng ngày/không lặp lại: [timeStamp]
      1 ngày trong tuần: [jsWeekday]
      các ngày trong tuần: [jsWeekdays]
      1 ngày trong tháng: [dayOfMonth]
      thứ `X` tuần thứ `Y` hàng tháng: [4SU]
    */
    if (isDaily) {
      return [startDate?.millisecondsSinceEpoch ?? 0];
    } else if (isWeekly) {
      return byDay!
          .split(",")
          .map((e) => e.toRepeatByWeekEnum().jsWeekDay)
          .toList();
    } else if (isMonthly) {
      if (hasByMonthDay) {
        // Lặp lại `X` tháng 1 lần vào ngày `Y`
        return [startDate?.day];
      } else if (bySetPos != "-1") {
        // Lặp lại `X` tháng 1 lần vào ngày `Y` lần thứ `Z`
        return ["${startDate?.weekOfMonth ?? 0}${startDate?.weekDayStr}"];
      } else {
        // Lặp lại `X` tháng 1 lần vào ngày `Y`  cuối cùng
        return ["-1${startDate?.weekDayStr}"];
      }
    } else if (isYearly) {
      if (hasByMonthDay) {
        // Lặp lại `X` năm 1 lần vào ngày `Y`
        return ["${startDate?.day}_${startDate?.month}"];
      } else {
        // Lặp lại `X` năm 1 lần vào ngày `Y` lần thứ `Z` của tháng `Z1`
        return [
          "${startDate?.weekOfMonth ?? 0}${startDate?.weekDayStr}${startDate?.month}"
        ];
      }
    }

    return [];
  }

  String _displayWeekly(DateTime startDate) {
    if (!hasByDay) {
      return LocaleKeys.calendar_details_repeat_label_weekly.tr;
    } else {
      // Lặp lại `X` tuần 1 lần vào các ngày `T2, T3,...`
      List weekDays = byDay!
          .split(",")
          .map((e) => e.toRepeatByWeekEnum().displayName)
          .toList();

      if (weekDays.length == 7) {
        if (interval == "1") {
          return LocaleKeys.calendar_details_repeat_label_weekly.tr;
        } else {
          // Lặp lại `X` tuần 1 lần
          if (Get.locale?.languageCode == "en") {
            return LocaleKeys.calendar_repeat_display_weekly_once.tr
                .format([interval == "1" ? " " : " $interval "]);
          } else {
            return LocaleKeys.calendar_repeat_display_weekly_once.tr
                .format([interval]);
          }
        }
      } else {
        String weekDayStr;
        bool isDefaultInterval = int.parse(interval) == 1;
        if (isWeekly && containsWeekday && isDefaultInterval) {
          return LocaleKeys.calendar_calendar_label_repeat_monday_to_friday.tr;
        } else if (weekDays.length == 1) {
          weekDayStr = DateFormat.EEEE(Get.locale.toString()).format(startDate);
        } else {
          weekDayStr = weekDays.join(", ");
        }

        if (interval == "1") {
          // Lặp lại hàng tuần vào ngày `X`
          return LocaleKeys.calendar_repeat_display_weekly_once_on_weekday.tr
              .format([weekDayStr]);
        } else {
          // Lặp lại `X` tháng 1 lần vào ngày `Y`
          if (Get.locale?.languageCode == "en") {
            return LocaleKeys.calendar_repeat_display_weekly.tr
                .format([interval == "1" ? " " : " $interval ", weekDayStr]);
          } else {
            return LocaleKeys.calendar_repeat_display_weekly.tr
                .format([interval, weekDayStr]);
          }
        }
      }
    }
  }

  String _displayMonthly(DateTime startDate) {
    if (hasByMonthDay) {
      final String dayStr = Get.locale?.languageCode == "en"
          ? "$byMonthDay${startDate.day.ordinalNumberSuffix}"
          : "$byMonthDay";

      if (interval == "1") {
        // lặp lại hàng tháng vào ngày `Y`
        return LocaleKeys.calendar_repeat_display_monthly_on_a_day_once.tr
            .format([dayStr]);
      } else {
        if (Get.locale?.languageCode == "en") {
          // Lặp lại `X` tháng 1 lần vào ngày `Y`
          interval = interval == "1" ? "$interval " : " $interval ";
        }

        return LocaleKeys.calendar_repeat_display_monthly_on_a_day.tr.format([
          interval,
          dayStr,
        ]);
      }
    } else if (bySetPos != "-1") {
      final String weekOrdinalStr =
          startDate.dayAppearInAMonth.ordinalNumberStr;
      final String weekDayStr =
          DateFormat.EEEE(Get.locale.toString()).format(startDate);

      if (interval == "1") {
        // lặp lại hàng tháng vào ngày `Y` lần thứ `Z`
        return LocaleKeys.calendar_repeat_display_monthly_on_a_weekday_once.tr
            .format(
          [
            weekDayStr,
            weekOrdinalStr,
          ],
        );
      } else {
        // Lặp lại `X` tháng 1 lần vào ngày `Y` lần thứ `Z`
        if (hasBySetPos) {
          interval = interval == "1" ? "$interval " : " $interval ";

          return LocaleKeys.calendar_repeat_display_monthly_on_a_weekday.tr
              .format(
            [
              interval,
              weekDayStr,
              weekOrdinalStr,
            ],
          );
        }

        return "";
      }
    } else {
      final String weekDayStr =
          DateFormat.EEEE(Get.locale.toString()).format(startDate);

      if (interval == "1") {
        // lặp lại hàng tháng vào ngày `Y` cuối cùng hàng tháng
        return LocaleKeys
            .calendar_repeat_display_monthly_on_last_weekday_once.tr
            .format(
          [
            weekDayStr,
          ],
        );
      } else {
        // Lặp lại `X` tháng 1 lần vào ngày `Y` cuối cùng hàng tháng
        if (hasBySetPos) {
          interval = interval == "1" ? "$interval " : " $interval ";

          return LocaleKeys
              .calendar_repeat_display_monthly_on_last_weekday_once.tr
              .format(
            [
              interval,
              weekDayStr,
            ],
          );
        }

        return "";
      }
    }
  }

  String _displayYearly(DateTime startDate) {
    final String dayStr = byMonthDay == null
        ? ""
        : DateFormat('d', Get.locale.toString())
            .format(DateTime(0, 0, int.tryParse(byMonthDay ?? "") ?? 1));
    final String monthStr = byMonth == null
        ? ""
        : DateFormat('MMMM', Get.locale.toString())
            .format(DateTime(0, int.tryParse(byMonth ?? "") ?? 1));

    final String suffix = Get.locale?.languageCode == "en"
        ? startDate.day.ordinalNumberSuffix
        : "";

    if (hasByMonthDay) {
      if (interval == "1") {
        // lặp lại hàng tháng vào ngày `Y`
        return LocaleKeys.calendar_repeat_display_yearly_on_a_day_once.tr
            .format([
          "$dayStr$suffix",
          monthStr,
        ]);
      } else {
        if (Get.locale?.languageCode == "en") {
          // Lặp lại `X` năm 1 lần vào ngày `Y`
          interval = interval == "1" ? "$interval " : " $interval ";
        }

        return LocaleKeys.calendar_repeat_display_yearly_on_a_day.tr.format([
          interval,
          dayStr,
          monthStr,
        ]);
      }
    } else {
      final String weekOrdinalStr =
          startDate.dayAppearInAMonth.ordinalNumberStr;
      final String weekdayStr =
          DateFormat.EEEE(Get.locale.toString()).format(startDate);
      if (interval == "1") {
        return LocaleKeys.task_datetimePicker_repeatEveryYear.tr;
        // lặp lại hàng tháng vào ngày `Y` lần thứ `Z`
        // return LocaleKeys.calendar_repeat_display_yearly_on_a_weekday_once.tr
        //     .format(
        //   [
        //     weekdayStr,
        //     weekOrdinalStr,
        //     monthStr,
        //   ],
        // );
      } else {
        // Lặp lại `X` tháng 1 lần vào ngày `Y` lần thứ `Z`
        if (hasBySetPos) {
          interval = interval == "1" ? "$interval " : " $interval ";

          return LocaleKeys.calendar_repeat_display_yearly_on_a_weekday.tr
              .format(
            [
              interval,
              weekdayStr,
              weekOrdinalStr,
              monthStr,
            ],
          );
        }

        return LocaleKeys.calendar_repeat_display_yearly.tr.format([interval]);

        // return "";
      }
    }
  }
}
