import 'package:gp_core/core.dart';

class FeatureFlag {
  static bool get isMomoEn {
    return Constants.baseUrl.startsWith("https://momo-api.gapowork.app");
  }

  static bool get enableSyncOutlook {
    if (isMomoEn) {
      return false;
    }
    return true;
  }

  static bool get enableSyncGoogle {
    // if (isMomoEn) {
    //   return false;
    // }
    return true;
  }

  static bool get enableFilterOutlook {
    if (isMomoEn) {
      return false;
    }
    return true;
  }

  /// Bộ lọc cho google event
  static bool get enableFilterGoogle {
    // if (isMomoEn) {
    //   return false;
    // }
    return true;
  }

  static const enableFilterEvent = true;

  static const bool enableDevicePreview = false;

  static bool enableHttpLog = false;

  static bool isFromNative = true;

  static bool enableCalendarAcceptDenyEvent = true;

  static bool enableCollabTaskShowAsAProject = true;

  // chú ý các nơi update lại variables
  static bool enableSecureDownload = true;
  static bool enableSecureDownloadImage = true;
  static bool enableSecureDownloadVideo = true;
  static bool enableSecureDownloadFile = true;

  // chú ý các nơi update lại variables
  static bool enableSecureUpload = true;

  static final List<String> whiteListDownloadAuthDomains = [
    'dl-f.gapowork.vn',
    'dl-i.gapowork.vn',
    'dl-v.gapowork.vn',
    // stg - new
    'dl-f-stg.gapowork.vn',
    'dl-i-stg.gapowork.vn',
    'dl-v-stg.gapowork.vn',
    // stg - old
    'dl-f.stg.gapowork.vn',
    'dl-i.stg.gapowork.vn',
    'dl-v.stg.gapowork.vn',
    // uat - new
    'dl-f-uat.gapowork.vn',
    'dl-i-uat.gapowork.vn',
    'dl-v-uat.gapowork.vn',
    // uat - old
    'dl-f.uat.gapowork.vn',
    'dl-i.uat.gapowork.vn',
    'dl-v.uat.gapowork.vn',

    // ---------- 13/01/2025 secured domain ---------- \
    // staging
    'dl-sa-stg.gapowork.vn',
    'dl-sf-stg.gapowork.vn',
    'dl-si-stg.gapowork.vn',
    'dl-sv-stg.gapowork.vn',
    'dl-smp4v-stg.gapowork.vn',
    'dl-sthumb-i-stg-3.gapowork.vn',
    'dl-sthumb-v-stg-3.gapowork.vn',
    'dl-sthumb-mp4v-stg-3.gapowork.vn',
    // uat
    'dl-sa-uat.gapowork.vn',
    'dl-sf-uat.gapowork.vn',
    'dl-si-uat.gapowork.vn',
    'dl-sv-uat.gapowork.vn',
    'dl-smp4v-uat.gapowork.vn',
    'dl-sthumb-i-uat-3.gapowork.vn',
    'dl-sthumb-v-uat-3.gapowork.vn',
    'dl-sthumb-mp4v-uat-3.gapowork.vn',
    // production
    'dl-sa.gapowork.vn',
    'dl-sf.gapowork.vn',
    'dl-si.gapowork.vn',
    'dl-sv.gapowork.vn',
    'dl-smp4v.gapowork.vn',
    'dl-sthumb-i-3.gapowork.vn',
    'dl-sthumb-v-3.gapowork.vn',
    'dl-sthumb-mp4v-3.gapowork.vn',
    // ToanNM check staging thấy đang bị thiếu
    'dl-sthumb-i.gapowork.vn',
    'dl-sthumb-v.gapowork.vn',
    'dl-sthumb-i-stg.gapowork.vn',
    'dl-sthumb-v-stg.gapowork.vn',
    'dl-sthumb-i-uat.gapowork.vn',
    'dl-sthumb-v-uat.gapowork.vn',
  ];

  static final unsecureRequestDio = Dio();
  static final downloadDio = GPDio.instance.setup(addLogger: false); //Dio();
  static final googleDocs = ["pdf", "txt"];
  static final officeDocs = ["xlsx", "xls", "doc", "docx", "ppt", "pptx"];
}
