#!/bin/bash

# <PERSON><PERSON><PERSON> to remove macro comments and template directives that might be causing dart_eval compilation issues

echo "Removing macro comments and template directives from Dart files..."

# Remove macro comments from assets.dart
find . -name "assets.dart" -type f -exec sed -i '' -E 's|/// \{@macro[^}]*\}||g' {} +

# Remove macro comments from all .dart files
find . -name "*.dart" -type f -exec sed -i '' -E 's|/// \{@macro[^}]*\}||g' {} +

# Remove macro comments from auto_mappr files
find . -name "*.auto_mappr.dart" -type f -exec sed -i '' -E 's|/// \{@macro[^}]*\}||g' {} +

# Remove mapper comments from mapper files
find . -name "*_mapper.dart" -type f -exec sed -i '' -E 's|/// \{@macro[^}]*\}||g' {} +

# Remove YouTube doc directives (these don't have closing tags)
find . -name "*.dart" -type f -exec sed -i '' -E 's|/// \{@youtube[^}]*\}||g' {} +

# Remove any other doc directives that might be unclosed
find . -name "*.dart" -type f -exec sed -i '' -E 's|/// \{@[a-zA-Z][^}]*\}||g' {} +

# Remove any unclosed doc directives (without closing brace)
find . -name "*.dart" -type f -exec sed -i '' -E 's|/// \{@[a-zA-Z][^}]*$||g' {} +

# Remove template directives using grep and temporary files
for file in $(find . -name "*.dart" -type f); do
    if grep -q "/// {@template" "$file"; then
        grep -v "/// {@template" "$file" | grep -v "/// {@endtemplate}" > "$file.tmp" && mv "$file.tmp" "$file"
    fi
done

echo "Macro comments and template directives removed successfully!"
