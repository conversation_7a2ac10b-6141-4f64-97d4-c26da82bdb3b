#!/usr/bin/env python3

import os
import re

def find_doc_directive_issues():
    """Find files with unclosed doc directive tags"""
    
    # Patterns for doc directives
    patterns = [
        r'\{@template\s+[^}]*$',  # Unclosed template
        r'\{@macro\s+[^}]*$',    # Unclosed macro
        r'\{@tool\s+[^}]*$',     # Unclosed tool
        r'\{@inject-html\s+[^}]*$',  # Unclosed inject-html
        r'\{@[^}]*$',            # Any unclosed doc directive
    ]
    
    issues = []
    
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.dart'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    # Check for unclosed doc directives
                    lines = content.split('\n')
                    for i, line in enumerate(lines):
                        for pattern in patterns:
                            if re.search(pattern, line):
                                issues.append({
                                    'file': file_path,
                                    'line': i + 1,
                                    'content': line.strip(),
                                    'char_pos': len('\n'.join(lines[:i])) + len(line)
                                })
                                
                    # Check for character positions around 9047-9102
                    if 9000 <= len(content) <= 10000:
                        start_pos = max(0, 9047 - 50)
                        end_pos = min(len(content), 9102 + 50)
                        snippet = content[start_pos:end_pos]
                        if '{@' in snippet and '}' not in snippet[snippet.find('{@'):]:
                            issues.append({
                                'file': file_path,
                                'line': 'around char 9047-9102',
                                'content': snippet,
                                'char_pos': 9047
                            })
                            
                except Exception as e:
                    print(f"Error reading {file_path}: {e}")
    
    return issues

if __name__ == "__main__":
    issues = find_doc_directive_issues()
    
    if issues:
        print("Found potential doc directive issues:")
        for issue in issues:
            print(f"File: {issue['file']}")
            print(f"Line: {issue['line']}")
            print(f"Content: {issue['content']}")
            print(f"Char position: {issue['char_pos']}")
            print("-" * 50)
    else:
        print("No doc directive issues found.")
