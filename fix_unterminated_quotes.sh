#!/bin/bash

# <PERSON><PERSON><PERSON> to fix unterminated quotes in documentation comments that might be causing dart_eval compilation issues

echo "Fixing unterminated quotes in documentation comments..."

# Find files with unterminated quotes in comments and fix them
find . -name "*.dart" -type f -exec sh -c '
    file="$1"
    temp_file="${file}.tmp"
    
    # Process the file line by line
    while IFS= read -r line; do
        # Check if line is a comment with unterminated quote
        if [[ "$line" =~ ^[[:space:]]*///.*\"[^\"]*$ ]]; then
            # Add closing quote to the line
            echo "${line}\""
        else
            echo "$line"
        fi
    done < "$file" > "$temp_file"
    
    # Replace original file if changes were made
    if ! cmp -s "$file" "$temp_file"; then
        mv "$temp_file" "$file"
        echo "Fixed: $file"
    else
        rm "$temp_file"
    fi
' _ {} \;

echo "Unterminated quotes in documentation comments fixed successfully!"
