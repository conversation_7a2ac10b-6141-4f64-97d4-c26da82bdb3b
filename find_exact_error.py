#!/usr/bin/env python3

import os
import re

def find_exact_error():
    """Find the exact file and location causing the doc directive error"""
    
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.dart'):
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # Check if file size is around the error position
                    if len(content) >= 9047:
                        # Check content around position 9047-9102
                        start_pos = max(0, 9047 - 100)
                        end_pos = min(len(content), 9102 + 100)
                        snippet = content[start_pos:end_pos]
                        
                        # Look for unclosed doc directives
                        if '{@' in snippet:
                            print(f"=== {file_path} ===")
                            print(f"File size: {len(content)} chars")
                            print(f"Content around position 9047-9102:")
                            print(repr(snippet))
                            print("-" * 50)
                            
                            # Check for specific patterns
                            lines = content.split('\n')
                            char_count = 0
                            for i, line in enumerate(lines):
                                if char_count <= 9047 <= char_count + len(line):
                                    print(f"Line {i+1}: {line}")
                                    break
                                char_count += len(line) + 1  # +1 for newline
                            
                            print("=" * 50)
                            
                except Exception as e:
                    print(f"Error reading {file_path}: {e}")

if __name__ == "__main__":
    find_exact_error()
